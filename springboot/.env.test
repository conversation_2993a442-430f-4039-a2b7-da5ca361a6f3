# Test Environment Variables
# This file contains test-specific environment variables

# Application Profile
SPRING_PROFILES_ACTIVE=test

# Server Configuration
PORT=8080

# Database Configuration - Test PostgreSQL
DATABASE_URL=**********************************************
DATABASE_USERNAME=animalia_test_user
DATABASE_PASSWORD=test_password_456

# Database Connection Pool Settings
DB_POOL_SIZE=8
DB_POOL_MIN_IDLE=2
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000
DB_LEAK_DETECTION=60000

# JPA/Hibernate Configuration
SPRING_JPA_HIBERNATE_DDL_AUTO=create-drop
SPRING_JPA_PROPERTIES_HIBERNATE_HBM2DDL_AUTO=create-drop
SPRING_JPA_SHOW_SQL=false
SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL=false

# Security Configuration
JWT_SECRET=testSecretKeyForJwtTokenGenerationAndValidationWithExtraLengthToMeetHS512RequirementsInTestEnvironment
JWT_EXPIRATION=86400000

# Firebase Configuration
FIREBASE_PROJECT_ID=animalia-de0f1
FIREBASE_DATABASE_URL=https://animalia-grooming-default-rtdb.europe-west1.firebasedatabase.app
FIREBASE_STORAGE_BUCKET=animalia-grooming.appspot.com

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081,http://localhost:4200,https://test.animalia-app.com

# Cloudinary Configuration (Test)
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=

# Email Configuration (Test - can use real SMTP)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=

# Notification Configuration
NOTIFICATIONS_SMS_ENABLED=true
NOTIFICATIONS_PUSH_ENABLED=true
NOTIFICATIONS_ASYNC_ENABLED=true

# Phone Validation
PHONE_VALIDATION_COUNTRY_CODE=+40
PHONE_VALIDATION_MIN_LENGTH=10
PHONE_VALIDATION_MAX_LENGTH=10

# Test Flags
SPRING_DEVTOOLS_RESTART_ENABLED=false
SPRING_DEVTOOLS_LIVERELOAD_ENABLED=false
SPRING_DATA_REST_ENABLED=false

# Logging Configuration
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB=INFO
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_SECURITY=INFO
LOGGING_LEVEL_ORG_HIBERNATE_SQL=INFO
LOGGING_LEVEL_RO_ANIMALIAPROGRAMARI=INFO

# Actuator Configuration
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics,prometheus
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when-authorized

# Test Data Configuration
TEST_DATA_ENABLED=true
TEST_DATA_CREATE_SAMPLE_DATA=true
