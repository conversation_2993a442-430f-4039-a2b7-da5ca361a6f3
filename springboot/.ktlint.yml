disabled_rules:
  - no-wildcard-imports
  - filename

ktlint_code_style: ktlint_official

# Allow wildcard imports for certain packages
ij_kotlin_allow_trailing_comma: true
ij_kotlin_allow_trailing_comma_on_call_site: true

# Increase max line length
max_line_length: 120

# Allow multiple spaces in some cases
indent_size: 4

# Custom rules configuration
standard:
  multiline-expression-wrapping: disabled
  trailing-comma-on-call-site: disabled
  trailing-comma-on-declaration-site: disabled
  chain-method-continuation: disabled
  argument-list-wrapping: disabled
  function-signature: disabled
  no-trailing-spaces: enabled
  no-blank-line-in-list: disabled
  function-expression-body: disabled
  blank-line-before-declaration: disabled
  no-empty-first-line-in-class-body: disabled
  no-wildcard-imports: disabled
  wrapping: disabled
  indent: enabled
  no-blank-line-before-rbrace: disabled
  spacing-between-declarations-with-comments: disabled
  class-signature: disabled
  import-ordering: enabled
  parameter-list-wrapping: disabled
  comma-spacing: enabled
  max-line-length: enabled
  no-unused-imports: enabled
  final-newline: enabled
  comment-spacing: enabled
  string-template: enabled
  if-else-wrapping: disabled
  no-consecutive-blank-lines: enabled
  string-template-indent: disabled
  function-literal: disabled
  no-multi-spaces: enabled
  multiline-if-else: disabled
  enum-wrapping: disabled
  binary-expression-wrapping: disabled
  condition-wrapping: disabled
  if-else-bracing: enabled
  no-empty-first-line-in-method-block: disabled
  filename: disabled
  no-semi: enabled
  parameter-list-spacing: enabled
  value-parameter-comment: disabled
  no-blank-lines-in-chained-method-calls: disabled
  chain-wrapping: disabled
  statement-wrapping: disabled
  no-consecutive-comments: disabled
  dot-spacing: enabled
  no-empty-file: enabled
  spacing-between-declarations-with-annotations: disabled
  parameter-wrapping: disabled
  paren-spacing: enabled
  property-wrapping: disabled
