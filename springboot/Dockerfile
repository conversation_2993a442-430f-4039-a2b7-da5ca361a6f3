# Build stage
FROM --platform=$BUILDPLATFORM eclipse-temurin:21-jdk AS builder

WORKDIR /app

# Copy gradle files first for better layer caching
COPY gradle/ gradle/
COPY gradlew build.gradle.kts settings.gradle.kts ./
RUN chmod +x ./gradlew

# Download dependencies (with parallel and cache optimizations)
RUN ./gradlew dependencies --no-daemon --parallel --build-cache

# Copy source code
COPY src/ src/

# Build the application with production profile (optimized)
RUN ./gradlew bootJar --no-daemon --parallel --build-cache -Pprofile=prod -x test

# Run stage
FROM --platform=$BUILDPLATFORM eclipse-temurin:21-jre-jammy

WORKDIR /app

# Install curl for health checks (before switching to non-root user)
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Add a non-root user
RUN addgroup --system --gid 1001 appuser && \
    adduser --system --uid 1001 --group appuser

# Copy the jar from builder stage
COPY --from=builder --chown=appuser:appuser /app/build/libs/*.jar app.jar

# Set environment variables
ENV SPRING_PROFILES_ACTIVE=prod \
    POSTGRES_URL=**************************************************************************************** \
    POSTGRES_USER=animalia_lbzo_user \
    POSTGRES_PASSWORD=U3Clr9xn3TbnhcNHIeuRzoXvIn4CWbGd

# Switch to non-root user
USER appuser

# Expose the application port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/api/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", \
    "-XX:+UseContainerSupport", \
    "-XX:MaxRAMPercentage=75.0", \
    "-Djava.security.egd=file:/dev/./urandom", \
    "-jar", \
    "app.jar" \
]