-- Create salon subscriptions table
CREATE TABLE salon_subscriptions (
    id VARCHAR(36) PRIMARY KEY,
    salon_id VARCHAR(36) NOT NULL,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('FREELANCER', 'TEAM', 'ENTERPRISE')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('ACTIVE', 'CANCELLED', 'SUSPENDED', 'EXPIRED', 'TRIAL')),
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT true,
    auto_renew BOOLEAN NOT NULL DEFAULT true,
    monthly_price DECIMAL(10,2) NOT NULL CHECK (monthly_price >= 0),
    billing_cycle VARCHAR(20) NOT NULL DEFAULT 'MONTHLY' CHECK (billing_cycle IN ('MONTHLY', 'QUARTERLY', 'YEARLY')),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_billing_date TIMESTAMP,
    next_billing_date TIMESTAMP,
    trial_end_date TIMESTAMP,
    cancelled_at TIMESTAMP,
    cancellation_reason VARCHAR(500)
);

-- Create indexes for performance
CREATE INDEX idx_salon_subscription_salon_id ON salon_subscriptions(salon_id);
CREATE INDEX idx_salon_subscription_status ON salon_subscriptions(status);
CREATE INDEX idx_salon_subscription_tier ON salon_subscriptions(tier);
CREATE INDEX idx_salon_subscription_active ON salon_subscriptions(is_active);
CREATE INDEX idx_salon_subscription_next_billing ON salon_subscriptions(next_billing_date);

-- Create unique index to ensure only one active subscription per salon
CREATE UNIQUE INDEX idx_salon_subscription_active_unique 
ON salon_subscriptions(salon_id) 
WHERE is_active = true AND status = 'ACTIVE';

-- Add foreign key constraint to salons table (if it exists)
-- ALTER TABLE salon_subscriptions 
-- ADD CONSTRAINT fk_salon_subscription_salon 
-- FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE;

-- Insert default subscriptions for existing salons (if any)
-- This will give all existing salons a Freelancer subscription
INSERT INTO salon_subscriptions (
    id,
    salon_id,
    tier,
    status,
    start_date,
    monthly_price,
    billing_cycle,
    next_billing_date
)
SELECT 
    gen_random_uuid()::text,
    id,
    'FREELANCER',
    'ACTIVE',
    CURRENT_TIMESTAMP,
    0.00,
    'MONTHLY',
    CURRENT_TIMESTAMP + INTERVAL '1 month'
FROM salons 
WHERE NOT EXISTS (
    SELECT 1 FROM salon_subscriptions ss WHERE ss.salon_id = salons.id
);

-- Add comment to table
COMMENT ON TABLE salon_subscriptions IS 'Stores salon subscription information including tier, billing, and status';
COMMENT ON COLUMN salon_subscriptions.tier IS 'Subscription tier: FREELANCER (50 SMS), TEAM (250 SMS), ENTERPRISE (1000 SMS)';
COMMENT ON COLUMN salon_subscriptions.status IS 'Current subscription status';
COMMENT ON COLUMN salon_subscriptions.monthly_price IS 'Monthly subscription price in the local currency';
COMMENT ON COLUMN salon_subscriptions.billing_cycle IS 'How often the subscription is billed';
COMMENT ON COLUMN salon_subscriptions.trial_end_date IS 'End date of trial period if applicable';
COMMENT ON COLUMN salon_subscriptions.cancellation_reason IS 'Reason for cancellation if status is CANCELLED';
