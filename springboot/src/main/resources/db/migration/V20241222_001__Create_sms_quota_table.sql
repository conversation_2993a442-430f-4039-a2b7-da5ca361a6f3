-- Create SMS quota table for tracking SMS usage per salon
CREATE TABLE sms_quota (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    salon_id VARCHAR(36) NOT NULL UNIQUE,
    total_quota INTEGER NOT NULL DEFAULT 30,
    used_quota INTEGER NOT NULL DEFAULT 0,
    reset_date TIMESTAMP NOT NULL,
    quota_period VARCHAR(20) NOT NULL DEFAULT 'MONTHLY',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_total_quota_non_negative CHECK (total_quota >= 0),
    CONSTRAINT chk_used_quota_non_negative CHECK (used_quota >= 0),
    CONSTRAINT chk_quota_period_valid CHECK (quota_period IN ('DAILY', 'WEEKLY', 'MONTHLY'))
);

-- Create indexes for performance
CREATE INDEX idx_sms_quota_salon_id ON sms_quota(salon_id);
CREATE INDEX idx_sms_quota_reset_date ON sms_quota(reset_date);

-- Add comment to table
COMMENT ON TABLE sms_quota IS 'SMS quota management per salon';
COMMENT ON COLUMN sms_quota.id IS 'Unique identifier for SMS quota record';
COMMENT ON COLUMN sms_quota.salon_id IS 'Salon identifier (foreign key)';
COMMENT ON COLUMN sms_quota.total_quota IS 'Total SMS quota allowed per period';
COMMENT ON COLUMN sms_quota.used_quota IS 'Number of SMS used in current period';
COMMENT ON COLUMN sms_quota.reset_date IS 'Date when quota period resets';
COMMENT ON COLUMN sms_quota.quota_period IS 'Quota reset period (DAILY, WEEKLY, MONTHLY)';
COMMENT ON COLUMN sms_quota.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN sms_quota.updated_at IS 'Record last update timestamp';
