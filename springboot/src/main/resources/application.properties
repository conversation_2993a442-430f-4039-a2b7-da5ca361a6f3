# Default profile configuration - Development settings
spring.application.name=animalia-programari-backend

# Server configuration
server.port=${PORT:8081}
server.servlet.context-path=/api
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# Database configuration - Use environment variables for production
spring.datasource.url=${DATABASE_URL:****************************************************************************************}
spring.datasource.username=animalia_lbzo_user
spring.datasource.password=U3Clr9xn3TbnhcNHIeuRzoXvIn4CWbGd
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection pool configuration for production
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:10}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:2}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1800000}
spring.datasource.hikari.leak-detection-threshold=${DB_LEAK_DETECTION:60000}

# JPA/Hibernate configuration - Using create-drop for development to fix schema issues
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
# Additional Hibernate properties to handle schema issues
spring.jpa.properties.hibernate.hbm2ddl.auto=update
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.properties.hibernate.globally_quoted_identifiers_skip_column_definitions=true

# Flyway configuration - DISABLED for development
spring.flyway.enabled=false
# spring.flyway.baseline-on-migrate=true
# spring.flyway.locations=classpath:db/migration
# spring.flyway.validate-on-migrate=true

# Logging configuration
logging.level.org.springframework=${SPRING_LOG_LEVEL:INFO}
logging.level.ro.animaliaprogramari.animalia=${APP_LOG_LEVEL:INFO}
# Disable Hibernate SQL logs
logging.level.org.hibernate.SQL=OFF
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=OFF
logging.level.org.hibernate.type=OFF
logging.level.org.hibernate.stat=OFF
logging.level.org.hibernate.engine=OFF
# Enhanced logging for JWT debugging
logging.level.ro.animaliaprogramari.animalia.adapter.outbound.auth=INFO
logging.level.ro.animaliaprogramari.animalia.application.usecase=INFO
logging.level.ro.animaliaprogramari.animalia.adapter.inbound.security=INFO
logging.level.ro.animaliaprogramari.animalia.adapter.inbound.rest=INFO
logging.level.ro.animaliaprogramari.animalia.config=INFO

# Conflict Detection Debug Logging - Enable for troubleshooting appointment scheduling conflicts
# Uncomment the lines below to enable detailed debug logging for conflict detection
logging.level.ro.animaliaprogramari.animalia.domain.service.SchedulingConflictService=DEBUG
logging.level.ro.animaliaprogramari.animalia.domain.service.BlockTimeSchedulingService=DEBUG
logging.level.ro.animaliaprogramari.animalia.application.usecase.AppointmentManagementUseCaseImpl=DEBUG
logging.level.ro.animaliaprogramari.animalia.adapter.inbound.rest.SalonAppointmentController=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Security configuration
jwt.secret=${JWT_SECRET:animaliaGroomingSecretKey2024ForJwtTokenGenerationAndValidationWithExtraLengthToMeetHS512Requirements}
jwt.expiration=${JWT_EXPIRATION:86400000}

# Firebase configuration
firebase.project-id=${FIREBASE_PROJECT_ID:animalia-de0f1}
firebase.database-url=${FIREBASE_DATABASE_URL:https://animalia-grooming-default-rtdb.europe-west1.firebasedatabase.app}
firebase.storage-bucket=${FIREBASE_STORAGE_BUCKET:animalia-grooming.appspot.com}

# Disable Docker Compose
spring.docker.compose.enabled=false

# Disable Spring Data REST to prevent conflicts with custom controllers
spring.data.rest.enabled=false

# Enable configuration properties
spring.config.import=optional:configtree:/etc/config/

# Business Rules Configuration
animalia.business-rules.validation.max-name-length=${MAX_NAME_LENGTH:255}
animalia.business-rules.validation.max-notes-length=${MAX_NOTES_LENGTH:2000}
animalia.business-rules.validation.max-description-length=${MAX_DESCRIPTION_LENGTH:1000}
animalia.business-rules.validation.max-address-length=${MAX_ADDRESS_LENGTH:1000}
animalia.business-rules.validation.max-city-length=${MAX_CITY_LENGTH:255}
animalia.business-rules.validation.max-breed-length=${MAX_BREED_LENGTH:255}
animalia.business-rules.validation.max-color-length=${MAX_COLOR_LENGTH:100}
animalia.business-rules.validation.max-medical-conditions-length=${MAX_MEDICAL_CONDITIONS_LENGTH:2000}

animalia.business-rules.appointment.minimum-advance-hours=${MIN_ADVANCE_HOURS:2}
animalia.business-rules.appointment.maximum-advance-days=${MAX_ADVANCE_DAYS:90}
animalia.business-rules.appointment.default-duration-minutes=${DEFAULT_DURATION_MINUTES:60}
animalia.business-rules.appointment.max-services-per-appointment=${MAX_SERVICES_PER_APPOINTMENT:10}
animalia.business-rules.appointment.min-duration-minutes=${MIN_DURATION_MINUTES:15}
animalia.business-rules.appointment.max-duration-minutes=${MAX_DURATION_MINUTES:480}

animalia.business-rules.salon.max-staff-members=${MAX_STAFF_MEMBERS:20}
animalia.business-rules.salon.max-services-per-salon=${MAX_SERVICES_PER_SALON:100}
animalia.business-rules.salon.max-clients-per-salon=${MAX_CLIENTS_PER_SALON:1000}

# Actuator configuration for production monitoring
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=${HEALTH_SHOW_DETAILS:when_authorized}
management.endpoint.health.roles=ADMIN
management.endpoints.web.base-path=/actuator
management.prometheus.metrics.export.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.sla.http.server.requests=50ms,100ms,200ms,500ms,1s
management.metrics.tags.application=animalia-programari-backend
management.metrics.tags.environment=${SPRING_PROFILES_ACTIVE:default}

# SMSO SMS Configuration
smso.api.key=${SMSO_API_KEY:uT4FMktcZ3Dk9bON9tGfn7pe3Pg8WtSHgmAAW7B6}
smso.api.url=${SMSO_API_URL:https://app.smso.ro/api/v1/send}
smso.api.timeout=${SMSO_API_TIMEOUT:30000}
smso.api.retry-attempts=${SMSO_RETRY_ATTEMPTS:3}

# FCM Configuration
fcm.enabled=${FCM_ENABLED:true}
fcm.service-account-key-path=${FCM_SERVICE_ACCOUNT_KEY_PATH:}

# Notification Configuration
notifications.sms.enabled=${SMS_NOTIFICATIONS_ENABLED:true}
notifications.sms.rate-limit-per-hour=${SMS_RATE_LIMIT_PER_HOUR:3}
notifications.sms.rate-limit-window-minutes=${SMS_RATE_LIMIT_WINDOW_MINUTES:60}
notifications.push.enabled=${PUSH_NOTIFICATIONS_ENABLED:true}
notifications.async.enabled=${ASYNC_NOTIFICATIONS_ENABLED:true}
notifications.async.thread-pool-size=${NOTIFICATION_THREAD_POOL_SIZE:5}

# Romanian Phone Number Configuration
phone.validation.country-code=${PHONE_COUNTRY_CODE:+40}
phone.validation.min-length=${PHONE_MIN_LENGTH:10}
phone.validation.max-length=${PHONE_MAX_LENGTH:10}

# Cloudinary configuration
cloudinary.cloud-name=${CLOUDINARY_CLOUD_NAME:datjgpnel}
cloudinary.api-key=${CLOUDINARY_API_KEY:***************}
cloudinary.api-secret=${CLOUDINARY_API_SECRET:g6BReXQ1_jxqJ-whjWMGN_CATT0}

# Make mail health check optional
management.health.mail.enabled=false
