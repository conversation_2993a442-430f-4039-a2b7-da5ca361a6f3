# Production profile configuration
# This file contains production-specific overrides

# Server configuration for production
server.port=${PORT:8080}
server.servlet.context-path=/api
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024
server.http2.enabled=true
server.forward-headers-strategy=framework

# Production database configuration
spring.datasource.url=${DATABASE_URL}
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}
spring.datasource.driver-class-name=org.postgresql.Driver

# Optimized connection pool for production
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:15}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:5}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:20000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:300000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1200000}
spring.datasource.hikari.leak-detection-threshold=${DB_LEAK_DETECTION:60000}
spring.datasource.hikari.pool-name=AnimaliaProdPool

# JPA/Hibernate production optimizations - Let Hibernate manage schema
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.properties.hibernate.generate_statistics=false
# Force clean schema validation and prevent caching issues
spring.jpa.properties.hibernate.hbm2ddl.auto=update

# Flyway DISABLED - Using Hibernate for schema management
spring.flyway.enabled=false

# Production logging configuration
logging.level.org.springframework=${SPRING_LOG_LEVEL:INFO}
logging.level.ro.animaliaprogramari.animalia=${APP_LOG_LEVEL:INFO}
logging.level.org.hibernate.SQL=${HIBERNATE_SQL_LOG_LEVEL:WARN}
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=${HIBERNATE_BINDER_LOG_LEVEL:WARN}
logging.level.org.springframework.security=${SECURITY_LOG_LEVEL:INFO}
logging.level.com.google.firebase=${FIREBASE_LOG_LEVEL:INFO}
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n

# Security configuration for production
jwt.secret=${JWT_SECRET}
jwt.expiration=${JWT_EXPIRATION:86400000}

# Firebase configuration for production
firebase.project-id=${FIREBASE_PROJECT_ID}
firebase.database-url=${FIREBASE_DATABASE_URL}
firebase.storage-bucket=${FIREBASE_STORAGE_BUCKET}

# Production actuator configuration
management.endpoints.web.exposure.include=health,info,prometheus,metrics
management.endpoint.health.show-details=never
management.endpoint.health.roles=ADMIN
management.endpoints.web.base-path=/actuator
management.prometheus.metrics.export.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.sla.http.server.requests=50ms,100ms,200ms,500ms,1s,2s
management.metrics.tags.application=animalia-programari-backend
management.metrics.tags.environment=production
management.metrics.tags.version=${app.version:unknown}

# Health check configuration
management.endpoint.health.probes.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Production security headers
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.same-site=strict

# Disable unnecessary features in production
spring.docker.compose.enabled=false
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# Performance tuning
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Error handling
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never
server.error.include-exception=false

# SMSO SMS Configuration for Production
smso.api.key=${SMSO_API_KEY}
smso.api.url=${SMSO_API_URL:https://api.smso.ro/v1}
smso.api.timeout=${SMSO_API_TIMEOUT:20000}
smso.api.retry-attempts=${SMSO_RETRY_ATTEMPTS:2}

# FCM Configuration for Production
fcm.enabled=${FCM_ENABLED:true}
fcm.service-account-key-path=${FCM_SERVICE_ACCOUNT_KEY_PATH}

# Notification Configuration for Production
notifications.sms.enabled=${SMS_NOTIFICATIONS_ENABLED:true}
notifications.sms.rate-limit-per-hour=${SMS_RATE_LIMIT_PER_HOUR:3}
notifications.sms.rate-limit-window-minutes=${SMS_RATE_LIMIT_WINDOW_MINUTES:60}
notifications.push.enabled=${PUSH_NOTIFICATIONS_ENABLED:true}
notifications.async.enabled=${ASYNC_NOTIFICATIONS_ENABLED:true}
notifications.async.thread-pool-size=${NOTIFICATION_THREAD_POOL_SIZE:10}

# Romanian Phone Number Configuration
phone.validation.country-code=${PHONE_COUNTRY_CODE:+40}
phone.validation.min-length=${PHONE_MIN_LENGTH:10}
phone.validation.max-length=${PHONE_MAX_LENGTH:10}

# Cloudinary configuration
cloudinary.cloud-name=${CLOUDINARY_CLOUD_NAME}
cloudinary.api-key=${CLOUDINARY_API_KEY}
cloudinary.api-secret=${CLOUDINARY_API_SECRET}
