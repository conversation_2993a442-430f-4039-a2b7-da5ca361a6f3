package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.AuthenticateWithFirebaseCommand
import ro.animaliaprogramari.animalia.application.command.RefreshTokenCommand
import ro.animaliaprogramari.animalia.application.query.GetUserByFirebaseUidQuery
import ro.animaliaprogramari.animalia.application.query.GetUserByIdQuery
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.JwtToken
import ro.animaliaprogramari.animalia.domain.model.User

/**
 * Inbound port for authentication use cases
 * Defines what the application can do regarding authentication
 */
interface AuthenticationUseCase {
    /**
     * Authenticate user with Firebase ID token
     * Returns JWT token for subsequent API calls
     */
    fun authenticateWithFirebase(command: AuthenticateWithFirebaseCommand): AuthenticationResult

    /**
     * Refresh JWT token
     */
    fun refreshToken(command: RefreshTokenCommand): AuthenticationResult

    /**
     * Get current authenticated user context
     */
    fun getCurrentUser(query: GetUserByIdQuery): AuthenticatedUser?

    /**
     * Get user by Firebase UID
     */
    fun getUserByFirebaseUid(query: GetUserByFirebaseUidQuery): User?

    /**
     * Validate JWT token and return user context
     */
    fun validateToken(token: String): AuthenticatedUser?
}

/**
 * Result of authentication operation
 */
data class AuthenticationResult(
    val success: Boolean,
    val user: AuthenticatedUser?,
    val jwtToken: JwtToken?,
    val errorMessage: String? = null,
) {
    companion object {
        fun success(
            user: AuthenticatedUser,
            jwtToken: JwtToken,
        ): AuthenticationResult {
            return AuthenticationResult(
                success = true,
                user = user,
                jwtToken = jwtToken,
            )
        }

        fun failure(errorMessage: String): AuthenticationResult {
            return AuthenticationResult(
                success = false,
                user = null,
                jwtToken = null,
                errorMessage = errorMessage,
            )
        }
    }
}
