package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.time.LocalDateTime

/**
 * Command for creating a new notification
 */
data class CreateNotificationCommand(
    val salonId: SalonId,
    val title: String,
    val message: String,
    val type: String,
    val appointmentId: String? = null,
    val clientId: String? = null,
    val metadata: String? = null,
    val timestamp: LocalDateTime = LocalDateTime.now()
) {
    init {
        require(title.isNotBlank()) { "Notification title cannot be blank" }
        require(message.isNotBlank()) { "Notification message cannot be blank" }
        require(type.isNotBlank()) { "Notification type cannot be blank" }
        require(title.length <= 255) { "Notification title cannot exceed 255 characters" }
    }
}

/**
 * Command for marking notifications as read
 */
data class MarkNotificationsAsReadCommand(
    val notificationIds: List<Long>
) {
    init {
        require(notificationIds.isNotEmpty()) { "Notification IDs list cannot be empty" }
    }
}
