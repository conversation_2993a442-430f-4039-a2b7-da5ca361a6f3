package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.*
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.BlockTimeSchedulingService
import ro.animaliaprogramari.animalia.domain.service.SchedulingConflictService
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.temporal.ChronoUnit

/**
 * Implementation of block time management use case
 * Handles all block time business logic and orchestrates domain services
 */
@Service
@Transactional
class BlockTimeManagementUseCaseImpl(
    private val blockTimeRepository: BlockTimeRepository,
    private val appointmentRepository: AppointmentRepository,
    private val staffRepository: StaffRepository,
    private val userRepository: UserRepository,
    private val salonRepository: SalonRepository,
    private val workingHoursRepository: WorkingHoursRepository,
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val blockTimeSchedulingService: BlockTimeSchedulingService,
    private val schedulingConflictService: SchedulingConflictService,
) : BlockTimeManagementUseCase {
    private val logger = LoggerFactory.getLogger(BlockTimeManagementUseCaseImpl::class.java)

    override fun createBlockTime(command: CreateBlockTimeCommand): BlockTimeResult {
        logger.debug("Creating block time for salon: ${command.salonId.value}")

        // Validate permissions
        validatePermissions(command.createdBy, command.salonId, command.staffIds)

        // Validate salon exists
        salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate staff members exist and belong to salon
        validateStaffMembers(command.staffIds, command.salonId)

        // Check for conflicts
        val existingBlocks =
            blockTimeRepository.findOverlappingBlocks(
                command.salonId,
                command.startTime,
                command.endTime,
                command.staffIds,
            )
        val existingAppointments =
            appointmentRepository.findBySalonIdAndStaffIdsAndTimeRange(
                command.salonId,
                command.staffIds,
                command.startTime,
                command.endTime,
            )

        val salonHours = workingHoursRepository.findBySalonId(command.salonId)

        // Only check staff working hours conflicts if allowConflicts is false
        if (!command.allowConflicts) {
            command.staffIds.forEach { sid ->
                val staffHours =
                    staffWorkingHoursRepository.findByStaffIdAndSalonId(sid, command.salonId)
                        ?: StaffWorkingHoursSettings.createDefault(sid, command.salonId)

                val conflicts =
                    schedulingConflictService.detectConflicts(
                        command.startTime.withZoneSameInstant(ZoneId.systemDefault()).toLocalDate(),
                        command.startTime.withZoneSameInstant(ZoneId.systemDefault()).toLocalTime(),
                        command.endTime.withZoneSameInstant(ZoneId.systemDefault()).toLocalTime(),
                        sid,
                        salonHours,
                        staffHours,
                        existingAppointments.filter { it.staffId == sid },
                        existingBlocks,
                    )
                if (conflicts.isNotEmpty()) {
                    throw SchedulingConflictException("Interval indisponibil pentru personal")
                }
            }
        }

        val validationResult =
            blockTimeSchedulingService.validateBlockTimeCreation(
                command.salonId,
                command.startTime,
                command.endTime,
                command.staffIds,
                existingBlocks,
                existingAppointments,
            )

        // Handle conflicts based on allowConflicts flag
        val conflicts = validationResult.getConflicts()
        if (!validationResult.isSuccess() && !command.allowConflicts) {
            throw SchedulingConflictException(
                "Există conflicte în intervalul selectat",
                conflicts,
            )
        }

        // Create block time
        val blockTime =
            BlockTime(
                id = BlockTimeId.generate(),
                salonId = command.salonId,
                startTime = command.startTime,
                endTime = command.endTime,
                reason = command.reason,
                customReason = command.customReason,
                staffIds = command.staffIds,
                createdBy = command.createdBy,
                isRecurring = command.isRecurring,
                recurrencePattern = command.recurrencePattern,
                notes = command.notes,
            )

        val savedBlockTime = blockTimeRepository.save(blockTime)

        // Find affected appointments
        val affectedAppointments = findAffectedAppointments(existingAppointments)

        logger.info("Block time created successfully: ${savedBlockTime.id.value}")

        return BlockTimeResult(
            blockTime = savedBlockTime,
            affectedAppointments = affectedAppointments,
            conflicts = conflicts,
            message = if (conflicts.isNotEmpty()) {
                "Timpul a fost blocat cu succes pentru ${command.staffIds.size} membri ai echipei. Există ${conflicts.size} conflicte informative."
            } else {
                "Timpul a fost blocat cu succes pentru ${command.staffIds.size} membri ai echipei"
            },
        )
    }

    override fun createMultiDayBlockTime(command: CreateMultiDayBlockTimeCommand): MultiDayBlockTimeResult {
        logger.info("Creating multi-day block time for salon: ${command.salonId.value}")
        logger.debug("Multi-day block details:")
        logger.debug("  - Date range: ${command.startDate} to ${command.endDate}")
        logger.debug("  - Time range: ${command.startTime} to ${command.endTime}")
        logger.debug("  - Staff IDs: ${command.staffIds.map { it.value }}")
        logger.debug("  - Reason: ${command.reason}")
        logger.debug("  - Allow conflicts: ${command.allowConflicts}")

        val createdBlocks = mutableListOf<BlockTime>()
        val allAffectedAppointments = mutableListOf<AffectedAppointment>()
        val allConflicts = mutableListOf<BlockTimeConflict>()
        var successCount = 0
        var failureCount = 0

        // Iterate through each day in the range
        var currentDate = command.startDate
        while (!currentDate.isAfter(command.endDate)) {
            try {
                val startDateTime = currentDate.atTime(command.startTime).atZone(ZoneId.systemDefault())
                val endDateTime = currentDate.atTime(command.endTime).atZone(ZoneId.systemDefault())

                // Create single-day block command
                val singleDayCommand = CreateBlockTimeCommand(
                    salonId = command.salonId,
                    startTime = startDateTime,
                    endTime = endDateTime,
                    reason = command.reason,
                    customReason = command.customReason,
                    staffIds = command.staffIds,
                    notes = command.notes,
                    createdBy = command.createdBy,
                    allowConflicts = command.allowConflicts,
                )

                // Create block for this day
                val result = createBlockTime(singleDayCommand)
                createdBlocks.add(result.blockTime)
                allAffectedAppointments.addAll(result.affectedAppointments)
                allConflicts.addAll(result.conflicts)
                successCount++

                logger.debug("Successfully created block for date: $currentDate")
            } catch (e: Exception) {
                logger.error("Failed to create block for date: $currentDate", e)
                failureCount++
            }

            currentDate = currentDate.plusDays(1)
        }

        val totalDays = ChronoUnit.DAYS.between(command.startDate, command.endDate).toInt() + 1
        val message = when {
            failureCount == 0 -> "Timpul a fost blocat cu succes pentru $totalDays zile și ${command.staffIds.size} membri ai echipei"
            successCount == 0 -> "Nu s-a putut bloca timpul pentru nicio zi"
            else -> "Timpul a fost blocat pentru $successCount din $totalDays zile"
        }

        logger.info("Multi-day block time creation completed: $successCount successes, $failureCount failures")

        return MultiDayBlockTimeResult(
            blockTimes = createdBlocks,
            affectedAppointments = allAffectedAppointments,
            conflicts = allConflicts,
            successCount = successCount,
            failureCount = failureCount,
            message = message,
        )
    }

    override fun getBlockTimeById(query: GetBlockTimeByIdQuery): BlockTime? {
        logger.debug("Getting block time by ID: ${query.blockId.value}")
        return blockTimeRepository.findByIdAndSalonId(query.blockId, query.salonId)
    }

    override fun getBlockTimeList(query: GetBlockTimeListQuery): BlockTimeListResult {
        logger.debug("Getting block time list for salon: ${query.salonId.value}")

        val blocks =
            blockTimeRepository.findBySalonId(
                salonId = query.salonId,
                startDate = query.startDate,
                endDate = query.endDate,
                staffId = query.staffId,
                reason = query.reason,
                status = query.status,
                page = query.page,
                limit = query.limit,
            )

        val totalCount =
            blockTimeRepository.countBySalonId(
                salonId = query.salonId,
                startDate = query.startDate,
                endDate = query.endDate,
                staffId = query.staffId,
                reason = query.reason,
                status = query.status,
            )

        // Populate staff names
        val blocksWithStaffNames =
            blocks.map { block ->
                val staffNames = getStaffNames(block.staffIds)
                val createdByName = getUserName(block.createdBy)
                BlockTimeWithStaffNames(
                    blockTime = block,
                    staffNames = staffNames,
                    createdByName = createdByName,
                )
            }

        val pagination =
            PaginationInfo(
                currentPage = query.page,
                totalPages = ((totalCount + query.limit - 1) / query.limit).toInt(),
                totalItems = totalCount.toInt(),
                itemsPerPage = query.limit,
                hasNextPage = query.page * query.limit < totalCount,
                hasPreviousPage = query.page > 1,
            )

        val summary = calculateSummary(blocks)

        return BlockTimeListResult(
            blocks = blocksWithStaffNames,
            pagination = pagination,
            summary = summary,
        )
    }

    override fun getBlockTimeDetails(query: GetBlockTimeDetailsQuery): BlockTimeDetails? {
        logger.debug("Getting block time details: ${query.blockId.value}")

        val blockTime =
            blockTimeRepository.findByIdAndSalonId(query.blockId, query.salonId)
                ?: return null

        val staffDetails = getStaffDetails(blockTime.staffIds, query.salonId)
        val createdByName = getUserName(blockTime.createdBy)
        val affectedAppointments = findAffectedAppointmentsForBlock(blockTime)
        val history = getBlockTimeHistory(blockTime)

        return BlockTimeDetails(
            blockTime = blockTime,
            staffDetails = staffDetails,
            createdByName = createdByName,
            affectedAppointments = affectedAppointments,
            history = history,
        )
    }

    override fun updateBlockTime(command: UpdateBlockTimeCommand): BlockTimeUpdateResult {
        logger.debug("Updating block time: ${command.blockId.value}")

        // Validate permissions
        validateUpdatePermissions(command.updatedBy, command.salonId)

        val existingBlock =
            blockTimeRepository.findByIdAndSalonId(command.blockId, command.salonId)
                ?: throw EntityNotFoundException("Block time not found: ${command.blockId.value}")

        if (!existingBlock.isActive()) {
            throw BlockTimeOperationException("Cannot update cancelled or expired block time")
        }

        // Track changes
        val changes = mutableListOf<FieldChange>()

        // Update block time
        val updatedBlock =
            existingBlock.update(
                startTime = command.startTime,
                endTime = command.endTime,
                reason = command.reason,
                customReason = command.customReason,
                staffIds = command.staffIds,
                notes = command.notes,
                updatedBy = command.updatedBy,
            )

        // Validate conflicts if time or staff changed
        if (command.startTime != null || command.endTime != null || command.staffIds != null) {
            validateUpdateConflicts(updatedBlock, existingBlock)
        }

        val savedBlock = blockTimeRepository.update(updatedBlock)

        // Track field changes
        trackFieldChanges(existingBlock, updatedBlock, changes)

        logger.info("Block time updated successfully: ${savedBlock.id.value}")

        return BlockTimeUpdateResult(
            blockTime = savedBlock,
            changes = changes,
            message = "Blocul de timp a fost actualizat cu succes",
        )
    }

    override fun deleteBlockTime(command: DeleteBlockTimeCommand): BlockTimeDeletionResult {
        logger.debug("Deleting block time: ${command.blockId.value}")

        // Validate permissions
        validateDeletePermissions(command.deletedBy, command.salonId)

        val blockTime =
            blockTimeRepository.findByIdAndSalonId(command.blockId, command.salonId)
                ?: throw EntityNotFoundException("Block time not found: ${command.blockId.value}")

        if (!blockTime.isActive()) {
            throw BlockTimeOperationException("Block time is already cancelled or expired")
        }

        // Cancel the block time
        val cancelledBlock = blockTime.cancel(command.deletedBy, command.reason)
        blockTimeRepository.update(cancelledBlock)

        // Get affected staff information
        val affectedStaff = getAffectedStaffMembers(blockTime.staffIds, command.notifyStaff)

        logger.info("Block time deleted successfully: ${command.blockId.value}")

        return BlockTimeDeletionResult(
            blockId = command.blockId.value,
            status = "CANCELLED",
            cancelledBy = command.deletedBy.value,
            cancelledAt = LocalDateTime.now(),
            cancellationReason = command.reason,
            affectedStaff = affectedStaff,
            message = "Blocul de timp a fost anulat cu succes",
        )
    }

    override fun bulkBlockTimeOperations(command: BulkBlockTimeOperationsCommand): BulkOperationResult {
        logger.debug("Performing bulk ${command.operation} operation for salon: ${command.salonId.value}")

        val results = mutableListOf<BulkOperationItemResult>()
        var successful = 0
        var failed = 0

        command.blocks.forEachIndexed { index, item ->
            try {
                val result =
                    when (command.operation) {
                        BulkOperation.CREATE -> {
                            val createCommand = item.toCreateCommand(command.salonId, command.performedBy)
                            if (createCommand != null) {
                                val blockResult = createBlockTime(createCommand)
                                BulkOperationItemResult(
                                    index = index,
                                    success = true,
                                    blockId = blockResult.blockTime.id.value,
                                    message = "Bloc creat cu succes",
                                )
                            } else {
                                BulkOperationItemResult(
                                    index = index,
                                    success = false,
                                    blockId = null,
                                    message = "Date insuficiente pentru creare",
                                    error = "Missing required fields",
                                )
                            }
                        }
                        BulkOperation.UPDATE -> {
                            val updateCommand = item.toUpdateCommand(command.salonId, command.performedBy)
                            if (updateCommand != null) {
                                val updateResult = updateBlockTime(updateCommand)
                                BulkOperationItemResult(
                                    index = index,
                                    success = true,
                                    blockId = updateResult.blockTime.id.value,
                                    message = "Bloc actualizat cu succes",
                                )
                            } else {
                                BulkOperationItemResult(
                                    index = index,
                                    success = false,
                                    blockId = null,
                                    message = "ID bloc lipsă pentru actualizare",
                                    error = "Missing block ID",
                                )
                            }
                        }
                        BulkOperation.DELETE -> {
                            val deleteCommand = item.toDeleteCommand(command.salonId, command.performedBy)
                            if (deleteCommand != null) {
                                deleteBlockTime(deleteCommand)
                                BulkOperationItemResult(
                                    index = index,
                                    success = true,
                                    blockId = deleteCommand.blockId.value,
                                    message = "Bloc șters cu succes",
                                )
                            } else {
                                BulkOperationItemResult(
                                    index = index,
                                    success = false,
                                    blockId = null,
                                    message = "ID bloc lipsă pentru ștergere",
                                    error = "Missing block ID",
                                )
                            }
                        }
                    }

                if (result.success) successful++ else failed++
                results.add(result)
            } catch (e: Exception) {
                logger.warn("Bulk operation failed for item $index", e)
                failed++
                results.add(
                    BulkOperationItemResult(
                        index = index,
                        success = false,
                        blockId = null,
                        message = "Operație eșuată",
                        error = e.message,
                    ),
                )
            }
        }

        return BulkOperationResult(
            operation = command.operation.displayName,
            totalRequested = command.blocks.size,
            successful = successful,
            failed = failed,
            results = results,
            message = "$successful din ${command.blocks.size} blocuri au fost procesate cu succes",
        )
    }

    override fun checkTimeAvailability(query: CheckTimeAvailabilityQuery): AvailabilityCheckResult {
        logger.debug("Checking time availability for salon: ${query.salonId.value}")

        val existingBlocks =
            blockTimeRepository.findOverlappingBlocks(
                query.salonId,
                query.startTime,
                query.endTime,
                query.staffIds,
            )
        val existingAppointments =
            appointmentRepository.findBySalonIdAndStaffIdsAndTimeRange(
                query.salonId,
                query.staffIds,
                query.startTime,
                query.endTime,
            )

        val conflicts =
            blockTimeSchedulingService.detectConflicts(
                query.startTime,
                query.endTime,
                query.staffIds,
                existingBlocks,
                existingAppointments,
            )

        val availableStaff =
            query.staffIds.map { staffId ->
                val staffConflicts = conflicts.filter { it.staffId == staffId }
                val staffName = getStaffNameByStaffId(staffId)
                AvailableStaffInfo(
                    staffId = staffId.value,
                    staffName = staffName,
                    available = staffConflicts.isEmpty(),
                )
            }

        val suggestions =
            if (conflicts.isNotEmpty()) {
                blockTimeSchedulingService.suggestAlternativeTimeSlots(
                    query.startTime,
                    query.endTime,
                    query.staffIds,
                    existingBlocks,
                    existingAppointments,
                )
            } else {
                emptyList()
            }

        return AvailabilityCheckResult(
            available = conflicts.isEmpty(),
            conflicts = conflicts,
            availableStaff = availableStaff,
            suggestions = suggestions,
        )
    }

    override fun getBlockTimeStatistics(query: GetBlockTimeStatisticsQuery): BlockTimeStatistics {
        logger.debug("Getting block time statistics for salon: ${query.salonId.value}")

        val startDate = query.startDate ?: java.time.LocalDate.now().minusMonths(1)
        val endDate = query.endDate ?: java.time.LocalDate.now()

        val statisticsData =
            blockTimeRepository.getStatisticsData(
                query.salonId,
                startDate,
                endDate,
                query.staffId,
            )

        val period =
            StatisticsPeriod(
                startDate = startDate,
                endDate = endDate,
                totalDays = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate).toInt() + 1,
            )

        val summary =
            StatisticsSummary(
                totalBlocks = statisticsData.totalBlocks.toInt(),
                totalHoursBlocked = statisticsData.totalMinutes.toDouble() / 60,
                averageBlockDuration =
                    if (statisticsData.totalBlocks > 0) {
                        (statisticsData.totalMinutes / statisticsData.totalBlocks).toInt()
                    } else {
                        0
                    },
                mostActiveDay = statisticsData.dailyCounts.maxByOrNull { it.value }?.key,
                leastActiveDay = statisticsData.dailyCounts.minByOrNull { it.value }?.key,
            )

        val byReason =
            statisticsData.reasonCounts.map { (reason, count) ->
                val totalMinutes = statisticsData.totalMinutes
                ReasonStatistics(
                    reason = reason.displayName,
                    count = count.toInt(),
                    totalHours = (count * 60).toDouble() / 60, // Approximate
                    percentage = if (totalMinutes > 0) (count.toDouble() / statisticsData.totalBlocks * 100) else 0.0,
                )
            }

        val byStaff =
            statisticsData.staffCounts.map { (staffId, count) ->
                val staffName = getStaffNameByStaffId(staffId)
                StaffStatistics(
                    staffId = staffId.value,
                    staffName = staffName,
                    totalBlocks = count.toInt(),
                    totalHours = (count * 60).toDouble() / 60, // Approximate
                    averageDuration = 60, // Default
                    mostCommonReason = "Pauză", // Default
                )
            }

        val weeklyPattern =
            java.time.DayOfWeek.values().map { dayOfWeek ->
                WeeklyPatternEntry(
                    day = dayOfWeek.name,
                    averageBlocks = 0.0, // Calculate from daily data
                    averageHours = 0.0,
                )
            }

        val hourlyPattern =
            (9..17).map { hour ->
                HourlyPatternEntry(
                    hour = hour,
                    averageBlocks = statisticsData.hourlyCounts[hour]?.toDouble() ?: 0.0,
                )
            }

        val trends =
            StatisticsTrends(
                weeklyPattern = weeklyPattern,
                hourlyPattern = hourlyPattern,
            )

        return BlockTimeStatistics(
            period = period,
            summary = summary,
            byReason = byReason,
            byStaff = byStaff,
            trends = trends,
        )
    }

    // Helper methods implementation
    private fun validatePermissions(
        userId: UserId,
        salonId: SalonId,
        staffIds: Set<StaffId>,
    ) {
        // Find the staff record for the requesting user
        val requestingUserStaff =
            staffRepository.findByUserIdAndSalonId(userId, salonId)
                ?: throw UnauthorizedException("User is not a staff member in this salon")

        // Check if user has permission to manage schedules
        if (!requestingUserStaff.permissions.canManageSchedule) {
            throw UnauthorizedException("User does not have permission to update block times in this salon")
        }
    }

    private fun validateStaffMembers(
        staffIds: Set<StaffId>,
        salonId: SalonId,
    ) {
        staffIds.forEach { userId ->
            val staff = staffRepository.findById(userId)
            if (staff == null || !staff.isActive || staff.salonId != salonId) {
                throw EntityNotFoundException("Staff member not found or inactive: ${userId.value}")
            }
        }
    }

    private fun findAffectedAppointments(appointments: List<Appointment>): List<AffectedAppointment> {
        return appointments.map { appointment ->
            AffectedAppointment(
                appointmentId = appointment.id.value,
                clientName = "Client", // Will be populated from client repository
                conflictType = "OVERLAP",
                suggestedAction = "RESCHEDULE",
            )
        }
    }

    private fun getStaffNames(staffIds: Set<StaffId>): List<String> {
        return staffIds.mapNotNull { staffId ->
            getStaffNameByStaffId(staffId)
        }
    }

    private fun getUserName(userId: UserId): String {
        return userRepository.findById(userId)?.name ?: "Unknown User"
    }

    private fun getStaffName(
        staffId: StaffId,
        salonId: SalonId,
    ): String {
        val staff = staffRepository.findById(staffId)
        if (staff != null) {
            val user = userRepository.findById(staff.userId)
            return user?.name ?: staff.nickname ?: "Unknown Staff"
        }
        return "Unknown Staff"
    }

    private fun getStaffNameByStaffId(staffId: StaffId): String {
        val staff = staffRepository.findById(staffId)
        if (staff != null) {
            val user = userRepository.findById(staff.userId)
            return user?.name ?: staff.nickname ?: "Unknown Staff"
        }
        return "Unknown Staff"
    }

    private fun calculateSummary(blocks: List<BlockTime>): BlockTimeSummary {
        val activeBlocks = blocks.filter { it.isActive() }
        val totalHours = activeBlocks.sumOf { it.getDurationMinutes() }.toDouble() / 60
        val reasonCounts = activeBlocks.groupingBy { it.reason }.eachCount()
        val mostCommonReason = reasonCounts.maxByOrNull { it.value }?.key?.displayName ?: ""

        return BlockTimeSummary(
            totalActiveBlocks = activeBlocks.size,
            totalHoursBlocked = totalHours,
            mostCommonReason = mostCommonReason,
            staffWithMostBlocks = null, // Will be calculated if needed
        )
    }

    private fun getStaffDetails(
        staffIds: Set<StaffId>,
        salonId: SalonId,
    ): List<StaffInfo> {
        return staffIds.mapNotNull { userId ->
            val staff = staffRepository.findById(userId)
            if (staff != null && staff.salonId == salonId) {
                val user = userRepository.findById(staff.userId)
                StaffInfo(
                    staffId = staff.id.value, // Use Staff ID instead of User ID
                    name = user?.name ?: staff.nickname ?: "Unknown Staff",
                    nickname = staff.nickname,
                    role = staff.role.name,
                )
            } else {
                null
            }
        }
    }

    private fun getStaffDetailsByStaffIds(staffIds: Set<StaffId>): List<StaffInfo> {
        return staffIds.mapNotNull { staffId ->
            val staff = staffRepository.findById(staffId)
            if (staff != null) {
                val user = userRepository.findById(staff.userId)
                StaffInfo(
                    staffId = staff.id.value,
                    name = user?.name ?: staff.nickname ?: "Unknown Staff",
                    nickname = staff.nickname,
                    role = staff.role.name,
                )
            } else {
                null
            }
        }
    }

    private fun findAffectedAppointmentsForBlock(blockTime: BlockTime): List<AffectedAppointment> {
        val appointments =
            appointmentRepository.findBySalonIdAndStaffIdsAndTimeRange(
                blockTime.salonId,
                blockTime.staffIds,
                blockTime.startTime,
                blockTime.endTime,
            )
        return findAffectedAppointments(appointments)
    }

    private fun getBlockTimeHistory(blockTime: BlockTime): List<BlockTimeHistoryEntry> {
        // For now, return creation entry. In a full implementation,
        // this would query an audit/history table
        val createdByName = getUserName(blockTime.createdBy)
        return listOf(
            BlockTimeHistoryEntry(
                action = "CREATED",
                performedBy = blockTime.createdBy.value,
                performedByName = createdByName,
                timestamp = blockTime.createdAt,
                details = "Bloc de timp creat pentru ${blockTime.staffIds.size} membri ai echipei",
            ),
        )
    }

    private fun validateUpdatePermissions(
        userId: UserId,
        salonId: SalonId,
    ) {
        val requestingUserStaff =
            staffRepository.findByUserIdAndSalonId(userId, salonId)
                ?: throw UnauthorizedException("User is not a staff member in this salon")

        // Only staff members with appropriate permissions can update block times
        if (!requestingUserStaff.permissions.canManageSchedule) {
            throw UnauthorizedException("User does not have permission to update block times in this salon")
        }
    }

    private fun validateUpdateConflicts(
        updatedBlock: BlockTime,
        existingBlock: BlockTime,
    ) {
        // Only check conflicts if time or staff changed
        val timeChanged =
            updatedBlock.startTime != existingBlock.startTime ||
                updatedBlock.endTime != existingBlock.endTime
        val staffChanged = updatedBlock.staffIds != existingBlock.staffIds

        if (timeChanged || staffChanged) {
            val existingBlocks =
                blockTimeRepository.findOverlappingBlocks(
                    updatedBlock.salonId,
                    updatedBlock.startTime,
                    updatedBlock.endTime,
                    updatedBlock.staffIds,
                    excludeBlockId = updatedBlock.id,
                )
            val existingAppointments =
                appointmentRepository.findBySalonIdAndStaffIdsAndTimeRange(
                    updatedBlock.salonId,
                    updatedBlock.staffIds,
                    updatedBlock.startTime,
                    updatedBlock.endTime,
                )

            val validationResult =
                blockTimeSchedulingService.validateBlockTimeCreation(
                    updatedBlock.salonId,
                    updatedBlock.startTime,
                    updatedBlock.endTime,
                    updatedBlock.staffIds,
                    existingBlocks,
                    existingAppointments,
                )

            if (!validationResult.isSuccess()) {
                throw SchedulingConflictException(
                    "Actualizarea creează conflicte în intervalul selectat",
                    validationResult.getConflicts(),
                )
            }
        }
    }

    private fun trackFieldChanges(
        existing: BlockTime,
        updated: BlockTime,
        changes: MutableList<FieldChange>,
    ) {
        if (existing.startTime != updated.startTime) {
            changes.add(FieldChange("startTime", existing.startTime.toString(), updated.startTime.toString()))
        }
        if (existing.endTime != updated.endTime) {
            changes.add(FieldChange("endTime", existing.endTime.toString(), updated.endTime.toString()))
        }
        if (existing.reason != updated.reason) {
            changes.add(FieldChange("reason", existing.reason.displayName, updated.reason.displayName))
        }
        if (existing.staffIds != updated.staffIds) {
            changes.add(FieldChange("staffIds", existing.staffIds.toString(), updated.staffIds.toString()))
        }
        if (existing.notes != updated.notes) {
            changes.add(FieldChange("notes", existing.notes ?: "", updated.notes ?: ""))
        }
    }

    private fun validateDeletePermissions(
        userId: UserId,
        salonId: SalonId,
    ) {
        val requestingUserStaff =
            staffRepository.findByUserIdAndSalonId(userId, salonId)
                ?: throw UnauthorizedException("User is not a staff member in this salon")

        // Only staff members with appropriate permissions can delete block times
        if (!requestingUserStaff.permissions.canManageSchedule) {
            throw UnauthorizedException("User does not have permission to delete block times in this salon")
        }
    }

    private fun getAffectedStaffMembers(
        staffIds: Set<StaffId>,
        notify: Boolean,
    ): List<AffectedStaffMember> {
        return staffIds.map { staffId ->
            val staffName = getStaffNameByStaffId(staffId)
            AffectedStaffMember(
                staffId = staffId.value,
                staffName = staffName,
                notified = notify,
            )
        }
    }
}
