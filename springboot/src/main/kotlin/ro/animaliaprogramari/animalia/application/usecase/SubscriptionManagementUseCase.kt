package ro.animaliaprogramari.animalia.application.usecase

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SubscriptionValidationService
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Use case for managing salon subscriptions
 */
@Service
@Transactional
class SubscriptionManagementUseCase(
    private val subscriptionRepository: SalonSubscriptionRepository,
    private val validationService: SubscriptionValidationService
) {

    /**
     * Create a new subscription for a salon
     */
    fun createSubscription(command: CreateSubscriptionCommand): SalonSubscription {
        // Check if salon already has an active subscription
        val existingSubscription = subscriptionRepository.findActiveBySalonId(command.salonId)
        if (existingSubscription != null) {
            throw DomainException("Salon already has an active subscription")
        }

        val subscription = SalonSubscription.create(
            salonId = command.salonId,
            tier = command.tier,
            monthlyPrice = command.monthlyPrice,
            billingCycle = command.billingCycle,
            trialDays = command.trialDays
        )

        return subscriptionRepository.save(subscription)
    }

    /**
     * Upgrade subscription to a higher tier
     */
    fun upgradeSubscription(command: UpgradeSubscriptionCommand): SalonSubscription {
        val validation = validationService.validateSubscriptionUpgrade(command.salonId, command.newTier)
        if (!validation.isValid) {
            throw DomainException(validation.errorMessage!!)
        }

        val currentSubscription = subscriptionRepository.findActiveBySalonId(command.salonId)
            ?: throw DomainException("No active subscription found for salon")

        val upgradedSubscription = currentSubscription.upgradeTo(command.newTier, command.newPrice)
        return subscriptionRepository.save(upgradedSubscription)
    }

    /**
     * Cancel subscription
     */
    fun cancelSubscription(command: CancelSubscriptionCommand): SalonSubscription {
        val subscription = subscriptionRepository.findActiveBySalonId(command.salonId)
            ?: throw DomainException("No active subscription found for salon")

        val cancelledSubscription = subscription.cancel(command.reason)
        return subscriptionRepository.save(cancelledSubscription)
    }

    /**
     * Reactivate cancelled subscription
     */
    fun reactivateSubscription(salonId: SalonId): SalonSubscription {
        val subscriptions = subscriptionRepository.findAllBySalonId(salonId)
        val cancelledSubscription = subscriptions
            .filter { it.status == SubscriptionStatus.CANCELLED }
            .maxByOrNull { it.cancelledAt ?: LocalDateTime.MIN }
            ?: throw DomainException("No cancelled subscription found for salon")

        val reactivatedSubscription = cancelledSubscription.reactivate()
        return subscriptionRepository.save(reactivatedSubscription)
    }

    /**
     * Get current subscription for salon
     */
    fun getCurrentSubscription(salonId: SalonId): SalonSubscription? {
        return subscriptionRepository.findActiveBySalonId(salonId)
    }

    /**
     * Get subscription history for salon
     */
    fun getSubscriptionHistory(salonId: SalonId): List<SalonSubscription> {
        return subscriptionRepository.findAllBySalonId(salonId)
    }

    /**
     * Get subscription features for salon
     */
    fun getSubscriptionFeatures(salonId: SalonId): SubscriptionFeatures {
        return validationService.getSubscriptionFeatures(salonId)
    }

    /**
     * Check subscription limits for various operations
     */
    fun checkSmsQuota(salonId: SalonId, currentUsage: Int): Boolean {
        return validationService.validateSmsQuota(salonId, currentUsage).isValid
    }

    fun checkTeamInvitation(salonId: SalonId, currentTeamSize: Int): Boolean {
        return validationService.validateTeamInvitation(salonId, currentTeamSize).isValid
    }

    fun checkSalonChange(salonId: SalonId): Boolean {
        return validationService.validateSalonChange(salonId).isValid
    }

    /**
     * Get subscription statistics
     */
    fun getSubscriptionStatistics(): SubscriptionStatistics {
        val totalActive = subscriptionRepository.countActiveSubscriptions()
        val freelancerCount = subscriptionRepository.countByTier(SubscriptionTier.FREELANCER)
        val teamCount = subscriptionRepository.countByTier(SubscriptionTier.TEAM)
        val enterpriseCount = subscriptionRepository.countByTier(SubscriptionTier.ENTERPRISE)

        return SubscriptionStatistics(
            totalActiveSubscriptions = totalActive,
            freelancerSubscriptions = freelancerCount,
            teamSubscriptions = teamCount,
            enterpriseSubscriptions = enterpriseCount
        )
    }
}

/**
 * Command for creating a new subscription
 */
data class CreateSubscriptionCommand(
    val salonId: SalonId,
    val tier: SubscriptionTier,
    val monthlyPrice: BigDecimal,
    val billingCycle: BillingCycle = BillingCycle.MONTHLY,
    val trialDays: Int? = null
)

/**
 * Command for upgrading subscription
 */
data class UpgradeSubscriptionCommand(
    val salonId: SalonId,
    val newTier: SubscriptionTier,
    val newPrice: BigDecimal
)

/**
 * Command for cancelling subscription
 */
data class CancelSubscriptionCommand(
    val salonId: SalonId,
    val reason: String? = null
)

/**
 * Subscription statistics
 */
data class SubscriptionStatistics(
    val totalActiveSubscriptions: Long,
    val freelancerSubscriptions: Long,
    val teamSubscriptions: Long,
    val enterpriseSubscriptions: Long
)
