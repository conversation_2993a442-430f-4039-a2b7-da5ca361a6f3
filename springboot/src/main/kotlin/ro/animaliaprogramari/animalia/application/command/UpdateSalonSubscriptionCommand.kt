package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SalonSubscriptionPlan
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Command to update a salon's subscription plan
 */
data class UpdateSalonSubscriptionCommand(
    val salonId: SalonId,
    val plan: SalonSubscriptionPlan,
    val updaterUserId: UserId,
)
