package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Command to update notification settings for a user within a salon
 */
data class UpdateNotificationSettingsCommand(
    val userId: UserId,
    val salonId: SalonId,
    val pushNotificationsEnabled: Boolean,
    val soundPreference: SoundPreference,
    val vibrationEnabled: <PERSON>olean,
    val doNotDisturb: DoNotDisturbSettings,
    val notificationRules: NotificationRules,
)
