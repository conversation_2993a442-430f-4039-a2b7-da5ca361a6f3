package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.JwtToken

/**
 * Outbound port for JWT token generation and validation
 * This will be implemented by JWT adapter
 */
interface JwtTokenGenerator {
    /**
     * Generate JWT token for authenticated user
     */
    fun generateToken(user: AuthenticatedUser): JwtToken

    /**
     * Validate JWT token and extract user information
     */
    fun validateToken(token: String): JwtTokenValidationResult

    /**
     * Refresh JWT token
     */
    fun refreshToken(token: String): JwtToken?
}

/**
 * Result of JWT token validation
 */
data class JwtTokenValidationResult(
    val isValid: Boolean,
    val userId: String?,
    val firebaseUid: String?,
    val email: String?,
    val name: String?,
    val role: String?,
    val isActive: Boolean = false,
    val errorMessage: String? = null,
) {
    companion object {
        fun success(
            userId: String,
            firebaseUid: String,
            email: String,
            name: String,
            role: String,
            isActive: Boolean,
        ): JwtTokenValidationResult {
            return JwtTokenValidationResult(
                isValid = true,
                userId = userId,
                firebaseUid = firebaseUid,
                email = email,
                name = name,
                role = role,
                isActive = isActive,
            )
        }

        fun failure(errorMessage: String): JwtTokenValidationResult {
            return JwtTokenValidationResult(
                isValid = false,
                userId = null,
                firebaseUid = null,
                email = null,
                name = null,
                role = null,
                errorMessage = errorMessage,
            )
        }
    }
}
