package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsProvider
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Command to update SMS reminder settings for a salon
 */
data class UpdateSmsReminderSettingsCommand(
    val salonId: SalonId,
    val enabled: <PERSON><PERSON><PERSON>,
    val appointmentConfirmations: <PERSON><PERSON><PERSON>,
    val dayBeforeReminders: <PERSON><PERSON><PERSON>,
    val followUpMessages: <PERSON><PERSON><PERSON>,
    val selectedProvider: SmsProvider,
    val updaterUserId: UserId,
)

data class SmsMessageCommand(
    val message: String,
    val phoneNumber: String,
)
