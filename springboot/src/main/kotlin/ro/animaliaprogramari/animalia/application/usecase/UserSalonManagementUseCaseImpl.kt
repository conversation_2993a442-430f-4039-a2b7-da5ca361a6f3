package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.SwitchCurrentSalonCommand
import ro.animaliaprogramari.animalia.application.port.inbound.UserSalonManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.query.GetUserStaffAssociationsQuery
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.Staff
import ro.animaliaprogramari.animalia.domain.model.User

/**
 * Implementation of user salon management use case
 * Handles all user-salon relationship business logic through staff
 */
@Service
@Transactional
class UserSalonManagementUseCaseImpl(
    private val userRepository: UserRepository,
    private val salonRepository: SalonRepository,
    private val staffRepository: StaffRepository,
) : UserSalonManagementUseCase {
    private val logger = LoggerFactory.getLogger(UserSalonManagementUseCaseImpl::class.java)

    override fun getUserStaffAssociations(query: GetUserStaffAssociationsQuery): List<Staff> {
        logger.debug("Getting staff associations for user: ${query.userId.value}")

        return if (query.activeOnly) {
            staffRepository.findActiveByUserId(query.userId)
        } else {
            staffRepository.findByUserId(query.userId)
        }
    }

    override fun switchCurrentSalon(command: SwitchCurrentSalonCommand): User {
        logger.debug("Switching current salon for user: ${command.userId.value} to salon: ${command.salonId.value}")

        // Verify user exists
        val user =
            userRepository.findById(command.userId)
                ?: throw EntityNotFoundException("User not found: ${command.userId.value}")

        // Verify salon exists
        val salon =
            salonRepository.findById(command.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Verify user has staff association with the salon
        val staff =
            staffRepository.findByUserIdAndSalonId(command.userId, command.salonId)
                ?: throw BusinessRuleViolationException("User does not have access to salon: ${command.salonId.value}")

        if (!staff.isActive) {
            throw BusinessRuleViolationException("User's staff association with salon is not active")
        }

        // Update user's current salon
        val updatedUser = user.setCurrentSalon(command.salonId)
        val savedUser = userRepository.save(updatedUser)

        logger.debug("Successfully switched current salon for user: ${command.userId.value}")
        return savedUser
    }
}
