package ro.animaliaprogramari.animalia.application.service

import com.google.firebase.messaging.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.outbound.FcmTokenRepository
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*

/**
 * Service for sending Firebase Cloud Messaging (FCM) push notifications
 */
@Service
class FirebaseService(
    private val fcmTokenRepository: FcmTokenRepository,
    private val notificationRepository: NotificationRepository,
) {
    private val logger = LoggerFactory.getLogger(FirebaseService::class.java)

    /**
     * Send push notification to a specific user
     */
    fun sendNotification(
        userId: UserId,
        salonId: SalonId,
        title: String,
        body: String,
        data: Map<String, String> = emptyMap(),
        appointmentId: AppointmentId? = null
    ): SendNotificationResult {
        return try {
            logger.info("Sending push notification to user: ${userId.value}")

            // Get active FCM tokens for the user
            val fcmTokens = fcmTokenRepository.findActiveByUserIdAndSalonId(userId, salonId)
            
            logger.info("Found ${fcmTokens.size} active FCM tokens for user ${userId.value} in salon ${salonId.value}")

            if (fcmTokens.isEmpty()) {
                logger.warn("No active FCM tokens found for user: ${userId.value} in salon: ${salonId.value}")
                return SendNotificationResult(
                    success = false,
                    devicesNotified = 0,
                    failureReason = "No active FCM tokens found"
                )
            }

            // Create notification record in database
            val notification = ro.animaliaprogramari.animalia.domain.model.notification.Notification.createPush(
                recipient = userId,
                title = title,
                body = body,
                data = data,
                appointmentId = appointmentId,
                salonId = salonId
            )

            val savedNotification = notificationRepository.save(notification)
            logger.debug("Created notification record: ${savedNotification.id.value}")

            // Send to all active tokens
            var successCount = 0
            var failureReason: String? = null

            for (fcmToken in fcmTokens) {
                try {
                    val message = buildFirebaseMessage(
                        token = fcmToken.token,
                        title = title,
                        body = body,
                        data = data + mapOf(
                            "notificationId" to savedNotification.id.value,
                            "appointmentId" to (appointmentId?.value ?: ""),
                            "salonId" to salonId.value
                        )
                    )

                    val response = FirebaseMessaging.getInstance().send(message)
                    logger.debug("Successfully sent message to token: ${fcmToken.token.take(10)}... Response: $response")

                    successCount++

                    // Update notification status to SENT
                    val sentNotification = savedNotification.markAsSent()
                    notificationRepository.save(sentNotification)

                } catch (e: Exception) {
                    logger.error("Failed to send notification to token: ${fcmToken.token.take(10)}...", e)
                    failureReason = e.message

                    // Update notification status to FAILED
                    val failedNotification = savedNotification.markAsFailed(e.message ?: "Unknown error")
                    notificationRepository.save(failedNotification)
                }
            }

            SendNotificationResult(
                success = successCount > 0,
                devicesNotified = successCount,
                failureReason = if (successCount == 0) failureReason else null,
                notificationId = savedNotification.id.value
            )

        } catch (e: Exception) {
            logger.error("Error sending push notification to user: ${userId.value}", e)
            SendNotificationResult(
                success = false,
                devicesNotified = 0,
                failureReason = "Internal error: ${e.message}"
            )
        }
    }

    /**
     * Send notification to multiple users
     */
    fun sendNotificationToUsers(
        userIds: List<UserId>,
        salonId: SalonId,
        title: String,
        body: String,
        data: Map<String, String> = emptyMap(),
        appointmentId: AppointmentId? = null
    ): BulkSendNotificationResult {
        val results = mutableListOf<SendNotificationResult>()

        for (userId in userIds) {
            val result = sendNotification(userId, salonId, title, body, data, appointmentId)
            results.add(result)
        }

        val totalSuccess = results.count { it.success }
        val totalDevices = results.sumOf { it.devicesNotified }

        return BulkSendNotificationResult(
            totalUsers = userIds.size,
            successfulUsers = totalSuccess,
            totalDevicesNotified = totalDevices,
            results = results
        )
    }

    /**
     * Build Firebase message
     */
    private fun buildFirebaseMessage(
        token: String,
        title: String,
        body: String,
        data: Map<String, String>
    ): Message {
        return Message.builder()
            .setToken(token)
            .setNotification(
                com.google.firebase.messaging.Notification.builder()
                    .setTitle(title)
                    .setBody(body)
                    .build()
            )
            .putAllData(data)
            .setAndroidConfig(
                AndroidConfig.builder()
                    .setNotification(
                        AndroidNotification.builder()
                            .setTitle(title)
                            .setBody(body)
                            .setIcon("ic_notification")
                            .setColor("#FF6B35")
                            .setSound("default")
                            .build()
                    )
                    .build()
            )
            .setApnsConfig(
                ApnsConfig.builder()
                    .setAps(
                        Aps.builder()
                            .setAlert(
                                ApsAlert.builder()
                                    .setTitle(title)
                                    .setBody(body)
                                    .build()
                            )
                            .setSound("default")
                            .setBadge(1)
                            .build()
                    )
                    .build()
            )
            .build()
    }
}

/**
 * Result of sending a notification
 */
data class SendNotificationResult(
    val success: Boolean,
    val devicesNotified: Int,
    val failureReason: String? = null,
    val notificationId: String? = null
)

/**
 * Result of sending notifications to multiple users
 */
data class BulkSendNotificationResult(
    val totalUsers: Int,
    val successfulUsers: Int,
    val totalDevicesNotified: Int,
    val results: List<SendNotificationResult>
)
