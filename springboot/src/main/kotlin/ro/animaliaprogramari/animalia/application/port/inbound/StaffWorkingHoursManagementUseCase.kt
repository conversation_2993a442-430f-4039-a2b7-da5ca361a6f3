package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.UpdateStaffWorkingHoursCommand
import ro.animaliaprogramari.animalia.application.query.GetBatchStaffWorkingHoursQuery
import ro.animaliaprogramari.animalia.application.query.GetStaffAvailabilityQuery
import ro.animaliaprogramari.animalia.application.query.GetStaffWorkingHoursQuery
import ro.animaliaprogramari.animalia.domain.model.StaffAvailabilityReport
import ro.animaliaprogramari.animalia.domain.model.StaffWorkingHoursSettings

/**
 * Use case port for staff working hours management operations
 * This is an inbound port that defines the business operations for staff working hours
 */
interface StaffWorkingHoursManagementUseCase {
    /**
     * Get working hours settings for a staff member
     */
    fun getStaffWorkingHours(query: GetStaffWorkingHoursQuery): StaffWorkingHoursSettings

    /**
     * Update working hours settings for a staff member
     */
    fun updateStaffWorkingHours(command: UpdateStaffWorkingHoursCommand): StaffWorkingHoursSettings

    /**
     * Reset staff working hours to default values
     */
    fun resetStaffWorkingHours(
        staffId: String,
        salonId: String,
        requesterId: String,
    ): StaffWorkingHoursSettings

    /**
     * Get working hours settings for multiple staff members
     */
    fun getBatchStaffWorkingHours(query: GetBatchStaffWorkingHoursQuery): List<StaffWorkingHoursSettings>

    /**
     * Get staff availability for a specific date and time range
     */
    fun getStaffAvailability(query: GetStaffAvailabilityQuery): StaffAvailabilityReport
}
