package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Gender
import ro.animaliaprogramari.animalia.domain.model.PetId
import java.math.BigDecimal

/**
 * Command to add a new pet
 */
data class AddPetCommand(
    val clientId: ClientId,
    val name: String,
    val breed: String? = null,
    val species: String? = null,
    val size: String? = null,
    val age: Int? = null,
    val weight: BigDecimal? = null,
    val color: String? = null,
    val gender: Gender? = null,
    val notes: String? = null,
    val medicalConditions: String? = null,
    val photoUrl: String? = null,
)

/**
 * Command to update pet information
 */
data class UpdatePetCommand(
    val petId: PetId,
    val name: String,
    val breed: String? = null,
    val age: Int? = null,
    val weight: BigDecimal? = null,
    val color: String? = null,
    val gender: Gender? = null,
    val notes: String? = null,
    val medicalConditions: String? = null,
    val photoUrl: String? = null,
)

/**
 * Command to deactivate a pet
 */
data class DeactivatePetCommand(
    val petId: PetId,
)

/**
 * Command to activate a pet
 */
data class ActivatePetCommand(
    val petId: PetId,
)
