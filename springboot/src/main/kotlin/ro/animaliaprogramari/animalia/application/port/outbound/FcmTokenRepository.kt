package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*
import java.time.LocalDateTime

/**
 * Outbound port for FCM token persistence
 * This interface defines the contract for FCM token data access
 */
interface FcmTokenRepository {
    /**
     * Save FCM token
     */
    fun save(fcmToken: FcmToken): FcmToken

    /**
     * Find FCM token by ID
     */
    fun findById(id: FcmTokenId): FcmToken?

    /**
     * Find FCM token by token value
     */
    fun findByToken(token: String): FcmToken?

    /**
     * Find active FCM tokens by user ID
     */
    fun findActiveByUserId(userId: UserId): List<FcmToken>

    /**
     * Find active FCM tokens by salon ID
     */
    fun findActiveBySalonId(salonId: SalonId): List<FcmToken>

    /**
     * Find active FCM tokens by user and salon
     */
    fun findActiveByUserIdAndSalonId(userId: UserId, salonId: SalonId): List<FcmToken>

    /**
     * Find FCM tokens by device ID
     */
    fun findByDeviceId(deviceId: String): List<FcmToken>

    /**
     * Count active FCM tokens by salon
     */
    fun countActiveBySalonId(salonId: SalonId): Long

    /**
     * Count active FCM tokens by user
     */
    fun countActiveByUserId(userId: UserId): Long

    /**
     * Find expired FCM tokens
     */
    fun findExpiredTokens(): List<FcmToken>

    /**
     * Delete inactive tokens older than specified date
     */
    fun deleteInactiveOlderThan(cutoffDate: LocalDateTime): Int

    /**
     * Deactivate tokens by user and salon (except specific token)
     */
    fun deactivateOtherTokens(userId: UserId, salonId: SalonId, keepToken: String): Int

    /**
     * Check if token exists and is active
     */
    fun existsActiveToken(token: String): Boolean
}
