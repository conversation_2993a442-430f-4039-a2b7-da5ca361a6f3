package ro.animaliaprogramari.animalia.application.query

import org.springframework.data.domain.Pageable
import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.time.LocalDateTime

/**
 * Query for retrieving notification history with filtering
 */
data class GetNotificationHistoryQuery(
    val salonId: SalonId,
    val type: String? = null,
    val readStatus: Boolean? = null,
    val startDate: LocalDateTime? = null,
    val endDate: LocalDateTime? = null,
    val pageable: Pageable
)

/**
 * Query for retrieving notification statistics
 */
data class GetNotificationStatisticsQuery(
    val salonId: SalonId,
    val startDate: LocalDateTime = LocalDateTime.now().minusDays(30)
)
