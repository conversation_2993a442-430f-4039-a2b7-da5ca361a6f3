package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime

/**
 * Query to check availability for appointments
 */
data class CheckAvailabilityQuery(
    val salonId: SalonId,
    val staffId: StaffId,
    val date: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
)

/**
 * Query to get conflicting appointments
 */
data class GetConflictingAppointmentsQuery(
    val salonId: SalonId,
    val staffId: StaffId,
    val date: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val excludeAppointmentId: AppointmentId? = null,
)

/**
 * Query to get available time slots
 */
data class GetAvailableSlotsQuery(
    val salonId: SalonId,
    val staffId: StaffId,
    val date: LocalDate,
    val serviceDuration: java.time.Duration,
)

/**
 * Query to get appointment summary
 */
data class GetAppointmentSummaryQuery(
    val salonId: SalonId,
    val dateRange: DateRange,
)

/**
 * Query to get appointments by date range
 */
data class GetAppointmentsByDateRangeQuery(
    val salonId: SalonId,
    val dateRange: DateRange,
    val staffId: StaffId? = null,
    val status: AppointmentStatus? = null,
)

/**
 * Query to get appointments by groomer
 */
data class GetAppointmentsByGroomerQuery(
    val salonId: SalonId,
    val staffId: StaffId,
    val dateRange: DateRange,
    val status: AppointmentStatus? = null,
)

/**
 * Query to check groomer availability
 */
data class CheckGroomerAvailabilityQuery(
    val salonId: SalonId,
    val staffId: StaffId,
    val date: LocalDate,
    val timeSlot: TimeSlot,
)

/**
 * Result for availability checks
 */
data class AvailabilityResult(
    val isAvailable: Boolean,
    val conflictingAppointments: List<Appointment> = emptyList(),
    val reason: String? = null,
)
