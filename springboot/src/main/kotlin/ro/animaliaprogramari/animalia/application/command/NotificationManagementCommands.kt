package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.NotificationId

/**
 * Command to generate test notification data
 */
data class GenerateTestNotificationDataCommand(
    val userId: UserId,
    val salonId: SalonId,
    val count: Int = 10
) {
    init {
        require(count > 0 && count <= 100) { "Count must be between 1 and 100" }
    }
}

/**
 * Command to send test notification
 */
data class SendTestNotificationCommand(
    val userId: UserId,
    val salonId: SalonId,
    val title: String,
    val message: String,
    val notificationType: String = "PUSH"
) {
    init {
        require(title.isNotBlank()) { "Title cannot be blank" }
        require(message.isNotBlank()) { "Message cannot be blank" }
    }
}

/**
 * Command to mark all notifications as read (bulk operation)
 */
data class BulkMarkNotificationsAsReadCommand(
    val userId: UserId,
    val salonId: SalonId
)

/**
 * Command to mark a specific notification as read
 */
data class MarkNotificationAsReadCommand(
    val notificationId: NotificationId,
    val userId: UserId,
    val salonId: SalonId
)

/**
 * Command to register FCM token
 */
data class RegisterFcmTokenCommand(
    val userId: UserId,
    val salonId: SalonId,
    val token: String,
    val deviceId: String? = null,
    val deviceType: String = "MOBILE"
) {
    init {
        require(token.isNotBlank()) { "FCM token cannot be blank" }
        require(deviceType.isNotBlank()) { "Device type cannot be blank" }
    }
}

/**
 * Command to update FCM token
 */
data class UpdateFcmTokenCommand(
    val userId: UserId,
    val salonId: SalonId,
    val oldToken: String,
    val newToken: String
) {
    init {
        require(oldToken.isNotBlank()) { "Old FCM token cannot be blank" }
        require(newToken.isNotBlank()) { "New FCM token cannot be blank" }
    }
}

/**
 * Command to deactivate FCM token
 */
data class DeactivateFcmTokenCommand(
    val userId: UserId,
    val salonId: SalonId,
    val token: String
) {
    init {
        require(token.isNotBlank()) { "FCM token cannot be blank" }
    }
}
