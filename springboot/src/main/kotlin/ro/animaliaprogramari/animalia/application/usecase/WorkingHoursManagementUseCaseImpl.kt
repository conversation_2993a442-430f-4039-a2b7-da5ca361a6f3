package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.UpdateWorkingHoursCommand
import ro.animaliaprogramari.animalia.application.port.inbound.WorkingHoursManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.application.query.GetWorkingHoursQuery
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.StaffRole
import ro.animaliaprogramari.animalia.domain.model.WorkingHoursSettings

/**
 * Implementation of working hours management use case
 * Contains the business logic for working hours operations
 */
@Service
@Transactional
class WorkingHoursManagementUseCaseImpl(
    private val workingHoursRepository: WorkingHoursRepository,
    private val salonRepository: SalonRepository,
    private val staffRepository: StaffRepository,
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
) : WorkingHoursManagementUseCase {
    private val logger = LoggerFactory.getLogger(WorkingHoursManagementUseCaseImpl::class.java)

    override fun getWorkingHours(query: GetWorkingHoursQuery): WorkingHoursSettings {
        logger.debug("Getting working hours for salon: ${query.salonId.value}")

        // Validate salon exists
        val salon =
            salonRepository.findById(query.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${query.salonId.value}")

        // Get working hours or create default if not exists
        val workingHours =
            workingHoursRepository.findBySalonId(query.salonId)
                ?: WorkingHoursSettings.createDefault(query.salonId)

        logger.debug("Working hours retrieved for salon: ${query.salonId.value}")
        return workingHours
    }

    override fun updateWorkingHours(command: UpdateWorkingHoursCommand): WorkingHoursSettings {
        logger.debug("Updating working hours for salon: ${command.salonId.value}")

        // Validate salon exists
        val salon =
            salonRepository.findById(command.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate updater has permission (must be chief groomer)
        val updaterStaff =
            staffRepository.findByUserIdAndSalonId(
                command.updaterUserId,
                command.salonId,
            )
        if (updaterStaff == null || updaterStaff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException(
                "Nu aveți permisiunea să modificați programul de lucru al acestui salon",
            )
        }

        // Get existing working hours or create default
        val existingWorkingHours =
            workingHoursRepository.findBySalonId(command.salonId)
                ?: WorkingHoursSettings.createDefault(command.salonId)

        // Update working hours
        val updatedWorkingHours =
            existingWorkingHours
                .updateWeeklySchedule(command.weeklySchedule)
                .updateHolidays(command.holidays)
                .updateCustomClosures(command.customClosures)

        // Save updated working hours
        val savedWorkingHours = workingHoursRepository.save(updatedWorkingHours)
        logger.debug("Working hours updated for salon: ${command.salonId.value}")

        // Propagate changes to staff schedules that have inheritance enabled
        try {
            propagateBusinessHoursToStaff(command.salonId, savedWorkingHours)
        } catch (e: Exception) {
            logger.warn("Failed to propagate business hours to staff schedules: ${e.message}", e)
            // Don't fail the business hours update if staff propagation fails
        }

        return savedWorkingHours
    }

    /**
     * Propagate business hours changes to staff schedules that have inheritance enabled
     */
    private fun propagateBusinessHoursToStaff(
        salonId: SalonId,
        businessHours: WorkingHoursSettings,
    ) {
        logger.debug("Propagating business hours changes to staff in salon: ${salonId.value}")

        // Get all staff working hours for this salon
        val allStaffWorkingHours = staffWorkingHoursRepository.findBySalonId(salonId)

        // Update each staff member's schedule if they have inheritance enabled
        val updatedCount =
            allStaffWorkingHours.count { staffHours ->
                if (staffHours.inheritFromBusiness) {
                    try {
                        logger.debug(
                            "Updating staff ${staffHours.staffId.value} schedule to inherit from business hours",
                        )
                        val updatedStaffHours = staffHours.inheritFromBusinessHours(businessHours)
                        staffWorkingHoursRepository.save(updatedStaffHours)
                        true
                    } catch (e: Exception) {
                        logger.error("Failed to update staff ${staffHours.staffId.value} schedule: ${e.message}", e)
                        false
                    }
                } else {
                    logger.debug("Skipping staff ${staffHours.staffId.value} - inheritance disabled")
                    false
                }
            }

        logger.info("Propagated business hours to {} staff members in salon {}", updatedCount, salonId.value)
    }
}
