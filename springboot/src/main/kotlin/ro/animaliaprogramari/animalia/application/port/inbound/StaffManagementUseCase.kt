package ro.animaliaprogramari.animalia.application.port.inbound

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

/**
 * Use case interface for staff management operations
 * Provides business operations for staff lifecycle and management
 */
interface StaffManagementUseCase {
    /**
     * Add new staff member to salon
     */
    fun addStaffMember(command: AddStaffMemberCommand): Staff

    /**
     * Update staff member information
     */
    fun updateStaffMember(command: UpdateStaffMemberCommand): Staff

    /**
     * Update staff role with authorization checks
     */
    fun updateStaffRole(command: UpdateStaffRoleCommand): Staff

    /**
     * Update staff working hours
     */
    fun updateWorkingHours(command: UpdateWorkingHoursCommand): Staff

    /**
     * Add specialization to staff member
     */
    fun addSpecialization(command: AddSpecializationCommand): Staff

    /**
     * Remove specialization from staff member
     */
    fun removeSpecialization(command: RemoveSpecializationCommand): Staff

    /**
     * Deactivate staff member
     */
    fun deactivateStaff(command: DeactivateStaffCommand): Staff

    /**
     * Activate staff member
     */
    fun activateStaff(command: ActivateStaffCommand): Staff

    /**
     * Get staff member by ID
     */
    fun getStaffById(query: GetStaffByIdQuery): Staff

    /**
     * Get all active staff members in salon
     */
    fun getActiveSalonStaff(query: GetActiveSalonStaffQuery): List<Staff>

    /**
     * Get staff members by role with pagination
     */
    fun getStaffByRole(query: GetStaffByRoleQuery): Page<Staff>

    /**
     * Find available staff for appointment scheduling
     */
    fun findAvailableStaff(query: FindAvailableStaffQuery): List<Staff>

    /**
     * Get staff workload report
     */
    fun getStaffWorkloadReport(query: GetStaffWorkloadQuery): StaffWorkloadReport

    /**
     * Get staff performance metrics
     */
    fun getStaffPerformance(query: GetStaffPerformanceQuery): StaffPerformanceReport

    /**
     * Update staff performance metrics (automated)
     */
    fun updatePerformanceMetrics(command: UpdatePerformanceMetricsCommand)

    /**
     * Get staff availability for a specific period
     */
    fun getStaffAvailability(query: GetStaffAvailabilityQuery): StaffAvailabilityReport

    /**
     * Assign staff to appointment
     */
    fun assignStaffToAppointment(command: AssignStaffCommand): Staff

    /**
     * Get staff schedule for a period
     */
    fun getStaffSchedule(query: GetStaffScheduleQuery): StaffScheduleReport
}

/**
 * Commands for staff management operations
 */
data class AddStaffMemberCommand(
    val userId: UserId,
    val salonId: SalonId,
    val role: StaffRole,
    val workingHours: WorkingHours,
    val hireDate: LocalDate,
    val specializations: Set<Specialization> = emptySet(),
    val requesterId: UserId,
)

data class UpdateStaffMemberCommand(
    val staffId: StaffId,
    val workingHours: WorkingHours? = null,
    val specializations: Set<Specialization>? = null,
    val requesterId: UserId,
)

data class UpdateStaffRoleCommand(
    val staffId: StaffId,
    val newRole: StaffRole,
    val requesterId: UserId,
)

data class UpdateWorkingHoursCommand(
    val staffId: StaffId,
    val workingHours: WorkingHours,
    val requesterId: UserId,
)

data class AddSpecializationCommand(
    val staffId: StaffId,
    val specialization: Specialization,
    val requesterId: UserId,
)

data class RemoveSpecializationCommand(
    val staffId: StaffId,
    val specialization: Specialization,
    val requesterId: UserId,
)

data class DeactivateStaffCommand(
    val staffId: StaffId,
    val reason: String? = null,
    val requesterId: UserId,
)

data class ActivateStaffCommand(
    val staffId: StaffId,
    val requesterId: UserId,
)

data class UpdatePerformanceMetricsCommand(
    val staffId: StaffId,
    val performance: StaffPerformance,
    val requesterId: UserId,
)

data class AssignStaffCommand(
    val appointmentId: AppointmentId,
    val staffId: StaffId,
    val requesterId: UserId,
)

/**
 * Queries for staff information retrieval
 */
data class GetStaffByIdQuery(
    val staffId: StaffId,
    val requesterId: UserId,
)

data class GetActiveSalonStaffQuery(
    val salonId: SalonId,
    val requesterId: UserId,
)

data class GetStaffByRoleQuery(
    val salonId: SalonId,
    val role: StaffRole,
    val pageable: Pageable,
    val requesterId: UserId,
)

data class FindAvailableStaffQuery(
    val salonId: SalonId,
    val date: LocalDate,
    val timeSlot: TimeSlot,
    val serviceCategory: ServiceCategory? = null,
    val excludeStaffIds: List<StaffId> = emptyList(),
    val requesterId: UserId,
)

data class GetStaffWorkloadQuery(
    val salonId: SalonId,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val staffIds: List<StaffId>? = null,
    val requesterId: UserId,
)

data class GetStaffPerformanceQuery(
    val staffId: StaffId,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val requesterId: UserId,
)

data class GetStaffAvailabilityQuery(
    val staffId: StaffId,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val requesterId: UserId,
)

data class GetStaffScheduleQuery(
    val staffId: StaffId,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val requesterId: UserId,
)

/**
 * Response objects for staff queries
 */
data class StaffWorkloadReport(
    val salonId: SalonId,
    val period: DateRange,
    val staffMetrics: List<StaffWorkloadMetrics>,
    val totalMetrics: WorkloadMetrics,
)

data class StaffWorkloadMetrics(
    val staff: Staff,
    val metrics: WorkloadMetrics,
    val appointments: List<Appointment>,
)

data class StaffPerformanceReport(
    val staff: Staff,
    val period: DateRange,
    val performance: StaffPerformance,
    val trends: PerformanceTrends,
    val recommendations: List<String>,
)

data class PerformanceTrends(
    val ratingTrend: TrendDirection,
    val appointmentTrend: TrendDirection,
    val satisfactionTrend: TrendDirection,
)

enum class TrendDirection {
    IMPROVING,
    STABLE,
    DECLINING,
}

data class StaffAvailabilityReport(
    val staff: Staff,
    val period: DateRange,
    val availableSlots: List<AvailableTimeSlot>,
    val bookedSlots: List<BookedTimeSlot>,
    val utilizationRate: Double,
)

data class AvailableTimeSlot(
    val date: LocalDate,
    val timeSlot: TimeSlot,
)

data class BookedTimeSlot(
    val date: LocalDate,
    val timeSlot: TimeSlot,
    val appointmentId: AppointmentId,
)

data class StaffScheduleReport(
    val staff: Staff,
    val period: DateRange,
    val workingDays: List<WorkingDay>,
    val totalHours: Int,
    val appointments: List<Appointment>,
)

data class WorkingDay(
    val date: LocalDate,
    val schedule: DaySchedule,
    val appointments: List<Appointment>,
)
