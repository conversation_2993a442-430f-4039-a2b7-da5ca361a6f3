package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SalonSubscription
import ro.animaliaprogramari.animalia.domain.model.SalonSubscriptionId
import ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier
import java.time.LocalDateTime

/**
 * Repository interface for salon subscription persistence operations
 */
interface SalonSubscriptionRepository {
    
    /**
     * Save a salon subscription
     */
    fun save(subscription: SalonSubscription): SalonSubscription
    
    /**
     * Find subscription by ID
     */
    fun findById(id: SalonSubscriptionId): SalonSubscription?
    
    /**
     * Find active subscription for a salon
     */
    fun findActiveBySalonId(salonId: SalonId): SalonSubscription?
    
    /**
     * Find all subscriptions for a salon
     */
    fun findAllBySalonId(salonId: SalonId): List<SalonSubscription>
    
    /**
     * Find subscriptions by status
     */
    fun findByStatus(status: SubscriptionStatus): List<SalonSubscription>
    
    /**
     * Find subscriptions by tier
     */
    fun findByTier(tier: SubscriptionTier): List<SalonSubscription>
    
    /**
     * Find subscriptions that need billing (next billing date is due)
     */
    fun findSubscriptionsForBilling(beforeDate: LocalDateTime): List<SalonSubscription>
    
    /**
     * Find expired subscriptions
     */
    fun findExpiredSubscriptions(): List<SalonSubscription>
    
    /**
     * Find subscriptions ending trial period
     */
    fun findTrialEndingSubscriptions(beforeDate: LocalDateTime): List<SalonSubscription>
    
    /**
     * Check if salon has any active subscription
     */
    fun hasActiveSubscription(salonId: SalonId): Boolean
    
    /**
     * Delete subscription by ID
     */
    fun deleteById(id: SalonSubscriptionId)
    
    /**
     * Count subscriptions by tier
     */
    fun countByTier(tier: SubscriptionTier): Long
    
    /**
     * Count active subscriptions
     */
    fun countActiveSubscriptions(): Long
}
