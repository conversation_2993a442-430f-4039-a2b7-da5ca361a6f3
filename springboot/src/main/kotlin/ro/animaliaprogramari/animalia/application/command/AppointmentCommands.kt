package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime

/**
 * Command to schedule a new appointment
 */
data class ScheduleAppointmentCommand(
    val salonId: SalonId,
    val clientId: ClientId,
    val clientName: String?,
    val clientPhone: PhoneNumber?,
    val petId: PetId?, // Made optional - will be generated if null
    val petName: String?,
    val petSpecies: String?,
    val petBreed: String?,
    val petSize: String?,
    val staffId: StaffId, // Changed from userId to staffId
    val appointmentDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val serviceIds: List<ServiceId>,
    val notes: String? = null,
    val repetitionFrequency: RepetitionFrequency? = null,
    val isNewClient: Boolean = false,
    val isNewPet: Boolean = false,
)

/**
 * Command to update an existing appointment
 */
data class UpdateAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val clientId: ClientId? = null,
    val petId: PetId? = null,
    val staffId: StaffId? = null,
    val appointmentDate: LocalDate? = null,
    val startTime: LocalTime? = null,
    val endTime: LocalTime? = null,
    val serviceIds: List<ServiceId>? = null,
    val notes: String? = null,
    val repetitionFrequency: RepetitionFrequency? = null,
    val updaterUserId: UserId,
)

/**
 * Command to cancel an appointment
 */
data class CancelAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val reason: String? = null,
    val cancellerUserId: UserId,
)

/**
 * Command to complete an appointment
 */
data class CompleteAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val notes: String? = null,
    val actualServices: List<ServiceId>? = null,
    val completerUserId: UserId,
)

/**
 * Command to reschedule an appointment
 */
data class RescheduleAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val newDate: LocalDate? = null,
    val newStartTime: LocalTime,
    val newEndTime: LocalTime,
    val newstaffId: StaffId? = null,
    val reason: String? = null,
    val reschedulerUserId: UserId,
)

/**
 * Command to mark appointment as no-show
 */
data class MarkNoShowCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val noShowNotes: String? = null,
    val notifyClient: Boolean = true,
    val markerUserId: UserId,
)

/**
 * Command to mark appointment as in-progress
 */
data class MarkInProgressCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val markerUserId: UserId,
)

/**
 * Command to create multiple appointments in batch
 */
data class CreateBulkAppointmentsCommand(
    val salonId: SalonId,
    val appointments: List<ScheduleAppointmentCommand>,
    val creatorUserId: UserId,
)

/**
 * Command to delete an appointment
 */
data class DeleteAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val deleterUserId: UserId,
)
