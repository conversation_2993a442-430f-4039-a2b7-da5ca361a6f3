package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.UpdateStaffWorkingHoursCommand
import ro.animaliaprogramari.animalia.application.port.inbound.StaffWorkingHoursManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.application.query.GetBatchStaffWorkingHoursQuery
import ro.animaliaprogramari.animalia.application.query.GetStaffAvailabilityQuery
import ro.animaliaprogramari.animalia.application.query.GetStaffWorkingHoursQuery
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Implementation of staff working hours management use case
 * Contains the business logic for staff working hours operations
 */
@Service
@Transactional
class StaffWorkingHoursManagementUseCaseImpl(
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val staffRepository: StaffRepository,
    private val appointmentRepository: AppointmentRepository,
    private val workingHoursRepository: WorkingHoursRepository,
) : StaffWorkingHoursManagementUseCase {
    private val logger = LoggerFactory.getLogger(StaffWorkingHoursManagementUseCaseImpl::class.java)

    override fun getStaffWorkingHours(query: GetStaffWorkingHoursQuery): StaffWorkingHoursSettings {
        logger.debug("Getting working hours for staff: ${query.staffId.value} in salon: ${query.salonId.value}")

        // Validate staff exists
        staffRepository.findById(StaffId.of(query.staffId.value))
            ?: throw EntityNotFoundException("Staff not found: ${query.staffId.value}")

        // Check permissions
        validatePermissions(query.requesterId, query.staffId, query.salonId, "view")

        // Get working hours or create default if not exists
        val workingHours =
            staffWorkingHoursRepository.findByStaffIdAndSalonId(query.staffId, query.salonId)
                ?: StaffWorkingHoursSettings.createDefault(query.staffId, query.salonId)

        logger.debug("Working hours retrieved for staff: ${query.staffId.value}")
        return workingHours
    }

    override fun updateStaffWorkingHours(command: UpdateStaffWorkingHoursCommand): StaffWorkingHoursSettings {
        logger.debug("Updating working hours for staff: ${command.staffId.value} in salon: ${command.salonId.value}")

        // Validate staff exists
        staffRepository.findById(command.staffId) ?: throw EntityNotFoundException("Staff not found: ${command.staffId.value}")

        // Check permissions
        validatePermissions(command.updaterUserId, command.staffId, command.salonId, "modify")

        // Get business working hours for validation
        val businessHours =
            workingHoursRepository.findBySalonId(command.salonId)

        // Get existing working hours or create default
        val existingWorkingHours =
            staffWorkingHoursRepository.findByStaffIdAndSalonId(command.staffId, command.salonId)
                ?: StaffWorkingHoursSettings.createDefault(command.staffId, command.salonId)

        // Create updated working hours
        val updatedWorkingHours =
            existingWorkingHours
                .updateWeeklySchedule(command.weeklySchedule)
                .updateHolidays(command.holidays)
                .updateCustomClosures(command.customClosures)
                .updateInheritance(command.inheritFromBusiness)

        // Validate against business hours constraints
        val violations = updatedWorkingHours.validateAgainstBusinessHours(businessHours)
        if (violations.isNotEmpty()) {
            logger.warn("Staff schedule validation failed: {}", violations)
            throw BusinessRuleViolationException(
                "Staff schedule violates business hours constraints: ${violations.joinToString("; ")}",
            )
        }

        // Save updated working hours
        val savedWorkingHours = staffWorkingHoursRepository.save(updatedWorkingHours)
        logger.debug("Working hours updated for staff: ${command.staffId.value}")

        return savedWorkingHours
    }

    override fun resetStaffWorkingHours(
        staffId: String,
        salonId: String,
        requesterId: String,
    ): StaffWorkingHoursSettings {
        logger.debug("Resetting working hours for staff: $staffId in salon: $salonId")

        val staffIdObj = StaffId.of(staffId)
        val salonIdObj = SalonId.of(salonId)
        val requesterIdObj = UserId.of(requesterId)

        // ✅ FIXED: Validate staff exists using staffId instead of userId
        staffRepository.findById(staffIdObj)
            ?: throw EntityNotFoundException("Staff not found: $staffId")

        // Only CHIEF_GROOMER can reset staff schedules
        val requesterStaff = staffRepository.findByUserIdAndSalonId(requesterIdObj, salonIdObj)
        if (requesterStaff?.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("Only CHIEF_GROOMER can reset staff schedules")
        }

        // Create default working hours
        val defaultWorkingHours = StaffWorkingHoursSettings.createDefault(staffIdObj, salonIdObj)

        // Save default working hours
        val savedWorkingHours = staffWorkingHoursRepository.save(defaultWorkingHours)
        logger.debug("Working hours reset for staff: $staffId")

        return savedWorkingHours
    }

    override fun getBatchStaffWorkingHours(query: GetBatchStaffWorkingHoursQuery): List<StaffWorkingHoursSettings> {
        logger.debug("Getting batch working hours for ${query.staffIds.size} staff members in salon: ${query.salonId.value}")

        // Validate requester has permission to view staff working hours
        val requesterStaff = staffRepository.findByUserIdAndSalonId(query.requesterId, query.salonId)
            ?: throw BusinessRuleViolationException("Nu aveți acces la acest salon")

        // Only CHIEF_GROOMER can view all staff working hours
//        if (requesterStaff.role != StaffRole.CHIEF_GROOMER) {
//            throw BusinessRuleViolationException("Nu aveți permisiunea să accesați programele angajaților")
//        }

        // Validate all staff IDs exist and belong to the salon
        val existingStaff = staffRepository.findBySalonId(query.salonId)
        val existingStaffIds = existingStaff.map { it.id }.toSet()

        val invalidStaffIds = query.staffIds.filter { it !in existingStaffIds }
        if (invalidStaffIds.isNotEmpty()) {
            throw EntityNotFoundException("Staff not found: ${invalidStaffIds.map { it.value }}")
        }

        // Get existing working hours for the requested staff
        val existingWorkingHours = staffWorkingHoursRepository.findByStaffIdsAndSalonId(query.staffIds, query.salonId)
        val existingWorkingHoursMap = existingWorkingHours.associateBy { it.staffId }

        // Create default working hours for staff that don't have them yet
        val allWorkingHours = query.staffIds.map { staffId ->
            existingWorkingHoursMap[staffId]
                ?: StaffWorkingHoursSettings.createDefault(staffId, query.salonId)
        }

        logger.debug("Retrieved working hours for ${allWorkingHours.size} staff members")
        return allWorkingHours
    }

    /**
     * Create staff working hours that inherit from business hours
     */
    fun createStaffWorkingHoursFromBusiness(
        staffId: StaffId,
        salonId: SalonId,
    ): StaffWorkingHoursSettings {
        logger.debug(
            "Creating staff working hours from business hours for staff: ${staffId.value} in salon: ${salonId.value}",
        )

        // Get business working hours
        val businessHours =
            workingHoursRepository.findBySalonId(salonId)

        // Create staff schedule inheriting from business
        val staffWorkingHours = StaffWorkingHoursSettings.createFromBusinessHours(staffId, salonId, businessHours)

        // Save the new schedule
        val savedWorkingHours = staffWorkingHoursRepository.save(staffWorkingHours)
        logger.debug("Staff working hours created from business hours for staff: ${staffId.value}")

        return savedWorkingHours
    }

    /**
     * Update all staff schedules when business hours change
     */
    fun propagateBusinessHoursToAllStaff(
        salonId: SalonId,
        updaterUserId: UserId,
    ): List<StaffWorkingHoursSettings> {
        logger.debug("Propagating business hours changes to all staff in salon: ${salonId.value}")

        // Validate updater has permission (must be chief groomer)
        val updaterStaff = staffRepository.findByUserIdAndSalonId(updaterUserId, salonId)
        if (updaterStaff == null || updaterStaff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("Only CHIEF_GROOMER can propagate business hours to staff")
        }

        // Get business working hours
        val businessHours =
            workingHoursRepository.findBySalonId(salonId)

        // Get all staff working hours for this salon
        val allStaffWorkingHours = staffWorkingHoursRepository.findBySalonId(salonId)

        // Update each staff member's schedule if they have inheritance enabled
        val updatedSchedules =
            allStaffWorkingHours.mapNotNull { staffHours ->
                if (staffHours.inheritFromBusiness) {
                    logger.debug("Updating staff ${staffHours.staffId.value} schedule to inherit from business hours")
                    val updatedStaffHours = staffHours.inheritFromBusinessHours(businessHours)
                    staffWorkingHoursRepository.save(updatedStaffHours)
                } else {
                    logger.debug("Skipping staff ${staffHours.staffId.value} - inheritance disabled")
                    null
                }
            }

        logger.debug("Propagated business hours to {} staff members", updatedSchedules.size)
        return updatedSchedules
    }

    override fun getStaffAvailability(query: GetStaffAvailabilityQuery): StaffAvailabilityReport {
        logger.debug("Getting staff availability for salon: ${query.salonId.value} on date: ${query.date}")

        // Get all active staff for the salon
        val allStaff = staffRepository.findActiveBySalonWithUserDetails(query.salonId)

        val staffAvailability =
            allStaff.map { staff ->
                val staffId = staff.id // ✅ FIXED: Use staff.id instead of staff.userId

                // Get working hours for this staff member
                val workingHours =
                    staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, query.salonId)
                        ?: StaffWorkingHoursSettings.createDefault(staffId, query.salonId)

                // Check if staff is available on this date
                val isAvailableOnDate = workingHours.isAvailableOn(query.date)
                val daySchedule = workingHours.getWorkingHoursFor(query.date)

                if (!isAvailableOnDate) {
                    StaffMemberAvailability(
                        staff = staff,
                        isAvailable = false,
                        workingHours = null,
                        conflictingAppointments = emptyList(),
                        reason = "Day off",
                    )
                } else {
                    // Check for conflicting appointments
                    val existingAppointments =
                        appointmentRepository.findBySalonIdAndStaffIdAndDate(
                            query.salonId,
                            staff.id,
                            query.date,
                        )

                    val conflictingAppointments =
                        if (query.startTime != null && query.endTime != null) {
                            existingAppointments.filter { appointment ->
                                // Check if appointment overlaps with requested time
                                !(appointment.endTime.isBefore(query.startTime) || appointment.startTime.isAfter(query.endTime))
                            }
                        } else {
                            existingAppointments
                        }

                    val isAvailable = conflictingAppointments.isEmpty()
                    val reason = if (!isAvailable) "Conflicting appointment" else null

                    StaffMemberAvailability(
                        staff = staff,
                        isAvailable = isAvailable,
                        workingHours = daySchedule,
                        conflictingAppointments = conflictingAppointments,
                        reason = reason,
                    )
                }
            }

        return StaffAvailabilityReport(
            date = query.date,
            staffAvailability = staffAvailability,
        )
    }

    private fun validatePermissions(
        requesterId: UserId,
        staffId: StaffId,
        salonId: SalonId,
        action: String,
    ) {
        val requesterStaff =
            staffRepository.findByUserIdAndSalonId(requesterId, salonId)
                ?: throw BusinessRuleViolationException("Nu aveți acces la acest salon")

        // For viewing, any staff member in the salon can view any other staff's schedule
        println("Requester staff: $requesterStaff - Action: $action")
        if (action == "view") {
            return
        }

        // For modification, CHIEF_GROOMER can modify any staff schedule
        if (requesterStaff.role == StaffRole.CHIEF_GROOMER) {
            return
        }

        // Staff member can only modify their own schedule
        if (requesterStaff.id == staffId) {
            return
        }

        throw BusinessRuleViolationException(
            "Nu aveți permisiunea să modificați programul acestui angajat"
        )
    }
}
