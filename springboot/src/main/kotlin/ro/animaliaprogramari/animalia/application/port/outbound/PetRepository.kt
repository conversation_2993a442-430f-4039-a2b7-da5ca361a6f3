package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Pet
import ro.animaliaprogramari.animalia.domain.model.PetId

/**
 * Outbound port for pet persistence
 */
interface PetRepository {
    /**
     * Save a pet
     */
    fun save(pet: Pet): Pet

    /**
     * Find a pet by ID
     */
    fun findById(id: PetId): Pet?

    /**
     * Find all pets for a client
     */
    fun findByClientId(
        clientId: ClientId,
        activeOnly: Boolean = false,
    ): List<Pet>

    /**
     * Find all pets with optional filtering
     */
    fun findAll(
        search: String? = null,
        clientId: ClientId? = null,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<Pet>

    /**
     * Check if a pet exists by ID
     */
    fun existsById(id: PetId): Boolean

    /**
     * Delete a pet by ID
     */
    fun deleteById(id: PetId): <PERSON><PERSON>an

    /**
     * Count total pets with optional filtering
     */
    fun count(
        search: String? = null,
        clientId: ClientId? = null,
        isActive: Boolean? = null,
    ): Long
}
