package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.command.UpdateNotificationSettingsCommand
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationSettingsManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.query.GetNotificationSettingsQuery
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.NotificationSettings

/**
 * Implementation of notification settings management use case
 * Contains the business logic for notification settings operations
 */
@Service
class NotificationSettingsManagementUseCaseImpl(
    private val notificationSettingsRepository: NotificationSettingsRepository,
    private val salonRepository: SalonRepository,
) : NotificationSettingsManagementUseCase {
    private val logger = LoggerFactory.getLogger(NotificationSettingsManagementUseCaseImpl::class.java)

    override fun getNotificationSettings(query: GetNotificationSettingsQuery): NotificationSettings {
        logger.debug("Getting notification settings for user: ${query.userId.value} in salon: ${query.salonId.value}")

        // Verify salon exists
        salonRepository.findById(query.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${query.salonId.value}")

        // Get existing settings or create default ones
        val settings =
            notificationSettingsRepository.findByUserIdAndSalonId(query.userId, query.salonId)
                ?: run {
                    logger.debug("No notification settings found for user ${query.userId.value} in salon ${query.salonId.value}, creating default settings")
                    val defaultSettings = NotificationSettings.createDefault(query.userId, query.salonId)
                    notificationSettingsRepository.save(defaultSettings)
                }

        logger.debug("Retrieved notification settings for user: ${query.userId.value} in salon: ${query.salonId.value}")
        return settings
    }

    override fun updateNotificationSettings(command: UpdateNotificationSettingsCommand): NotificationSettings {
        logger.debug("Updating notification settings for user: ${command.userId.value} in salon: ${command.salonId.value}")

        // Verify salon exists
        salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Get existing settings or create default ones
        val existingSettings =
            notificationSettingsRepository.findByUserIdAndSalonId(command.userId, command.salonId)
                ?: NotificationSettings.createDefault(command.userId, command.salonId)

        // Update settings
        val updatedSettings =
            existingSettings.updateSettings(
                pushNotificationsEnabled = command.pushNotificationsEnabled,
                soundPreference = command.soundPreference,
                vibrationEnabled = command.vibrationEnabled,
                doNotDisturb = command.doNotDisturb,
                notificationRules = command.notificationRules,
            )

        // Save updated settings
        val savedSettings = notificationSettingsRepository.save(updatedSettings)
        logger.debug("Updated notification settings for user: ${command.userId.value} in salon: ${command.salonId.value}")

        return savedSettings
    }
}
