package ro.animaliaprogramari.animalia.domain.exception

/**
 * Base class for all domain exceptions
 */
abstract class DomainException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

/**
 * Exception thrown when a business rule is violated
 */
class BusinessRuleViolationException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause)

/**
 * Exception thrown when an entity is not found
 */
class EntityNotFoundException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause) {
    constructor(entityType: String, entityId: String, cause: Throwable? = null) :
        this("$entityType with ID '$entityId' not found", cause)
}

/**
 * Exception thrown when user is not authorized to perform an action
 */
class UnauthorizedException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause)

/**
 * Exception thrown when trying to schedule an appointment at an unavailable time
 */
class TimeSlotUnavailableException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause)

/**
 * Exception thrown when an appointment cannot be modified due to its current state
 */
class AppointmentStateException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause)

/**
 * Exception thrown when a groomer is not available
 */
class GroomerUnavailableException(
    groomerId: String,
    reason: String,
    cause: Throwable? = null,
) : DomainException("Groomer '$groomerId' is not available: $reason", cause)

/**
 * Exception thrown when a staff member is not available
 */
class StaffUnavailableException(
    staffId: String,
    reason: String,
    cause: Throwable? = null,
) : DomainException("Staff member '$staffId' is not available: $reason", cause)

/**
 * Exception thrown when a client operation is invalid
 */
class ClientOperationException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause)

/**
 * Exception thrown when a pet operation is invalid
 */
class PetOperationException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause)

/**
 * Exception thrown when appointment scheduling conflicts occur
 */
class AppointmentSchedulingConflictException(
    message: String,
    val conflicts: List<ro.animaliaprogramari.animalia.domain.model.AppointmentConflictItem> = emptyList(),
    val alternatives: List<ro.animaliaprogramari.animalia.domain.model.AlternativeSuggestion> = emptyList(),
    cause: Throwable? = null,
) : Exception(message, cause)
