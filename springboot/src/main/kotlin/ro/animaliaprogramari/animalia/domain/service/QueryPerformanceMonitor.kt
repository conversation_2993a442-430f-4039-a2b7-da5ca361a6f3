package ro.animaliaprogramari.animalia.domain.service

import io.micrometer.core.annotation.Timed
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.util.concurrent.TimeUnit

/**
 * Service for monitoring database query performance
 * Tracks execution times and identifies slow queries
 */
@Service
class QueryPerformanceMonitor(
    private val meterRegistry: MeterRegistry,
) {
    private val logger = LoggerFactory.getLogger(QueryPerformanceMonitor::class.java)

    companion object {
        private const val SLOW_QUERY_THRESHOLD_MS = 100L
        private const val VERY_SLOW_QUERY_THRESHOLD_MS = 500L
    }

    /**
     * Monitor a database operation and record its performance
     */
    @Timed(value = "database.query.execution", description = "Database query execution time")
    fun <T> monitorQuery(
        queryName: String,
        queryType: QueryType,
        operation: () -> T,
    ): T {
        val startTime = Instant.now()
        val timer = Timer.start(meterRegistry)

        return try {
            val result = operation()
            val duration = Duration.between(startTime, Instant.now())

            // Record metrics
            timer.stop(
                Timer.builder("database.query.duration")
                    .tag("query.name", queryName)
                    .tag("query.type", queryType.name)
                    .tag("status", "success")
                    .register(meterRegistry)
            )

            // Log performance warnings
            logPerformanceWarnings(queryName, queryType, duration)

            result
        } catch (exception: Exception) {
            val duration = Duration.between(startTime, Instant.now())

            // Record error metrics
            timer.stop(
                Timer.builder("database.query.duration")
                    .tag("query.name", queryName)
                    .tag("query.type", queryType.name)
                    .tag("status", "error")
                    .register(meterRegistry)
            )

            logger.error(
                "Database query failed: {} ({}ms) - {}",
                queryName,
                duration.toMillis(),
                exception.message
            )

            throw exception
        }
    }

    /**
     * Monitor batch operations
     */
    fun <T> monitorBatchQuery(
        queryName: String,
        batchSize: Int,
        operation: () -> T,
    ): T {
        return monitorQuery("${queryName}_batch_${batchSize}", QueryType.BATCH, operation)
    }

    /**
     * Monitor conflict detection queries
     */
    fun <T> monitorConflictDetection(
        staffId: String,
        operation: () -> T,
    ): T {
        return monitorQuery("conflict_detection_staff_${staffId}", QueryType.CONFLICT_DETECTION, operation)
    }

    /**
     * Monitor appointment suggestion queries
     */
    fun <T> monitorSuggestionQuery(
        salonId: String,
        staffCount: Int,
        operation: () -> T,
    ): T {
        return monitorQuery(
            "appointment_suggestions_salon_${salonId}_staff_${staffCount}",
            QueryType.SUGGESTION,
            operation
        )
    }

    private fun logPerformanceWarnings(
        queryName: String,
        queryType: QueryType,
        duration: Duration,
    ) {
        val durationMs = duration.toMillis()

        when {
            durationMs >= VERY_SLOW_QUERY_THRESHOLD_MS -> {
                logger.warn(
                    "VERY SLOW QUERY DETECTED: {} ({}) took {}ms - REQUIRES IMMEDIATE OPTIMIZATION",
                    queryName,
                    queryType,
                    durationMs
                )
            }
            durationMs >= SLOW_QUERY_THRESHOLD_MS -> {
                logger.warn(
                    "Slow query detected: {} ({}) took {}ms - consider optimization",
                    queryName,
                    queryType,
                    durationMs
                )
            }
            else -> {
                logger.debug(
                    "Query executed: {} ({}) took {}ms",
                    queryName,
                    queryType,
                    durationMs
                )
            }
        }
    }

    /**
     * Get performance statistics for a query
     */
    fun getQueryStatistics(queryName: String): QueryStatistics? {
        val timer = meterRegistry.find("database.query.duration")
            .tag("query.name", queryName)
            .timer()

        return timer?.let {
            QueryStatistics(
                queryName = queryName,
                count = it.count(),
                totalTimeMs = it.totalTime(TimeUnit.MILLISECONDS).toLong(),
                averageTimeMs = it.mean(TimeUnit.MILLISECONDS),
                maxTimeMs = it.max(TimeUnit.MILLISECONDS)
            )
        }
    }

    /**
     * Get all slow queries above threshold
     */
    fun getSlowQueries(): List<QueryStatistics> {
        return meterRegistry.find("database.query.duration")
            .timers()
            .mapNotNull { timer ->
                val queryName = timer.id.getTag("query.name") ?: "unknown"
                if (timer.mean(TimeUnit.MILLISECONDS) >= SLOW_QUERY_THRESHOLD_MS) {
                    QueryStatistics(
                        queryName = queryName,
                        count = timer.count(),
                        totalTimeMs = timer.totalTime(TimeUnit.MILLISECONDS).toLong(),
                        averageTimeMs = timer.mean(TimeUnit.MILLISECONDS),
                        maxTimeMs = timer.max(TimeUnit.MILLISECONDS)
                    )
                } else null
            }
            .sortedByDescending { it.averageTimeMs }
    }
}

/**
 * Types of database queries for categorization
 */
enum class QueryType {
    SELECT,
    INSERT,
    UPDATE,
    DELETE,
    BATCH,
    CONFLICT_DETECTION,
    SUGGESTION,
    WORKING_HOURS,
    NOTIFICATION
}

/**
 * Query performance statistics
 */
data class QueryStatistics(
    val queryName: String,
    val count: Long,
    val totalTimeMs: Long,
    val averageTimeMs: Double,
    val maxTimeMs: Double,
) {
    val isSlowQuery: Boolean
        get() = averageTimeMs >= 100.0

    val isVerySlowQuery: Boolean
        get() = averageTimeMs >= 500.0
}
