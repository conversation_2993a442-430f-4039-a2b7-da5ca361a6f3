package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing a salon
 * Pure domain model with no infrastructure dependencies
 */
data class Salon(
    val id: SalonId,
    val name: String,
    val address: String?,
    val city: String?,
    val phone: PhoneNumber?,
    val email: Email?,
    val ownerId: UserId,
    val subscriptionPlan: SalonSubscriptionPlan = SalonSubscriptionPlan.FREELANCER,
    val isActive: Boolean = true,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val description: String?,
) {
    // Validation is now handled by ValidationService in the application layer
    // This keeps the domain model clean and validation logic centralized

    /**
     * Activate salon
     */
    fun activate(): Salon {
        return copy(
            isActive = true,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Deactivate salon
     */
    fun deactivate(): Salon {
        return copy(
            isActive = false,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Update subscription plan for the salon
     */
    fun updateSubscriptionPlan(plan: SalonSubscriptionPlan): Salon {
        return copy(
            subscriptionPlan = plan,
            updatedAt = LocalDateTime.now(),
        )
    }

    companion object {
        /**
         * Create a new salon
         */
        fun create(
            name: String,
            address: String? = null,
            city: String? = null,
            phone: PhoneNumber? = null,
            email: Email? = null,
            ownerId: UserId,
            description: String?,
            subscriptionPlan: SalonSubscriptionPlan = SalonSubscriptionPlan.FREELANCER,
        ): Salon {
            return Salon(
                id = SalonId.generate(),
                name = name,
                address = address,
                city = city,
                phone = phone,
                email = email,
                ownerId = ownerId,
                subscriptionPlan = subscriptionPlan,
                description = description,
            )
        }
    }
}
