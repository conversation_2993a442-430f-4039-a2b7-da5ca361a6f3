package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

/**
 * Optimized service for fast, accurate scheduling conflict detection
 * Provides clear, human-readable conflict explanations with minimal overhead
 */
@Service
class OptimizedSchedulingConflictService {
    private val logger = LoggerFactory.getLogger(OptimizedSchedulingConflictService::class.java)
    private val systemZone = ZoneId.systemDefault()

    // Cache for converted block times to avoid repeated timezone conversions
    private val blockTimeCache = mutableMapOf<String, TimeSlot>()

    // Pre-computed data structures for faster lookups
    data class ConflictCheckData(
        val appointmentsByStaffAndDate: Map<Pair<StaffId, LocalDate>, List<TimeSlot>>,
        val blocksByStaff: Map<StaffId, List<Pair<TimeSlot, BlockTime>>>,
    )

    /**
     * Detect all conflicts for an appointment request with optimized data structures
     * Returns detailed conflict information with human-readable explanations
     */
    fun detectConflicts(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        appointments: List<Appointment>,
        blocks: List<BlockTime>,
    ): List<DetailedScheduleConflict> {
        val conflicts = mutableListOf<DetailedScheduleConflict>()
        val requestedSlot = TimeSlot.of(startTime, endTime)

        logger.debug("Checking conflicts for {}, {}-{}, staff={}", date, startTime, endTime, staffId.value)

        // 1. Check salon availability (fast path - check closure first)
        checkSalonAvailability(date, requestedSlot, salonHours)?.let { conflicts.add(it) }

        // 2. Check staff availability (fast path - check closure first)
        checkStaffAvailability(date, requestedSlot, staffHours)?.let { conflicts.add(it) }

        // 3. Check appointment overlaps (only if staff/salon are available)
        if (conflicts.isEmpty()) {
            checkAppointmentOverlaps(date, requestedSlot, staffId, appointments)?.let { conflicts.add(it) }
        }

        // 4. Check block time overlaps (only if no other conflicts)
        if (conflicts.isEmpty()) {
            checkBlockTimeOverlaps(requestedSlot, staffId, blocks)?.let { conflicts.add(it) }
        }

        logConflictResults(conflicts, date, startTime, endTime)
        return conflicts
    }

    /**
     * Optimized conflict detection using pre-computed data structures
     * Use this when checking multiple time slots for the same staff/date
     */
    fun detectConflictsOptimized(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        conflictData: ConflictCheckData,
    ): List<DetailedScheduleConflict> {
        val conflicts = mutableListOf<DetailedScheduleConflict>()
        val requestedSlot = TimeSlot.of(startTime, endTime)

        // 1. Check salon availability (fast path)
        checkSalonAvailability(date, requestedSlot, salonHours)?.let { conflicts.add(it) }

        // 2. Check staff availability (fast path)
        checkStaffAvailability(date, requestedSlot, staffHours)?.let { conflicts.add(it) }

        // Early termination if basic availability fails
        if (conflicts.isNotEmpty()) return conflicts

        // 3. Check appointment overlaps using pre-computed data (O(1) lookup)
        val staffAppointments = conflictData.appointmentsByStaffAndDate[Pair(staffId, date)] ?: emptyList()
        val conflictingAppointment = staffAppointments.find { it.overlaps(requestedSlot) }

        conflictingAppointment?.let {
            conflicts.add(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Overlaps with existing appointment ${it.startTime}-${it.endTime}",
                    timeSlot = requestedSlot,
                ),
            )
        }

        // Early termination if appointment conflict found
        if (conflicts.isNotEmpty()) return conflicts

        // 4. Check block time overlaps using pre-computed data (O(1) lookup)
        val staffBlocks = conflictData.blocksByStaff[staffId] ?: emptyList()
        val conflictingBlock = staffBlocks.find { (blockSlot, _) -> blockSlot.overlaps(requestedSlot) }

        conflictingBlock?.let { (blockSlot, block) ->
            conflicts.add(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.BLOCK_TIME,
                    message = "Overlaps with blocked time ${blockSlot.startTime}-${blockSlot.endTime}: ${block.getDisplayReason()}",
                    timeSlot = requestedSlot,
                ),
            )
        }

        return conflicts
    }

    /**
     * Fast availability check without detailed conflict information
     */
    fun isSlotAvailable(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        appointments: List<Appointment>,
        blocks: List<BlockTime>,
    ): Boolean {
        return detectConflicts(
            date,
            startTime,
            endTime,
            staffId,
            salonHours,
            staffHours,
            appointments,
            blocks,
        ).isEmpty()
    }

    /**
     * Fast availability check using pre-computed data
     */
    fun isSlotAvailableOptimized(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        conflictData: ConflictCheckData,
    ): Boolean {
        return detectConflictsOptimized(
            date,
            startTime,
            endTime,
            staffId,
            salonHours,
            staffHours,
            conflictData,
        ).isEmpty()
    }

    /**
     * Build optimized data structures for batch conflict checking
     * Call this once and reuse the result for multiple conflict checks
     */
    fun buildOptimizedConflictData(
        appointments: List<Appointment>,
        blocks: List<BlockTime>,
    ): ConflictCheckData {
        return buildConflictCheckData(appointments, blocks)
    }

    /**
     * Clear the timezone conversion cache
     * Call this periodically to prevent memory leaks in long-running applications
     */
    fun clearCache() {
        blockTimeCache.clear()
    }

    // Private helper methods for specific conflict checks

    private fun checkSalonAvailability(
        date: LocalDate,
        requestedSlot: TimeSlot,
        salonHours: WorkingHoursSettings,
    ): DetailedScheduleConflict? {
        // Check if salon is closed on this date
        if (!salonHours.isOpenOn(date)) {
            val reason = getSalonClosureReason(date, salonHours)
            logger.error("SALON CLOSED: {} - {}", date, reason)
            return DetailedScheduleConflict(
                type = ScheduleConflictType.SALON_CLOSED,
                message = "Salon is closed on ${date.dayOfWeek.name.lowercase()}: $reason",
                timeSlot = requestedSlot,
            )
        }

        // Check if requested time is within salon working hours
        val salonDay = salonHours.getWorkingHoursFor(date)
        if (salonDay == null || !isTimeSlotAvailable(requestedSlot, salonDay)) {
            val workingHours = salonDay?.let { "${it.startTime}-${it.endTime}" } ?: "Not configured"
            val breakTime =
                salonDay?.let {
                    if (it.breakStart != null && it.breakEnd != null) "${it.breakStart}-${it.breakEnd}" else "None"
                } ?: "None"

            logger.error(
                "SALON HOURS CONFLICT: Requested {}-{} outside salon hours {} or during break {}",
                requestedSlot.startTime,
                requestedSlot.endTime,
                workingHours,
                breakTime,
            )

            return DetailedScheduleConflict(
                type = ScheduleConflictType.SALON_CLOSED,
                message = "Requested time ${requestedSlot.startTime}-${requestedSlot.endTime} is outside salon hours ($workingHours) or during break ($breakTime)",
                timeSlot = requestedSlot,
            )
        }

        return null
    }

    private fun checkStaffAvailability(
        date: LocalDate,
        requestedSlot: TimeSlot,
        staffHours: StaffWorkingHoursSettings,
    ): DetailedScheduleConflict? {
        // Check if staff is available on this date
        if (!staffHours.isAvailableOn(date)) {
            val reason = getStaffUnavailabilityReason(date, staffHours)
            logger.error("STAFF UNAVAILABLE: {} - {}", date, reason)
            return DetailedScheduleConflict(
                type = ScheduleConflictType.STAFF_UNAVAILABLE,
                message = "Staff is unavailable on ${date.dayOfWeek.name.lowercase()}: $reason",
                timeSlot = requestedSlot,
            )
        }

        // Check if requested time is within staff working hours
        val staffDay = staffHours.getWorkingHoursFor(date)
        if (staffDay == null || !isTimeSlotAvailable(requestedSlot, staffDay)) {
            val workingHours = staffDay?.let { "${it.startTime}-${it.endTime}" } ?: "Not configured"
            val breakTime =
                staffDay?.let {
                    if (it.breakStart != null && it.breakEnd != null) "${it.breakStart}-${it.breakEnd}" else "None"
                } ?: "None"

            logger.error(
                "STAFF HOURS CONFLICT: Requested {}-{} outside staff hours {} or during break {}",
                requestedSlot.startTime,
                requestedSlot.endTime,
                workingHours,
                breakTime,
            )

            return DetailedScheduleConflict(
                type = ScheduleConflictType.STAFF_UNAVAILABLE,
                message = "Requested time ${requestedSlot.startTime}-${requestedSlot.endTime} is outside staff hours ($workingHours) or during break ($breakTime)",
                timeSlot = requestedSlot,
            )
        }

        return null
    }

    private fun checkAppointmentOverlaps(
        date: LocalDate,
        requestedSlot: TimeSlot,
        staffId: StaffId,
        appointments: List<Appointment>,
    ): DetailedScheduleConflict? {
        val conflictingAppointment =
            appointments
                .filter { it.staffId == staffId && it.appointmentDate == date && it.isActive() }
                .find { TimeSlot.of(it.startTime, it.endTime).overlaps(requestedSlot) }

        return conflictingAppointment?.let { appt ->
            logger.error(
                "APPOINTMENT OVERLAP: Requested {}-{} overlaps with existing appointment {}-{} ({})",
                requestedSlot.startTime,
                requestedSlot.endTime,
                appt.startTime,
                appt.endTime,
                appt.status,
            )

            DetailedScheduleConflict(
                type = ScheduleConflictType.APPOINTMENT,
                message = "Overlaps with existing appointment ${appt.startTime}-${appt.endTime} (${appt.status})",
                timeSlot = requestedSlot,
                conflictingAppointmentId = appt.id,
            )
        }
    }

    private fun checkBlockTimeOverlaps(
        requestedSlot: TimeSlot,
        staffId: StaffId,
        blocks: List<BlockTime>,
    ): DetailedScheduleConflict? {
        val conflictingBlock =
            blocks
                .filter { it.isActive() && it.affectsStaff(staffId) }
                .find { block ->
                    // Use cached timezone conversion for better performance
                    val blockSlot = getBlockTimeSlot(block)
                    blockSlot.overlaps(requestedSlot)
                }

        return conflictingBlock?.let { block ->
            // Use cached conversion for display as well
            val blockSlot = getBlockTimeSlot(block)

            logger.error(
                "BLOCK TIME OVERLAP: Requested {}-{} overlaps with blocked time {}-{}: {}",
                requestedSlot.startTime,
                requestedSlot.endTime,
                blockSlot.startTime,
                blockSlot.endTime,
                block.getDisplayReason(),
            )

            DetailedScheduleConflict(
                type = ScheduleConflictType.BLOCK_TIME,
                message = "Overlaps with blocked time ${blockSlot.startTime}-${blockSlot.endTime}: ${block.getDisplayReason()}",
                timeSlot = requestedSlot,
            )
        }
    }

    // Helper methods

    /**
     * Convert block time to local timezone with caching to avoid repeated conversions
     */
    private fun getBlockTimeSlot(block: BlockTime): TimeSlot {
        val cacheKey = "${block.id.value}-${block.startTime}-${block.endTime}"
        return blockTimeCache.getOrPut(cacheKey) {
            val blockStartLocal = block.startTime.withZoneSameInstant(systemZone).toLocalTime()
            val blockEndLocal = block.endTime.withZoneSameInstant(systemZone).toLocalTime()
            TimeSlot.of(blockStartLocal, blockEndLocal)
        }
    }

    /**
     * Pre-process appointments into optimized data structures for faster lookups
     */
    private fun buildConflictCheckData(
        appointments: List<Appointment>,
        blocks: List<BlockTime>,
    ): ConflictCheckData {
        // Group appointments by staff and date for O(1) lookup
        val appointmentsByStaffAndDate =
            appointments
                .filter { it.isActive() }
                .groupBy { Pair(it.staffId, it.appointmentDate) }
                .mapValues { (_, appts) ->
                    appts.map { TimeSlot.of(it.startTime, it.endTime) }
                }

        // Group blocks by staff with pre-converted time slots
        val blocksByStaff =
            blocks
                .filter { it.isActive() }
                .flatMap { block ->
                    block.staffIds.map { staffId ->
                        staffId to Pair(getBlockTimeSlot(block), block)
                    }
                }
                .groupBy({ it.first }, { it.second })

        return ConflictCheckData(appointmentsByStaffAndDate, blocksByStaff)
    }

    private fun isTimeSlotAvailable(
        requestedSlot: TimeSlot,
        daySchedule: DaySchedule,
    ): Boolean {
        return daySchedule.isAvailableAt(requestedSlot.startTime) &&
            daySchedule.isAvailableAt(requestedSlot.endTime.minusMinutes(1))
    }

    private fun getSalonClosureReason(
        date: LocalDate,
        salonHours: WorkingHoursSettings,
    ): String {
        // Check custom closures first
        salonHours.customClosures.find { it.date == date }?.let { return it.reason }

        // Check holidays
        salonHours.holidays.find { it.date == date && !it.isWorkingDay }?.let { return it.name }

        // Check if it's a non-working day
        val daySchedule = salonHours.weeklySchedule[date.dayOfWeek]
        if (daySchedule == null || !daySchedule.isWorkingDay) {
            return "Non-working day"
        }

        return "Closed"
    }

    private fun getStaffUnavailabilityReason(
        date: LocalDate,
        staffHours: StaffWorkingHoursSettings,
    ): String {
        // Check custom closures first
        staffHours.customClosures.find { it.date == date }?.let { return it.reason }

        // Check holidays
        staffHours.holidays.find { it.date == date && !it.isWorkingDay }?.let { return it.name }

        // Check if it's a non-working day
        val daySchedule = staffHours.weeklySchedule[date.dayOfWeek]
        if (daySchedule == null || !daySchedule.isWorkingDay) {
            return "Non-working day"
        }

        return "Unavailable"
    }



    private fun logConflictResults(
        conflicts: List<DetailedScheduleConflict>,
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
    ) {
        if (conflicts.isEmpty()) {
            logger.debug("✅ AVAILABLE: {} {}-{}", date, startTime, endTime)
        } else {
            logger.warn("❌ CONFLICTS ({}): {} {}-{}", conflicts.size, date, startTime, endTime)
            conflicts.forEach { conflict ->
                logger.warn("   - {}: {}", conflict.type, conflict.message)
            }
        }
    }
}

data class ScheduleConflict(val type: ScheduleConflictType)

// Extension function to check if appointment is active
fun Appointment.isActive(): Boolean {
    return this.status in
        listOf(
            AppointmentStatus.SCHEDULED,
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.IN_PROGRESS,
        )
}
