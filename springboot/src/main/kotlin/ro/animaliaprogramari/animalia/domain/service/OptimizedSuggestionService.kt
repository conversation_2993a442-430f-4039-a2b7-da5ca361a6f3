package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaAppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.temporal.ChronoUnit

/**
 * Optimized appointment suggestion service with <100ms performance target
 * Uses SQL-based approach with minimal data transfer
 */
@Service
class OptimizedSuggestionService(
    private val appointmentRepository: JpaAppointmentRepository,
    private val staffRepository: StaffRepository,
    private val workingHoursRepository: WorkingHoursRepository,
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val conflictDetectionService: OptimizedConflictDetectionService,
    private val performanceMonitor: QueryPerformanceMonitor,
) {
    private val logger = LoggerFactory.getLogger(OptimizedSuggestionService::class.java)

    companion object {
        private const val DEFAULT_SUGGESTION_LIMIT = 10
        private const val MAX_DAYS_AHEAD = 14
        private const val TIME_SLOT_INTERVAL_MINUTES = 30L
    }

    /**
     * Find alternative appointment suggestions with optimized performance
     * Target: <100ms response time
     */
    fun findAlternativeSuggestionsOptimized(
        originalRequest: AppointmentRequest,
        limit: Int = DEFAULT_SUGGESTION_LIMIT,
    ): List<OptimizedSuggestion> {
        return performanceMonitor.monitorSuggestionQuery(
            originalRequest.salonId.value,
            1,
        ) {
            logger.debug("Finding optimized suggestions for salon {}", originalRequest.salonId.value)

            val suggestions = mutableListOf<OptimizedSuggestion>()
            val duration = ChronoUnit.MINUTES.between(originalRequest.startTime, originalRequest.endTime)

            // 1. Same staff, different times (highest priority)
            suggestions.addAll(
                findSameStaffAlternatives(originalRequest, duration, limit / 2)
            )

            // 2. Different staff, same time (medium priority)
            if (suggestions.size < limit) {
                suggestions.addAll(
                    findAlternativeStaffSuggestions(originalRequest, limit - suggestions.size)
                )
            }

            // 3. Different days (lower priority)
            if (suggestions.size < limit) {
                suggestions.addAll(
                    findAlternativeDaySuggestions(originalRequest, duration, limit - suggestions.size)
                )
            }

            logger.debug("Found {} optimized suggestions", suggestions.size)
            suggestions.take(limit)
        }
    }

    /**
     * Batch suggestion finding for multiple staff members
     */
    fun findBatchSuggestionsOptimized(
        salonId: SalonId,
        date: LocalDate,
        durationMinutes: Long,
        staffIds: List<StaffId>,
        limit: Int = DEFAULT_SUGGESTION_LIMIT,
    ): List<OptimizedSuggestion> {
        return performanceMonitor.monitorSuggestionQuery(salonId.value, staffIds.size) {
            logger.debug("Finding batch suggestions for {} staff members", staffIds.size)

            // Pre-load all appointments for the date range
            val endDate = date.plusDays(MAX_DAYS_AHEAD.toLong())
            val allAppointments = appointmentRepository.findAppointmentsForSuggestions(
                salonId = salonId,
                startDate = date,
                endDate = endDate,
                staffIds = staffIds
            )

            // Group appointments by staff for efficient lookup
            val appointmentsByStaff = allAppointments.groupBy { it.staffId }

            val suggestions = mutableListOf<OptimizedSuggestion>()

            // Generate time slots for each staff member
            staffIds.forEach { staffId ->
                if (suggestions.size < limit) {
                    val staffAppointments = appointmentsByStaff[staffId] ?: emptyList()
                    val staffSuggestions = findAvailableSlotsForStaff(
                        staffId = staffId,
                        salonId = salonId,
                        startDate = date,
                        endDate = endDate,
                        durationMinutes = durationMinutes,
                        existingAppointments = staffAppointments,
                        limit = limit - suggestions.size
                    )
                    suggestions.addAll(staffSuggestions)
                }
            }

            suggestions.sortedBy { it.priority }.take(limit)
        }
    }

    private fun findSameStaffAlternatives(
        originalRequest: AppointmentRequest,
        durationMinutes: Long,
        limit: Int,
    ): List<OptimizedSuggestion> {
        val suggestions = mutableListOf<OptimizedSuggestion>()
        val staff = staffRepository.findById(originalRequest.staffId) ?: return suggestions

        // Check same day, different times
        val timeSlots = generateTimeSlots(
            startTime = LocalTime.of(8, 0),
            endTime = LocalTime.of(18, 0),
            durationMinutes = durationMinutes
        ).filter { it.startTime != originalRequest.startTime }

        val conflictResults = conflictDetectionService.detectConflictsBatch(
            date = originalRequest.appointmentDate,
            timeSlots = timeSlots,
            staffId = originalRequest.staffId,
            salonId = originalRequest.salonId
        )

        conflictResults.filter { it.isAvailable }.take(limit).forEach { result ->
            suggestions.add(
                OptimizedSuggestion(
                    date = originalRequest.appointmentDate,
                    startTime = result.requestedSlot.startTime,
                    endTime = result.requestedSlot.endTime,
                    staffId = originalRequest.staffId,
                    staffName = staff.nickname ?: "Unknown",
                    priority = 1,
                    reason = "Same staff, different time",
                    confidence = 0.9
                )
            )
        }

        return suggestions
    }

    private fun findAlternativeStaffSuggestions(
        originalRequest: AppointmentRequest,
        limit: Int,
    ): List<OptimizedSuggestion> {
        val suggestions = mutableListOf<OptimizedSuggestion>()

        // Find other active staff in the salon
        val allStaff = staffRepository.findActiveBySalonWithUserDetails(originalRequest.salonId)
            .filter { it.id != originalRequest.staffId }

        allStaff.take(limit).forEach { staff ->
            val conflictResult = conflictDetectionService.detectConflictsOptimized(
                date = originalRequest.appointmentDate,
                startTime = originalRequest.startTime,
                endTime = originalRequest.endTime,
                staffId = staff.id,
                salonId = originalRequest.salonId
            )

            if (conflictResult.isAvailable) {
                suggestions.add(
                    OptimizedSuggestion(
                        date = originalRequest.appointmentDate,
                        startTime = originalRequest.startTime,
                        endTime = originalRequest.endTime,
                        staffId = staff.id,
                        staffName = staff.nickname ?: "Unknown",
                        priority = 2,
                        reason = "Alternative staff available",
                        confidence = 0.8
                    )
                )
            }
        }

        return suggestions
    }

    private fun findAlternativeDaySuggestions(
        originalRequest: AppointmentRequest,
        durationMinutes: Long,
        limit: Int,
    ): List<OptimizedSuggestion> {
        val suggestions = mutableListOf<OptimizedSuggestion>()
        val staff = staffRepository.findById(originalRequest.staffId) ?: return suggestions

        // Check next few days
        for (i in 1..MAX_DAYS_AHEAD) {
            if (suggestions.size >= limit) break

            val alternativeDate = originalRequest.appointmentDate.plusDays(i.toLong())
            val conflictResult = conflictDetectionService.detectConflictsOptimized(
                date = alternativeDate,
                startTime = originalRequest.startTime,
                endTime = originalRequest.endTime,
                staffId = originalRequest.staffId,
                salonId = originalRequest.salonId
            )

            if (conflictResult.isAvailable) {
                suggestions.add(
                    OptimizedSuggestion(
                        date = alternativeDate,
                        startTime = originalRequest.startTime,
                        endTime = originalRequest.endTime,
                        staffId = originalRequest.staffId,
                        staffName = staff.nickname ?: "Unknown",
                        priority = 3,
                        reason = "Same staff, $i days later",
                        confidence = 0.7 - (i * 0.1)
                    )
                )
            }
        }

        return suggestions
    }

    private fun findAvailableSlotsForStaff(
        staffId: StaffId,
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate,
        durationMinutes: Long,
        existingAppointments: List<Appointment>,
        limit: Int,
    ): List<OptimizedSuggestion> {
        val suggestions = mutableListOf<OptimizedSuggestion>()
        val staff = staffRepository.findById(staffId) ?: return suggestions

        var currentDate = startDate
        while (currentDate.isBefore(endDate) && suggestions.size < limit) {
            val dayAppointments = existingAppointments.filter { it.appointmentDate == currentDate }
            val timeSlots = generateAvailableTimeSlots(currentDate, durationMinutes, dayAppointments)

            timeSlots.take(limit - suggestions.size).forEach { slot ->
                suggestions.add(
                    OptimizedSuggestion(
                        date = currentDate,
                        startTime = slot.startTime,
                        endTime = slot.endTime,
                        staffId = staffId,
                        staffName = staff.nickname ?: "Unknown",
                        priority = 1,
                        reason = "Available slot",
                        confidence = 0.9
                    )
                )
            }

            currentDate = currentDate.plusDays(1)
        }

        return suggestions
    }

    private fun generateTimeSlots(
        startTime: LocalTime,
        endTime: LocalTime,
        durationMinutes: Long,
    ): List<TimeSlot> {
        val slots = mutableListOf<TimeSlot>()
        var currentTime = startTime

        while (currentTime.plusMinutes(durationMinutes).isBefore(endTime) ||
               currentTime.plusMinutes(durationMinutes) == endTime) {
            slots.add(
                TimeSlot(
                    startTime = currentTime,
                    endTime = currentTime.plusMinutes(durationMinutes)
                )
            )
            currentTime = currentTime.plusMinutes(TIME_SLOT_INTERVAL_MINUTES)
        }

        return slots
    }

    private fun generateAvailableTimeSlots(
        date: LocalDate,
        durationMinutes: Long,
        existingAppointments: List<Appointment>,
    ): List<TimeSlot> {
        val allSlots = generateTimeSlots(
            startTime = LocalTime.of(8, 0),
            endTime = LocalTime.of(18, 0),
            durationMinutes = durationMinutes
        )

        return allSlots.filter { slot ->
            existingAppointments.none { appointment ->
                appointment.startTime < slot.endTime && appointment.endTime > slot.startTime
            }
        }
    }
}

/**
 * Optimized suggestion with performance metrics
 */
data class OptimizedSuggestion(
    val date: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val staffId: StaffId,
    val staffName: String,
    val priority: Int,
    val reason: String,
    val confidence: Double,
)

/**
 * Appointment request for suggestions
 */
data class AppointmentRequest(
    val salonId: SalonId,
    val staffId: StaffId,
    val appointmentDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
)
