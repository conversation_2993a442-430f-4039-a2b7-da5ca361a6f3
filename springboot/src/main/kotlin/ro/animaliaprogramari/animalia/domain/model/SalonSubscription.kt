package ro.animaliaprogramari.animalia.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Domain entity representing a salon's subscription to the platform
 */
data class SalonSubscription(
    val id: SalonSubscriptionId,
    val salonId: SalonId,
    val tier: SubscriptionTier,
    val status: SubscriptionStatus,
    val startDate: LocalDateTime,
    val endDate: LocalDateTime?,
    val isActive: Boolean = true,
    val autoRenew: Boolean = true,
    val monthlyPrice: BigDecimal,
    val billingCycle: BillingCycle = BillingCycle.MONTHLY,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val lastBillingDate: LocalDateTime? = null,
    val nextBillingDate: LocalDateTime? = null,
    val trialEndDate: LocalDateTime? = null,
    val cancelledAt: LocalDateTime? = null,
    val cancellationReason: String? = null
) {
    init {
        require(monthlyPrice >= BigDecimal.ZERO) { "Monthly price must be non-negative" }
        endDate?.let { require(it.isAfter(startDate)) { "End date must be after start date" } }
        trialEndDate?.let { require(it.isAfter(startDate)) { "Trial end date must be after start date" } }
    }

    /**
     * Check if subscription is currently active and valid
     */
    fun isCurrentlyActive(): Boolean {
        val now = LocalDateTime.now()
        return isActive && 
               status == SubscriptionStatus.ACTIVE &&
               now.isAfter(startDate) &&
               (endDate == null || now.isBefore(endDate))
    }

    /**
     * Check if subscription is in trial period
     */
    fun isInTrial(): Boolean {
        val now = LocalDateTime.now()
        return trialEndDate?.let { now.isBefore(it) } ?: false
    }

    /**
     * Check if subscription has expired
     */
    fun isExpired(): Boolean {
        val now = LocalDateTime.now()
        return endDate?.let { now.isAfter(it) } ?: false
    }

    /**
     * Get subscription features based on tier
     */
    fun getFeatures(): SubscriptionFeatures {
        return SubscriptionFeatures.fromTier(tier)
    }

    /**
     * Upgrade subscription to a higher tier
     */
    fun upgradeTo(newTier: SubscriptionTier, newPrice: BigDecimal): SalonSubscription {
        require(newTier.ordinal > tier.ordinal) { "Can only upgrade to a higher tier" }
        
        return copy(
            tier = newTier,
            monthlyPrice = newPrice,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Cancel subscription
     */
    fun cancel(reason: String? = null): SalonSubscription {
        return copy(
            status = SubscriptionStatus.CANCELLED,
            isActive = false,
            autoRenew = false,
            cancelledAt = LocalDateTime.now(),
            cancellationReason = reason,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Reactivate cancelled subscription
     */
    fun reactivate(): SalonSubscription {
        require(status == SubscriptionStatus.CANCELLED) { "Can only reactivate cancelled subscriptions" }
        
        return copy(
            status = SubscriptionStatus.ACTIVE,
            isActive = true,
            cancelledAt = null,
            cancellationReason = null,
            updatedAt = LocalDateTime.now()
        )
    }

    companion object {
        /**
         * Create a new salon subscription
         */
        fun create(
            salonId: SalonId,
            tier: SubscriptionTier = SubscriptionTier.FREELANCER,
            monthlyPrice: BigDecimal,
            billingCycle: BillingCycle = BillingCycle.MONTHLY,
            trialDays: Int? = null
        ): SalonSubscription {
            val now = LocalDateTime.now()
            val trialEndDate = trialDays?.let { now.plusDays(it.toLong()) }
            
            return SalonSubscription(
                id = SalonSubscriptionId.generate(),
                salonId = salonId,
                tier = tier,
                status = SubscriptionStatus.ACTIVE,
                startDate = now,
                endDate = null,
                monthlyPrice = monthlyPrice,
                billingCycle = billingCycle,
                trialEndDate = trialEndDate,
                nextBillingDate = trialEndDate ?: now.plusMonths(1)
            )
        }
    }
}

/**
 * Enum representing subscription status
 */
enum class SubscriptionStatus {
    ACTIVE,
    CANCELLED,
    SUSPENDED,
    EXPIRED,
    TRIAL
}

/**
 * Enum representing billing cycles
 */
enum class BillingCycle(val months: Int) {
    MONTHLY(1),
    QUARTERLY(3),
    YEARLY(12)
}

/**
 * Value object representing a salon subscription identifier
 */
@JvmInline
value class SalonSubscriptionId(val value: String) {
    init {
        require(value.isNotBlank()) { "Salon subscription ID cannot be blank" }
    }

    companion object {
        fun generate(): SalonSubscriptionId = SalonSubscriptionId(java.util.UUID.randomUUID().toString())
        fun of(value: String): SalonSubscriptionId = SalonSubscriptionId(value)
    }

    override fun toString(): String = value
}
