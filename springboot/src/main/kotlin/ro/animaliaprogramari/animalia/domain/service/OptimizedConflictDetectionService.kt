package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaAppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

/**
 * Optimized conflict detection service with <100ms performance target
 * Uses database-level queries instead of application-level filtering
 */
@Service
class OptimizedConflictDetectionService(
    private val appointmentRepository: JpaAppointmentRepository,
    private val workingHoursRepository: WorkingHoursRepository,
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val performanceMonitor: QueryPerformanceMonitor,
) {
    private val logger = LoggerFactory.getLogger(OptimizedConflictDetectionService::class.java)

    /**
     * Fast conflict detection using optimized database queries
     * Target: <100ms response time
     */
    fun detectConflictsOptimized(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonId: SalonId,
        zoneId: ZoneId = ZoneId.systemDefault(),
    ): ConflictDetectionResult {
        return performanceMonitor.monitorConflictDetection(staffId.value) {
            logger.debug("Starting optimized conflict detection for staff {} on {}", staffId.value, date)

            val conflicts = mutableListOf<DetailedScheduleConflict>()

            // 1. Fast working hours check (cached/optimized)
            val workingHoursConflict = checkWorkingHoursOptimized(date, startTime, endTime, staffId, salonId)
            workingHoursConflict?.let { conflicts.add(it) }

            // 2. Fast appointment overlap check (single optimized query)
            if (conflicts.isEmpty()) {
                val appointmentConflict = checkAppointmentConflictsOptimized(date, startTime, endTime, staffId)
                appointmentConflict?.let { conflicts.add(it) }
            }

            // 3. Block time check (if needed)
            // TODO: Implement optimized block time checking

            val isAvailable = conflicts.isEmpty()
            val executionTimeMs = System.currentTimeMillis() // This would be measured by the monitor

            logger.debug(
                "Conflict detection completed for staff {} - Available: {}, Conflicts: {}",
                staffId.value,
                isAvailable,
                conflicts.size
            )

            ConflictDetectionResult(
                isAvailable = isAvailable,
                conflicts = conflicts,
                executionTimeMs = executionTimeMs,
                staffId = staffId,
                requestedSlot = TimeSlot.of(startTime, endTime)
            )
        }
    }

    /**
     * Batch conflict detection for multiple time slots
     * Optimized for appointment suggestion scenarios
     */
    fun detectConflictsBatch(
        date: LocalDate,
        timeSlots: List<TimeSlot>,
        staffId: StaffId,
        salonId: SalonId,
    ): List<ConflictDetectionResult> {
        return performanceMonitor.monitorBatchQuery("conflict_detection", timeSlots.size) {
            logger.debug("Starting batch conflict detection for {} slots", timeSlots.size)

            // Pre-load working hours and appointments for the entire day
            val salonHours = workingHoursRepository.findBySalonId(salonId)
            val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId)
                ?: StaffWorkingHoursSettings.createDefault(staffId, salonId)

            val dayAppointments = appointmentRepository.findConflictingAppointments(
                staffId = staffId,
                date = date,
                startTime = LocalTime.MIN,
                endTime = LocalTime.MAX
            )

            // Check each time slot against pre-loaded data
            timeSlots.map { slot ->
                val conflicts = mutableListOf<DetailedScheduleConflict>()

                // Check working hours
                if (!isWorkingHoursAvailable(date, slot, salonHours, staffHours)) {
                    conflicts.add(
                        DetailedScheduleConflict(
                            type = ScheduleConflictType.STAFF_UNAVAILABLE,
                            message = "Staff not available during working hours",
                            timeSlot = slot
                        )
                    )
                }

                // Check appointment overlaps
                val overlappingAppointments = dayAppointments.filter { appointment ->
                    appointment.appointmentDate == date &&
                    appointment.startTime < slot.endTime &&
                    appointment.endTime > slot.startTime
                }

                if (overlappingAppointments.isNotEmpty()) {
                    conflicts.add(
                        DetailedScheduleConflict(
                            type = ScheduleConflictType.APPOINTMENT,
                            message = "Overlaps with existing appointment",
                            timeSlot = slot,
                            conflictingAppointmentId = overlappingAppointments.first().id
                        )
                    )
                }

                ConflictDetectionResult(
                    isAvailable = conflicts.isEmpty(),
                    conflicts = conflicts,
                    executionTimeMs = 0, // Batch operation
                    staffId = staffId,
                    requestedSlot = slot
                )
            }
        }
    }

    private fun checkWorkingHoursOptimized(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonId: SalonId,
    ): DetailedScheduleConflict? {
        return performanceMonitor.monitorQuery("working_hours_check", QueryType.WORKING_HOURS) {
            val salonHours = workingHoursRepository.findBySalonId(salonId)
            val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId)
                ?: StaffWorkingHoursSettings.createDefault(staffId, salonId)

            val timeSlot = TimeSlot.of(startTime, endTime)

            if (!isWorkingHoursAvailable(date, timeSlot, salonHours, staffHours)) {
                DetailedScheduleConflict(
                    type = ScheduleConflictType.STAFF_UNAVAILABLE,
                    message = "Staff not available during requested time",
                    timeSlot = timeSlot
                )
            } else null
        }
    }

    private fun checkAppointmentConflictsOptimized(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
    ): DetailedScheduleConflict? {
        return performanceMonitor.monitorQuery("appointment_conflicts", QueryType.CONFLICT_DETECTION) {
            val conflictingAppointments = appointmentRepository.findConflictingAppointments(
                staffId = staffId,
                date = date,
                startTime = startTime,
                endTime = endTime
            )

            if (conflictingAppointments.isNotEmpty()) {
                val firstConflict = conflictingAppointments.first()
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Overlaps with appointment from ${firstConflict.startTime} to ${firstConflict.endTime}",
                    timeSlot = TimeSlot.of(startTime, endTime),
                    conflictingAppointmentId = firstConflict.id
                )
            } else null
        }
    }

    private fun isWorkingHoursAvailable(
        date: LocalDate,
        timeSlot: TimeSlot,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
    ): Boolean {
        // Check salon hours
        if (!salonHours.isOpenOn(date)) return false

        val salonDay = salonHours.getWorkingHoursFor(date) ?: return false
        if (!salonDay.isAvailableAt(timeSlot.startTime) || !salonDay.isAvailableAt(timeSlot.endTime.minusMinutes(1))) {
            return false
        }

        // Check staff hours with business inheritance
        if (!staffHours.isAvailableOnWithBusinessHours(date, salonHours)) return false

        val staffDay = staffHours.getWorkingHoursFor(date) ?: return false
        return staffDay.isAvailableAt(timeSlot.startTime) && staffDay.isAvailableAt(timeSlot.endTime.minusMinutes(1))
    }
}

/**
 * Result of conflict detection with performance metrics
 */
data class ConflictDetectionResult(
    val isAvailable: Boolean,
    val conflicts: List<DetailedScheduleConflict>,
    val executionTimeMs: Long,
    val staffId: StaffId,
    val requestedSlot: TimeSlot,
) {
    val isSlowQuery: Boolean
        get() = executionTimeMs > 100
}

/**
 * Detailed conflict information with human-readable messages
 */
data class DetailedScheduleConflict(
    val type: ScheduleConflictType,
    val message: String,
    val timeSlot: TimeSlot,
    val conflictingAppointmentId: AppointmentId? = null,
) {
    override fun toString(): String {
        return "Conflict: $message (${timeSlot.startTime}-${timeSlot.endTime})"
    }
}

/**
 * Time slot value object
 */
data class TimeSlot(
    val startTime: LocalTime,
    val endTime: LocalTime,
) {
    companion object {
        fun of(startTime: LocalTime, endTime: LocalTime): TimeSlot {
            require(startTime.isBefore(endTime)) { "Start time must be before end time" }
            return TimeSlot(startTime, endTime)
        }
    }

    /**
     * Check if this time slot overlaps with another
     */
    fun overlaps(other: TimeSlot): Boolean {
        return startTime < other.endTime && endTime > other.startTime
    }
}

/**
 * Types of schedule conflicts
 */
enum class ScheduleConflictType {
    SALON_CLOSED,
    STAFF_UNAVAILABLE,
    APPOINTMENT,
    BLOCK_TIME,
}
