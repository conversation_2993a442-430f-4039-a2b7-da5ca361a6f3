package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.outbound.SmsQuotaRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.notification.SmsQuota
import ro.animaliaprogramari.animalia.domain.model.notification.QuotaPeriod

/**
 * Enhanced SMS quota service with subscription integration
 */
@Service
class SmsQuotaServiceWithSubscription(
    private val smsQuotaRepository: SmsQuotaRepository,
    private val subscriptionRepository: SalonSubscriptionRepository
) {
    private val logger = LoggerFactory.getLogger(SmsQuotaServiceWithSubscription::class.java)

    /**
     * Get or create SMS quota for a salon based on subscription tier
     */
    fun getOrCreateSmsQuota(salonId: SalonId): SmsQuota {
        val existingQuota = smsQuotaRepository.findBySalonId(salonId)
        
        // If quota exists, check if it needs to be updated based on subscription
        if (existingQuota != null) {
            val subscription = subscriptionRepository.findActiveBySalonId(salonId)
            if (subscription != null) {
                val subscriptionQuota = subscription.tier.getSmsQuota()
                if (existingQuota.totalQuota != subscriptionQuota) {
                    // Update quota to match subscription tier
                    val updatedQuota = existingQuota.updateTotalQuota(subscriptionQuota)
                    return smsQuotaRepository.save(updatedQuota)
                }
            }
            return existingQuota
        }
        
        return createQuotaBasedOnSubscription(salonId)
    }

    /**
     * Check if SMS can be sent for a salon
     */
    fun canSendSms(salonId: SalonId): Boolean {
        val quota = getOrCreateSmsQuota(salonId)
        val canSend = quota.canSendSms()
        
        if (!canSend) {
            logger.warn("SMS quota exhausted for salon: ${salonId.value}. Used: ${quota.usedQuota}/${quota.totalQuota}")
        }
        
        return canSend
    }

    /**
     * Record an SMS sent for a salon (atomic operation)
     */
    @Transactional
    fun recordSmsSent(salonId: SalonId): SmsQuota {
        val quota = getOrCreateSmsQuota(salonId)

        // Double-check quota before recording (race condition protection)
        if (!quota.canSendSms()) {
            logger.error("Attempted to record SMS for exhausted quota. Salon: ${salonId.value}, Used: ${quota.usedQuota}/${quota.totalQuota}")
            throw IllegalStateException("SMS quota exhausted for salon ${salonId.value}")
        }

        val updatedQuota = quota.recordSmsSent()
        val savedQuota = smsQuotaRepository.save(updatedQuota)

        logger.info("SMS recorded for salon: ${salonId.value}. Used: ${savedQuota.usedQuota}/${savedQuota.totalQuota}")

        // Log warning if quota is nearly exhausted
        if (savedQuota.isNearlyExhausted()) {
            logger.warn("SMS quota nearly exhausted for salon: ${salonId.value}. Used: ${savedQuota.usedQuota}/${savedQuota.totalQuota} (${savedQuota.getUsagePercentage()}%)")
        }

        return savedQuota
    }

    /**
     * Get remaining SMS count for a salon
     */
    fun getRemainingQuota(salonId: SalonId): Int {
        val quota = getOrCreateSmsQuota(salonId)
        return quota.getRemainingQuota()
    }

    /**
     * Update quota when subscription changes
     */
    fun updateQuotaForSubscriptionChange(salonId: SalonId): SmsQuota {
        val subscription = subscriptionRepository.findActiveBySalonId(salonId)
            ?: throw IllegalStateException("No active subscription found for salon ${salonId.value}")
        
        val newQuotaLimit = subscription.tier.getSmsQuota()
        val quota = getOrCreateSmsQuota(salonId)
        val updatedQuota = quota.updateTotalQuota(newQuotaLimit)
        val savedQuota = smsQuotaRepository.save(updatedQuota)
        
        logger.info("SMS quota updated for subscription change. Salon: ${salonId.value}, New limit: $newQuotaLimit")
        
        return savedQuota
    }

    /**
     * Get quota status for a salon with subscription info
     */
    fun getQuotaStatusWithSubscription(salonId: SalonId): SmsQuotaStatusWithSubscription {
        val quota = getOrCreateSmsQuota(salonId)
        val subscription = subscriptionRepository.findActiveBySalonId(salonId)
        
        return SmsQuotaStatusWithSubscription(
            salonId = salonId.value,
            totalQuota = quota.totalQuota,
            usedQuota = quota.usedQuota,
            remainingQuota = quota.getRemainingQuota(),
            usagePercentage = quota.getUsagePercentage(),
            resetDate = quota.resetDate,
            quotaPeriod = quota.quotaPeriod.name,
            isExhausted = quota.isExhausted(),
            isNearlyExhausted = quota.isNearlyExhausted(),
            subscriptionTier = subscription?.tier?.name,
            subscriptionActive = subscription?.isCurrentlyActive() ?: false
        )
    }

    /**
     * Create quota based on salon's subscription tier
     */
    private fun createQuotaBasedOnSubscription(salonId: SalonId): SmsQuota {
        val subscription = subscriptionRepository.findActiveBySalonId(salonId)
        val quotaLimit = subscription?.tier?.getSmsQuota() ?: 30 // Default fallback
        
        val quota = SmsQuota.createDefault(salonId, quotaLimit)
        val savedQuota = smsQuotaRepository.save(quota)
        
        logger.info("Created SMS quota for salon: ${salonId.value}. Limit: $quotaLimit (based on subscription: ${subscription?.tier?.name ?: "DEFAULT"})")
        
        return savedQuota
    }
}

/**
 * Enhanced SMS quota status with subscription information
 */
data class SmsQuotaStatusWithSubscription(
    val salonId: String,
    val totalQuota: Int,
    val usedQuota: Int,
    val remainingQuota: Int,
    val usagePercentage: Double,
    val resetDate: java.time.LocalDateTime,
    val quotaPeriod: String,
    val isExhausted: Boolean,
    val isNearlyExhausted: Boolean,
    val subscriptionTier: String?,
    val subscriptionActive: Boolean
)
