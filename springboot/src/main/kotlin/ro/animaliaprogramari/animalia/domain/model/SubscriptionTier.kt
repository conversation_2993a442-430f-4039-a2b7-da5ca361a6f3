package ro.animaliaprogramari.animalia.domain.model

/**
 * Enum representing the different subscription tiers available for salons
 */
enum class SubscriptionTier(
    val tierName: String,
    val maxSmsPerMonth: Int,
    val canAddTeamMembers: Boolean,
    val canChangeSalon: Bo<PERSON>an,
    val maxTeamMembers: Int?
) {
    FREELANCER(
        tierName = "Freelancer",
        maxSmsPerMonth = 50,
        canAddTeamMembers = false,
        canChangeSalon = false,
        maxTeamMembers = 1
    ),
    
    TEAM(
        tierName = "Team",
        maxSmsPerMonth = 250,
        canAddTeamMembers = true,
        canChangeSalon = false,
        maxTeamMembers = null // unlimited
    ),
    
    ENTERPRISE(
        tierName = "Enterprise",
        maxSmsPerMonth = 1000,
        canAddTeamMembers = true,
        canChangeSalon = true,
        maxTeamMembers = null // unlimited
    );

    /**
     * Check if this tier allows adding team members
     */
    fun allowsTeamMembers(): Boolean = canAddTeamMembers

    /**
     * Check if this tier allows changing salon
     */
    fun allowsSalonChanges(): Boolean = canChangeSalon

    /**
     * Check if team size is within limits for this tier
     */
    fun isTeamSizeAllowed(currentTeamSize: Int): Boolean {
        return maxTeamMembers?.let { currentTeamSize <= it } ?: true
    }

    /**
     * Get the SMS quota for this tier
     */
    fun getSmsQuota(): Int = maxSmsPerMonth

    /**
     * Check if this tier has unlimited team members
     */
    fun hasUnlimitedTeamMembers(): Boolean = maxTeamMembers == null

    companion object {
        /**
         * Get subscription tier from string value
         */
        fun fromString(value: String): SubscriptionTier {
            return values().find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Unknown subscription tier: $value")
        }

        /**
         * Get the default subscription tier for new salons
         */
        fun getDefault(): SubscriptionTier = FREELANCER
    }
}

/**
 * Value object representing subscription tier features
 */
data class SubscriptionFeatures(
    val tier: SubscriptionTier,
    val smsQuota: Int,
    val canInviteTeamMembers: Boolean,
    val canChangeSalon: Boolean,
    val maxTeamMembers: Int?,
    val description: String
) {
    companion object {
        fun fromTier(tier: SubscriptionTier): SubscriptionFeatures {
            return SubscriptionFeatures(
                tier = tier,
                smsQuota = tier.maxSmsPerMonth,
                canInviteTeamMembers = tier.canAddTeamMembers,
                canChangeSalon = tier.canChangeSalon,
                maxTeamMembers = tier.maxTeamMembers,
                description = getDescriptionForTier(tier)
            )
        }

        private fun getDescriptionForTier(tier: SubscriptionTier): String {
            return when (tier) {
                SubscriptionTier.FREELANCER -> 
                    "Perfect for individual groomers. Includes ${tier.maxSmsPerMonth} SMS reminders per month."
                SubscriptionTier.TEAM -> 
                    "Great for small teams. Includes ${tier.maxSmsPerMonth} SMS reminders per month and unlimited team members."
                SubscriptionTier.ENTERPRISE -> 
                    "Full-featured plan for growing businesses. Includes ${tier.maxSmsPerMonth} SMS reminders per month, unlimited team members, and salon management features."
            }
        }
    }
}
