package ro.animaliaprogramari.animalia.domain.service

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SalonSubscription
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier

/**
 * Domain service for validating subscription-based operations
 */
@Service
class SubscriptionValidationService(
    private val subscriptionRepository: SalonSubscriptionRepository
) {

    /**
     * Validate if salon can send SMS based on subscription tier
     */
    fun validateSmsQuota(salonId: SalonId, currentUsage: Int): ValidationResult {
        val subscription = getActiveSubscription(salonId)
        val maxSms = subscription.tier.getSmsQuota()
        
        return if (currentUsage >= maxSms) {
            ValidationResult.failure(
                "SMS quota exceeded. Your ${subscription.tier.tierName} plan allows ${maxSms} SMS per month. " +
                "Upgrade your subscription to send more messages."
            )
        } else {
            ValidationResult.success()
        }
    }

    /**
     * Validate if salon can invite team members based on subscription tier
     */
    fun validateTeamInvitation(salonId: SalonId, currentTeamSize: Int): ValidationResult {
        val subscription = getActiveSubscription(salonId)
        
        if (!subscription.tier.allowsTeamMembers()) {
            return ValidationResult.failure(
                "Team invitations are not available with your ${subscription.tier.tierName} plan. " +
                "Upgrade to Team or Enterprise plan to invite team members."
            )
        }

        if (!subscription.tier.isTeamSizeAllowed(currentTeamSize + 1)) {
            return ValidationResult.failure(
                "Team size limit reached. Your ${subscription.tier.tierName} plan allows up to " +
                "${subscription.tier.maxTeamMembers} team members."
            )
        }

        return ValidationResult.success()
    }

    /**
     * Validate if salon can be changed based on subscription tier
     */
    fun validateSalonChange(salonId: SalonId): ValidationResult {
        val subscription = getActiveSubscription(salonId)
        
        return if (!subscription.tier.allowsSalonChanges()) {
            ValidationResult.failure(
                "Salon changes are not available with your ${subscription.tier.tierName} plan. " +
                "Upgrade to Enterprise plan to manage multiple salons."
            )
        } else {
            ValidationResult.success()
        }
    }

    /**
     * Validate if subscription upgrade is valid
     */
    fun validateSubscriptionUpgrade(salonId: SalonId, newTier: SubscriptionTier): ValidationResult {
        val currentSubscription = getActiveSubscription(salonId)
        
        if (newTier.ordinal <= currentSubscription.tier.ordinal) {
            return ValidationResult.failure(
                "Cannot upgrade to ${newTier.tierName} from ${currentSubscription.tier.tierName}. " +
                "You can only upgrade to a higher tier."
            )
        }

        return ValidationResult.success()
    }

    /**
     * Validate if subscription downgrade is valid
     */
    fun validateSubscriptionDowngrade(
        salonId: SalonId, 
        newTier: SubscriptionTier,
        currentTeamSize: Int,
        currentSmsUsage: Int
    ): ValidationResult {
        val currentSubscription = getActiveSubscription(salonId)
        
        if (newTier.ordinal >= currentSubscription.tier.ordinal) {
            return ValidationResult.failure(
                "Cannot downgrade to ${newTier.tierName} from ${currentSubscription.tier.tierName}. " +
                "You can only downgrade to a lower tier."
            )
        }

        // Check if current usage fits within new tier limits
        if (!newTier.isTeamSizeAllowed(currentTeamSize)) {
            return ValidationResult.failure(
                "Cannot downgrade to ${newTier.tierName}. Your current team size ($currentTeamSize) " +
                "exceeds the limit for this plan (${newTier.maxTeamMembers ?: "unlimited"})."
            )
        }

        if (currentSmsUsage > newTier.getSmsQuota()) {
            return ValidationResult.failure(
                "Cannot downgrade to ${newTier.tierName}. Your current SMS usage ($currentSmsUsage) " +
                "exceeds the monthly limit for this plan (${newTier.getSmsQuota()})."
            )
        }

        return ValidationResult.success()
    }

    /**
     * Get subscription features for a salon
     */
    fun getSubscriptionFeatures(salonId: SalonId): SubscriptionFeatures {
        val subscription = getActiveSubscription(salonId)
        return subscription.getFeatures()
    }

    /**
     * Check if salon has active subscription
     */
    fun hasActiveSubscription(salonId: SalonId): Boolean {
        return subscriptionRepository.hasActiveSubscription(salonId)
    }

    /**
     * Get active subscription for salon or throw exception
     */
    private fun getActiveSubscription(salonId: SalonId): SalonSubscription {
        return subscriptionRepository.findActiveBySalonId(salonId)
            ?: throw DomainException("No active subscription found for salon: ${salonId.value}")
    }
}

/**
 * Result of subscription validation
 */
data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String? = null
) {
    companion object {
        fun success(): ValidationResult = ValidationResult(true)
        fun failure(message: String): ValidationResult = ValidationResult(false, message)
    }
}

/**
 * Exception thrown when subscription validation fails
 */
class SubscriptionValidationException(message: String) : DomainException(message)
