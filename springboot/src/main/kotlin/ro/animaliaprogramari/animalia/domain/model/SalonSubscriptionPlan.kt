package ro.animaliaprogramari.animalia.domain.model

/**
 * Subscription plans available for a salon. The plan determines
 * what features are enabled for that salon.
 */
enum class SalonSubscriptionPlan(
    val canChangeSalon: Boolean,
    val maxSmsReminders: Int,
    val canAddTeamMembers: Boolean,
) {
    /** Freelancer plan: single groomer salon. */
    FREELANCER(
        canChangeSalon = false,
        maxSmsReminders = 50,
        canAddTeamMembers = false,
    ),

    /** Team subscription: multiple groomers but cannot change salon name. */
    TEAM(
        canChangeSalon = false,
        maxSmsReminders = 250,
        canAddTeamMembers = true,
    ),

    /** Enterprise subscription: full features enabled. */
    ENTERPRISE(
        canChangeSalon = true,
        maxSmsReminders = 1000,
        canAddTeamMembers = true,
    );

    companion object {
        fun fromString(value: String): SalonSubscriptionPlan {
            return values().firstOrNull { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Unknown subscription plan: $value")
        }
    }
}

