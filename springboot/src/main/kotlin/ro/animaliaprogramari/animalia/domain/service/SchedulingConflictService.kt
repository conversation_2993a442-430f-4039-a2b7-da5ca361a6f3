package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

/**
 * Domain service for comprehensive schedule conflict detection.
 * It performs pure business logic checks without repository access.
 */
class SchedulingConflictService {
    private val logger = LoggerFactory.getLogger(SchedulingConflictService::class.java)

    /**
     * Detect all conflicts for a staff member within the provided time slot.
     */
    fun detectConflicts(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        appointments: List<Appointment>,
        blocks: List<BlockTime>,
        zoneId: ZoneId = ZoneId.systemDefault(),
    ): List<ScheduleConflict> {
        logger.debug("=== CONFLICT DETECTION START ===")
        logger.debug("Appointment Request Details:")
        logger.debug("  - Date: {}", date)
        logger.debug("  - Time: {} - {}", startTime, endTime)
        logger.debug("  - Staff ID: {}", staffId.value)
        logger.debug("  - Salon ID: {}", salonHours.salonId.value)
        logger.debug("  - Zone ID: {}", zoneId)
        logger.debug("  - Existing appointments count: {}", appointments.size)
        logger.debug("  - Block times count: {}", blocks.size)

        val conflicts = mutableListOf<ScheduleConflict>()

        // Salon open check
        logger.debug("--- Checking Salon Working Hours ---")
        if (!salonHours.isOpenOn(date)) {
            logger.debug("CONFLICT DETECTED: Salon is closed on date {}", date)
            logger.debug("  - Salon custom closures: {}", salonHours.customClosures.map { "${it.date}: ${it.reason}" })
            logger.debug("  - Salon holidays: {}", salonHours.holidays.map { "${it.date}: working=${it.isWorkingDay}" })
            conflicts.add(ScheduleConflict(ScheduleConflictType.SALON_CLOSED))
        } else {
            val salonDay = salonHours.getWorkingHoursFor(date)
            logger.debug("Salon is open on date {}, working hours: {}", date, salonDay)
            if (salonDay == null ||
                !salonDay.isAvailableAt(startTime) ||
                !salonDay.isAvailableAt(endTime.minusMinutes(1))
            ) {
                logger.debug("CONFLICT DETECTED: Requested time outside salon working hours")
                logger.debug("  - Salon day schedule: {}", salonDay)
                logger.debug("  - Start time {} available: {}", startTime, salonDay?.isAvailableAt(startTime))
                logger.debug(
                    "  - End time {} available: {}",
                    endTime.minusMinutes(1),
                    salonDay?.isAvailableAt(endTime.minusMinutes(1)),
                )
                if (salonDay != null) {
                    logger.debug("  - Salon working hours: {} - {}", salonDay.startTime, salonDay.endTime)
                    if (salonDay.breakStart != null && salonDay.breakEnd != null) {
                        logger.debug("  - Salon break time: {} - {}", salonDay.breakStart, salonDay.breakEnd)
                    }
                }
                conflicts.add(ScheduleConflict(ScheduleConflictType.SALON_CLOSED))
            } else {
                logger.debug("Salon working hours check PASSED")
            }
        }

        // Staff availability check with business hours inheritance
        logger.debug("--- Checking Staff Working Hours (with Business Hours Inheritance) ---")
        if (!staffHours.isAvailableOnWithBusinessHours(date, salonHours)) {
            logger.error("CONFLICT DETECTED: Staff is unavailable on date {} (considering business hours)", date)
            logger.error("  - Day of week: {}", date.dayOfWeek)
            logger.error("  - Business hours open: {}", salonHours.isOpenOn(date))
            logger.error("  - Staff available (raw): {}", staffHours.isAvailableOn(date))
            logger.error("  - Staff inheritance enabled: {}", staffHours.inheritFromBusiness)
            logger.error("  - Staff weekly schedule: {}", staffHours.weeklySchedule)
            logger.error("  - Staff custom closures: {}", staffHours.customClosures.map { "${it.date}: ${it.reason}" })
            logger.error("  - Staff holidays: {}", staffHours.holidays.map { "${it.date}: working=${it.isWorkingDay}" })

            // Check specific day schedule
            val daySchedule = staffHours.weeklySchedule[date.dayOfWeek]
            logger.error("  - Day schedule for {}: {}", date.dayOfWeek, daySchedule)
            if (daySchedule != null) {
                logger.error("  - Is working day: {}", daySchedule.isWorkingDay)
                logger.error("  - Working hours: {} - {}", daySchedule.startTime, daySchedule.endTime)
            } else {
                logger.error("  - No schedule configured for {}", date.dayOfWeek)
            }

            conflicts.add(ScheduleConflict(ScheduleConflictType.STAFF_UNAVAILABLE))
        } else {
            // Use effective working hours that consider business hours inheritance
            val effectiveStaffDay = staffHours.getEffectiveWorkingHoursFor(date, salonHours)
            logger.debug("Staff is available on date {}, effective working hours: {}", date, effectiveStaffDay)

            if (effectiveStaffDay == null ||
                !effectiveStaffDay.isAvailableAt(startTime) ||
                !effectiveStaffDay.isAvailableAt(endTime.minusMinutes(1))
            ) {
                logger.debug("CONFLICT DETECTED: Requested time outside effective staff working hours")
                logger.debug("  - Effective staff day schedule: {}", effectiveStaffDay)
                logger.debug("  - Start time {} available: {}", startTime, effectiveStaffDay?.isAvailableAt(startTime))
                logger.debug(
                    "  - End time {} available: {}",
                    endTime.minusMinutes(1),
                    effectiveStaffDay?.isAvailableAt(endTime.minusMinutes(1)),
                )
                if (effectiveStaffDay != null) {
                    logger.debug(
                        "  - Effective working hours: {} - {}",
                        effectiveStaffDay.startTime,
                        effectiveStaffDay.endTime,
                    )
                    if (effectiveStaffDay.breakStart != null && effectiveStaffDay.breakEnd != null) {
                        logger.debug(
                            "  - Effective break time: {} - {}",
                            effectiveStaffDay.breakStart,
                            effectiveStaffDay.breakEnd,
                        )
                    }
                }

                // Also log the raw staff schedule for debugging
                val rawStaffDay = staffHours.getWorkingHoursFor(date)
                logger.debug("  - Raw staff schedule: {}", rawStaffDay)
                val businessDay = salonHours.getWorkingHoursFor(date)
                logger.debug("  - Business schedule: {}", businessDay)

                conflicts.add(ScheduleConflict(ScheduleConflictType.STAFF_UNAVAILABLE))
            } else {
                logger.debug("Staff working hours check PASSED (effective schedule)")
            }
        }

        // Appointment overlaps
        logger.debug("--- Checking Existing Appointment Overlaps ---")
        val relevantAppointments = appointments.filter { it.staffId == staffId && it.appointmentDate == date }
        logger.debug(
            "Found {} existing appointments for staff {} on date {}",
            relevantAppointments.size,
            staffId.value,
            date,
        )

        relevantAppointments.forEach { appt ->
            logger.debug(
                "Checking appointment: ID={}, Time={}-{}, Status={}",
                appt.id.value,
                appt.startTime,
                appt.endTime,
                appt.status,
            )
            val slot = TimeSlot.of(appt.startTime, appt.endTime)
            val requestedSlot = TimeSlot.of(startTime, endTime)
            if (slot.overlaps(requestedSlot)) {
                logger.error("CONFLICT DETECTED: Appointment overlap found")
                logger.error("  - Existing appointment ID: {}", appt.id.value)
                logger.error("  - Existing appointment: {} - {}", appt.startTime, appt.endTime)
                logger.error("  - Existing appointment status: {}", appt.status)
                logger.error("  - Existing appointment date: {}", appt.appointmentDate)
                logger.error("  - Requested appointment: {} - {}", startTime, endTime)
                logger.error("  - Requested appointment date: {}", date)
                logger.error("  - Overlap details: existing slot={}, requested slot={}", slot, requestedSlot)
                conflicts.add(ScheduleConflict(ScheduleConflictType.APPOINTMENT))
            } else {
                logger.debug("No overlap with appointment {} - {}", appt.startTime, appt.endTime)
            }
        }

        if (relevantAppointments.isEmpty()) {
            logger.debug("No existing appointments found for staff on this date - appointment overlap check PASSED")
        } else if (conflicts.none { it.type == ScheduleConflictType.APPOINTMENT }) {
            logger.debug("All existing appointments checked - no overlaps found - appointment overlap check PASSED")
        }

        // Block overlaps
        logger.debug("--- Checking Block Time Overlaps ---")
        val relevantBlocks = blocks.filter { it.isActive() && it.affectsStaff(staffId) }
        logger.debug("Found {} active block times affecting staff {}", relevantBlocks.size, staffId.value)

        relevantBlocks.forEach { block ->
            val blockStart = block.startTime.withZoneSameInstant(zoneId)
            val blockEnd = block.endTime.withZoneSameInstant(zoneId)
            logger.debug(
                "Checking block: ID={}, Time={}-{}, Reason={}, Active={}",
                block.id.value,
                blockStart,
                blockEnd,
                block.reason,
                block.isActive(),
            )

            if (blockStart.toLocalDate() == date &&
                blockStart.toLocalTime().isBefore(endTime) &&
                blockEnd.toLocalTime().isAfter(startTime)
            ) {
                logger.debug("CONFLICT DETECTED: Block time overlap found")
                logger.debug("  - Block time: {} - {}", blockStart.toLocalTime(), blockEnd.toLocalTime())
                logger.debug("  - Requested time: {} - {}", startTime, endTime)
                logger.debug("  - Block reason: {}", block.reason)
                logger.debug("  - Block affects staff: {}", block.affectsStaff(staffId))
                conflicts.add(ScheduleConflict(ScheduleConflictType.BLOCK_TIME))
            } else {
                logger.debug(
                    "No overlap with block {} - {} (different date or no time overlap)",
                    blockStart.toLocalTime(),
                    blockEnd.toLocalTime(),
                )
            }
        }

        if (relevantBlocks.isEmpty()) {
            logger.debug("No active block times found for staff - block time check PASSED")
        } else if (conflicts.none { it.type == ScheduleConflictType.BLOCK_TIME }) {
            logger.debug("All block times checked - no overlaps found - block time check PASSED")
        }

        val distinctConflicts = conflicts.distinct()
        logger.debug("=== CONFLICT DETECTION COMPLETE ===")
        logger.debug("Total conflicts found: {}", distinctConflicts.size)
        if (distinctConflicts.isNotEmpty()) {
            logger.debug("Conflict types: {}", distinctConflicts.map { it.type })
        } else {
            logger.debug("No conflicts detected - time slot is AVAILABLE")
        }

        return distinctConflicts
    }

    /** Shortcut to check if a slot is free */
    fun isSlotAvailable(
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        staffId: StaffId,
        salonHours: WorkingHoursSettings,
        staffHours: StaffWorkingHoursSettings,
        appointments: List<Appointment>,
        blocks: List<BlockTime>,
        zoneId: ZoneId = ZoneId.systemDefault(),
    ): Boolean {
        logger.debug(
            "Checking slot availability for Date: {}, Time: {}-{}, Staff: {}",
            date,
            startTime,
            endTime,
            staffId.value,
        )

        val conflicts =
            detectConflicts(
                date,
                startTime,
                endTime,
                staffId,
                salonHours,
                staffHours,
                appointments,
                blocks,
                zoneId,
            )

        val isAvailable = conflicts.isEmpty()
        logger.debug(
            "Slot availability result: {} (conflicts: {})",
            if (isAvailable) "AVAILABLE" else "NOT AVAILABLE",
            conflicts.size,
        )

        return isAvailable
    }
}
