package ro.animaliaprogramari.animalia.infrastructure.scheduler

import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Scheduler that automatically completes past appointments which were not cancelled.
 */
@Component
class AppointmentCompletionScheduler(
    private val appointmentRepository: AppointmentRepository,
) {
    private val logger = LoggerFactory.getLogger(AppointmentCompletionScheduler::class.java)

    /**
     * Runs hourly and marks past appointments as completed.
     */
    @Scheduled(cron = "0 15 * * * *")
    fun completePastAppointments() {
        logger.info("🔄 Starting automatic completion of past appointments...")

        val statusesToCheck =
            listOf(
                AppointmentStatus.SCHEDULED,
                AppointmentStatus.CONFIRMED,
                AppointmentStatus.IN_PROGRESS,
                AppointmentStatus.RESCHEDULED,
            )

        try {
            val appointments = appointmentRepository.findByStatuses(statusesToCheck)
            logger.info("📋 Found ${appointments.size} appointments with eligible statuses: ${statusesToCheck.map { it.name }}")

            val pastAppointments = appointments.filter { isAppointmentFinished(it.appointmentDate, it.endTime) }
            logger.info("⏰ Found ${pastAppointments.size} past appointments that need completion")

            var completedCount = 0
            var failedCount = 0

            pastAppointments.forEach { appointment ->
                try {
                    if (appointment.canBeCompleted()) {
                        val completed = appointment.complete("Auto-completed by scheduler")
                        appointmentRepository.save(completed)
                        completedCount++
                        logger.info("✅ Auto-completed appointment ${appointment.id.value} (${appointment.appointmentDate} ${appointment.startTime}-${appointment.endTime})")
                    } else {
                        logger.warn("⚠️ Skipped appointment ${appointment.id.value} - cannot be completed in status: ${appointment.status}")
                    }
                } catch (ex: Exception) {
                    failedCount++
                    logger.error("❌ Failed to auto-complete appointment ${appointment.id.value}: ${ex.message}", ex)
                }
            }

            logger.info("📊 Appointment completion summary: ${completedCount} completed, ${failedCount} failed")
        } catch (ex: Exception) {
            logger.error("❌ Error during appointment completion scheduler execution", ex)
        }
    }

    private fun isAppointmentFinished(
        date: LocalDate,
        endTime: LocalTime,
    ): Boolean {
        val endDateTime = LocalDateTime.of(date, endTime)
        return endDateTime.isBefore(LocalDateTime.now())
    }
}
