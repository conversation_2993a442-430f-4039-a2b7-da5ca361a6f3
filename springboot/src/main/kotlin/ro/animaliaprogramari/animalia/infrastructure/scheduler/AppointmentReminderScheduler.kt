package ro.animaliaprogramari.animalia.infrastructure.scheduler

import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.DomainEventPublisher
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentReminderEvent
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentReminderType
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import java.util.UUID

/**
 * Scheduler that publishes reminder events for upcoming appointments.
 * Every day at 6 PM, it checks for appointments scheduled for the next day and publishes reminder events.
 * Every 6 hours, it checks for appointments scheduled in the next hour and publishes reminder events.
 */
@Component
class AppointmentReminderScheduler(
    private val appointmentRepository: AppointmentRepository,
    private val staffRepository: StaffRepository,
    private val domainEventPublisher: DomainEventPublisher,
) {
    private val logger = LoggerFactory.getLogger(AppointmentReminderScheduler::class.java)

    @Scheduled(cron = "0 0 18 * * *")
    fun dayBeforeReminders() {
        val targetDate = LocalDate.now().plusDays(1)
        val appointments = appointmentRepository.findByDateAndStatus(targetDate, AppointmentStatus.SCHEDULED)
        appointments.forEach { appt ->
            publishReminder(
                appt.id.value,
                appt.clientId.value,
                appt.staffId.value,
                appt.appointmentDate,
                appt.startTime,
                AppointmentReminderType.DAY_BEFORE,
                appt.salonId
            )
        }
    }

    @Scheduled(cron = "0 0 */6 * * *")
    fun sixHoursReminders() {
        val now = LocalDateTime.now()
        val from = now.plusHours(6).truncatedTo(ChronoUnit.HOURS)
        val to = from.plusHours(1)
        val appointments =
            appointmentRepository.findByDate(from.toLocalDate())
                .filter { it.status == AppointmentStatus.SCHEDULED }
                .filter { it.startTime >= from.toLocalTime() && it.startTime < to.toLocalTime() }
        appointments.forEach { appt ->
            publishReminder(
                appt.id.value,
                appt.clientId.value,
                appt.staffId.value,
                appt.appointmentDate,
                appt.startTime,
                AppointmentReminderType.SIX_HOURS_BEFORE,
                appt.salonId,
            )
        }
    }

    private fun publishReminder(
        appointmentId: String,
        clientId: String,
        staffId: String,
        date: LocalDate,
        startTime: LocalTime,
        type: AppointmentReminderType,
        salonId: SalonId,
    ) {
        try {
            val userId =
                staffRepository.findById(ro.animaliaprogramari.animalia.domain.model.StaffId.of(staffId))?.userId
                    ?: UserId.of(staffId)
            val event =
                AppointmentReminderEvent(
                    eventId = UUID.randomUUID().toString(),
                    occurredAt = LocalDateTime.now(),
                    aggregateId = appointmentId,
                    appointmentId = ro.animaliaprogramari.animalia.domain.model.AppointmentId.of(appointmentId),
                    clientId = ro.animaliaprogramari.animalia.domain.model.ClientId.of(clientId),
                    userId = userId,
                    appointmentDate = date,
                    startTime = startTime,
                    reminderType = type,
                    salonId = salonId,
                )
            domainEventPublisher.publish(event)
        } catch (ex: Exception) {
            logger.error("Failed to publish reminder event for appointment $appointmentId", ex)
        }
    }
}
