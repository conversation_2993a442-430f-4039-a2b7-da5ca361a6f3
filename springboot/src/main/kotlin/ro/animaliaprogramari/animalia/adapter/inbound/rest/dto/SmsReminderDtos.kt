package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * Response DTO for SMS reminder settings
 */
@Schema(description = "SMS reminder settings response")
data class SmsReminderSettingsResponse(
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    @Schema(description = "Automated SMS enabled")
    @JsonProperty("enabled")
    val enabled: Boolean,
    @Schema(description = "Send confirmation messages")
    @JsonProperty("appointmentConfirmations")
    val appointmentConfirmations: Boolean,
    @Schema(description = "Send day-before reminders")
    @JsonProperty("dayBeforeReminders")
    val dayBeforeReminders: Boolean,
    @Schema(description = "Send follow-up messages")
    @JsonProperty("followUpMessages")
    val followUpMessages: Boolean,
    @Schema(description = "Selected SMS provider")
    @JsonProperty("selectedProvider")
    val selectedProvider: String,
    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime,
)

/**
 * Request DTO for updating SMS reminder settings
 */
@Schema(description = "Update SMS reminder settings request")
data class UpdateSmsReminderSettingsRequest(
    @Schema(description = "Enable automated SMS")
    @JsonProperty("enabled")
    val enabled: Boolean,
    @Schema(description = "Send confirmation messages")
    @JsonProperty("appointmentConfirmations")
    val appointmentConfirmations: Boolean,
    @Schema(description = "Send day-before reminders")
    @JsonProperty("dayBeforeReminders")
    val dayBeforeReminders: Boolean,
    @Schema(description = "Send follow-up messages")
    @JsonProperty("followUpMessages")
    val followUpMessages: Boolean,
    @field:NotBlank(message = "Selected provider is required")
    @Schema(description = "Selected SMS provider")
    @JsonProperty("selectedProvider")
    val selectedProvider: String,
) {
    init {
        require(selectedProvider.equals("animalia", ignoreCase = true)) {
            "Selected provider must be 'animalia'"
        }
    }
}
