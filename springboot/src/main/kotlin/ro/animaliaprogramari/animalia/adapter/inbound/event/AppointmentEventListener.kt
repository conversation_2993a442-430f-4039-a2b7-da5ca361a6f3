package ro.animaliaprogramari.animalia.adapter.inbound.event

import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.event.appointment.*
import ro.animaliaprogramari.animalia.domain.service.PersonalizedSmsService
import ro.animaliaprogramari.animalia.application.service.FirebaseService
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsReminderSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.NotificationUtils
import java.time.format.DateTimeFormatter
import java.time.LocalTime

/**
 * Listener that sends personalized SMS notifications and creates push notifications for appointment related events.
 * Uses PersonalizedSmsService to create warm, professional messages in Romanian.
 * Creates push notifications for staff members to see in the application.
 */
@Component
class AppointmentEventListener(
    private val personalizedSmsService: PersonalizedSmsService,
    private val firebaseService: FirebaseService,
    private val staffRepository: StaffRepository,
    private val smsReminderSettingsRepository: SmsReminderSettingsRepository,
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
) {
    private val logger = LoggerFactory.getLogger(AppointmentEventListener::class.java)

    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    private fun getNames(clientId: ClientId, petId: PetId?): Pair<String?, String?> {
        val clientName = try {
            clientRepository.findById(clientId)?.name
        } catch (_: Exception) {
            null
        }
        val petName = petId?.let {
            try {
                petRepository.findById(it)?.name
            } catch (_: Exception) {
                null
            }
        }
        return clientName to petName
    }

    @EventListener
    fun onAppointmentScheduled(event: AppointmentScheduledEvent) {
        logger.debug("Processing appointment scheduled event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.appointmentConfirmations) {
            personalizedSmsService.sendAppointmentScheduledNotification(
                clientId = event.clientId,
                petId = event.petId,
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
                startTime = event.startTime,
            )
        }

        NotificationUtils.sleep(event.userId, 5, logger)

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, event.petId)

        val title = buildString {
            append("Programare Nouă")
            petName?.let { append(": $it") }
        }
        val message = buildString {
            clientName?.let { append(it) }
            if (petName != null) append(" - $petName")
            append(" - ${event.appointmentDate.format(dateFormatter)} la ${event.startTime.format(timeFormatter)}")
        }

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_SCHEDULED",
            startTime = event.startTime
        )
    }

    @EventListener
    fun onAppointmentCancelled(event: AppointmentCancelledEvent) {
        logger.debug("Processing appointment cancelled event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.appointmentConfirmations) {
            personalizedSmsService.sendAppointmentCancelledNotification(
                clientId = event.clientId,
                petId = null,
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
            )
        }

        NotificationUtils.sleep(event.userId, 5, logger)

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, null)

        val title = buildString {
            append("Programare Anulată")
            petName?.let { append(": $it") }
        }

        val message = buildString {
            clientName?.let { append(it) }
            if (petName != null) append(" - $petName")
            append(" din ${event.appointmentDate.format(dateFormatter)} a fost anulată")
            event.reason?.let { append(". Motiv: $it") }
        }

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_CANCELLED"
        )
    }

    @EventListener
    fun onAppointmentRescheduled(event: AppointmentRescheduledEvent) {
        logger.debug("Processing appointment rescheduled event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.appointmentConfirmations) {
            personalizedSmsService.sendAppointmentRescheduledNotification(
                clientId = event.clientId,
                petId = null,
                appointmentId = event.appointmentId,
                oldDate = event.oldDate,
                newDate = event.newDate,
                newStartTime = event.newStartTime,
            )
        }

        NotificationUtils.sleep(event.userId, 5, logger)

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, null)

        val title = buildString {
            append("Programare Reprogramată")
            petName?.let { append(": $it") }
        }

        val message = buildString {
            clientName?.let { append(it) }
            if (petName != null) append(" - $petName")
            append(" a fost mutată de pe ${event.oldDate.format(dateFormatter)} pe ${event.newDate.format(dateFormatter)} la ${event.newStartTime.format(timeFormatter)}")
        }

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_RESCHEDULED",
            startTime = event.newStartTime
        )
    }

    @EventListener
    fun onAppointmentDeleted(event: AppointmentDeletedEvent) {
        logger.debug("Processing appointment deleted event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.appointmentConfirmations) {
            personalizedSmsService.sendAppointmentDeletedNotification(
                clientId = event.clientId,
                petId = null,
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
            )
        }

        NotificationUtils.sleep(event.userId, 5, logger)

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, null)

        val title = buildString {
            append("🗑️ Programare Ștearsă")
            petName?.let { append(": $it") }
        }

        val message = buildString {
            clientName?.let { append(it) }
            if (petName != null) append(" - $petName")
            append(" din ${event.appointmentDate.format(dateFormatter)} a fost ștearsă din sistem")
        }

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_DELETED"
        )
    }

    @EventListener
    fun onAppointmentReminder(event: AppointmentReminderEvent) {
        logger.debug("Processing appointment reminder event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.dayBeforeReminders && event.reminderType == AppointmentReminderType.DAY_BEFORE) {
            personalizedSmsService.sendAppointmentReminderNotification(
                clientId = event.clientId,
                petId = null,
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
                startTime = event.startTime,
                reminderType = event.reminderType,
            )
        }

        NotificationUtils.sleep(event.userId, 5, logger)

        // Create push notification for staff
        val reminderTypeText = when (event.reminderType) {
            AppointmentReminderType.DAY_BEFORE -> "cu o zi înainte"
            AppointmentReminderType.SIX_HOURS_BEFORE -> "cu 6 ore înainte"
        }

        val (clientName, petName) = getNames(event.clientId, null)

        val title = buildString {
            append("Reminder Programare")
            petName?.let { append(": $it") }
        }

        val message = buildString {
            clientName?.let { append(it) }
            if (petName != null) append(" - $petName")
            append(" are programare maine la ${event.startTime.format(timeFormatter)} ($reminderTypeText)")
        }

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_REMINDER",
            startTime = event.startTime
        )
    }

    @EventListener
    fun onAppointmentCompleted(event: AppointmentCompletedEvent) {
        logger.debug("Processing appointment completed event for client: ${event.clientId.value}")
    }

    /**
     * Helper method to send push notifications to all staff members in a salon via Firebase
     */
    private fun createNotificationForAllStaff(
        salonId: SalonId?,
        appointmentId: AppointmentId,
        title: String,
        message: String,
        eventType: String,
        startTime: LocalTime? = null
    ) {
        try {
            if (salonId == null) {
                logger.warn("Cannot send notification: salon ID not found")
                return
            }

            logger.info("Creating push notification for all staff - Salon: $salonId, Appointment: $appointmentId")

            // Get all active staff members for the salon
            val staffMembers = staffRepository.findActiveBySalonWithUserDetails(salonId)

            if (staffMembers.isEmpty()) {
                logger.warn("No active staff members found for salon $salonId")
                return
            }

            // Extract user IDs from staff members
            val userIds = staffMembers.map { staff ->
                staff.userId
            }

            if (userIds.isEmpty()) {
                logger.warn("No staff members with user details found for salon $salonId")
                return
            }

            logger.info("Sending notification to ${userIds.size} staff members in salon $salonId: ${userIds.map { it.value }}")

            val dataMap = mutableMapOf(
                "appointmentId" to appointmentId.value,
                "salonId" to salonId.value,
                "eventType" to eventType,
                "timestamp" to System.currentTimeMillis().toString()
            )
            startTime?.let { dataMap["startTime"] = it.toString() }

            // Send notification to all staff members
            val result = firebaseService.sendNotificationToUsers(
                userIds = userIds,
                salonId = salonId,
                title = title,
                body = message,
                data = dataMap,
                appointmentId = appointmentId
            )

            if (result.successfulUsers > 0) {
                logger.info("Successfully sent push notification to ${result.totalDevicesNotified} devices for ${result.successfulUsers}/${result.totalUsers} staff members: $title")
            } else {
                logger.warn("Failed to send push notification to any staff members. Total users: ${result.totalUsers}")
            }

        } catch (e: Exception) {
            logger.error("Failed to send push notification to staff for appointment $appointmentId", e)
        }
    }

}
