package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.SalonSubscriptionDto
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.SubscriptionFeaturesDto
import ro.animaliaprogramari.animalia.domain.model.SalonSubscription
import ro.animaliaprogramari.animalia.domain.model.SubscriptionFeatures

/**
 * Mapper between SalonSubscription domain model and DTOs
 */
@Component
class SalonSubscriptionDtoMapper {

    /**
     * Convert domain model to DTO
     */
    fun toDto(subscription: SalonSubscription): SalonSubscriptionDto {
        return SalonSubscriptionDto(
            id = subscription.id.value,
            salonId = subscription.salonId.value,
            tier = subscription.tier,
            status = subscription.status,
            startDate = subscription.startDate,
            endDate = subscription.endDate,
            isActive = subscription.isActive,
            autoRenew = subscription.autoRenew,
            monthlyPrice = subscription.monthlyPrice,
            billingCycle = subscription.billingCycle,
            createdAt = subscription.createdAt,
            updatedAt = subscription.updatedAt,
            lastBillingDate = subscription.lastBillingDate,
            nextBillingDate = subscription.nextBillingDate,
            trialEndDate = subscription.trialEndDate,
            cancelledAt = subscription.cancelledAt,
            cancellationReason = subscription.cancellationReason
        )
    }

    /**
     * Convert subscription features to DTO
     */
    fun toFeaturesDto(features: SubscriptionFeatures): SubscriptionFeaturesDto {
        return SubscriptionFeaturesDto(
            tier = features.tier,
            smsQuota = features.smsQuota,
            canInviteTeamMembers = features.canInviteTeamMembers,
            canChangeSalon = features.canChangeSalon,
            maxTeamMembers = features.maxTeamMembers,
            description = features.description
        )
    }

    /**
     * Convert list of domain models to DTOs
     */
    fun toDtoList(subscriptions: List<SalonSubscription>): List<SalonSubscriptionDto> {
        return subscriptions.map { toDto(it) }
    }
}
