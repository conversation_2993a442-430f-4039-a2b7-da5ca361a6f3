package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.WorkingHoursMapper
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.WorkingHoursSettings

/**
 * Implementation of working hours repository using JPA
 * This is an outbound adapter that implements the repository port
 */
@Repository
@Transactional
class WorkingHoursRepositoryImpl(
    private val springWorkingHoursRepository: SpringWorkingHoursRepository,
    private val springWeeklyScheduleRepository: SpringWeeklyScheduleRepository,
    private val springHolidayRepository: SpringHolidayRepository,
    private val springCustomClosureRepository: SpringCustomClosureRepository,
    private val workingHoursMapper: WorkingHoursMapper,
) : WorkingHoursRepository {
    override fun findBySalonId(salonId: SalonId): WorkingHoursSettings {
        val salonIdValue = salonId.value

        // Check if working hours settings exist
        if (!springWorkingHoursRepository.existsBySalonId(salonIdValue)) {
            throw IllegalArgumentException("salonId '$salonIdValue' does not exist")
        }

        // Get all components
        val weeklySchedule = springWeeklyScheduleRepository.findBySalonIdOrderByDayOfWeek(salonIdValue)
        val holidays = springHolidayRepository.findBySalonIdOrderByDate(salonIdValue)
        val customClosures = springCustomClosureRepository.findBySalonIdOrderByDate(salonIdValue)

        return workingHoursMapper.toDomain(salonId, weeklySchedule, holidays, customClosures)
    }

    @Transactional
    override fun save(workingHours: WorkingHoursSettings): WorkingHoursSettings {
        val salonIdValue = workingHours.salonId.value

        // Save or update working hours settings
        val settingsEntity =
            ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WorkingHoursSettings(
                salonId = salonIdValue,
                updatedAt = workingHours.updatedAt,
            )
        springWorkingHoursRepository.save(settingsEntity)

        // Delete existing schedule, holidays, and closures
        springWeeklyScheduleRepository.deleteBySalonId(salonIdValue)
        springHolidayRepository.deleteBySalonId(salonIdValue)
        springCustomClosureRepository.deleteBySalonId(salonIdValue)

        // Flush to ensure deletes are executed before inserts
        springWeeklyScheduleRepository.flush()
        springHolidayRepository.flush()
        springCustomClosureRepository.flush()

        // Save new data
        val weeklyScheduleEntities = workingHoursMapper.toWeeklyScheduleEntities(workingHours)
        val holidayEntities = workingHoursMapper.toHolidayEntities(workingHours)
        val customClosureEntities = workingHoursMapper.toCustomClosureEntities(workingHours)

        springWeeklyScheduleRepository.saveAll(weeklyScheduleEntities)
        springHolidayRepository.saveAll(holidayEntities)
        springCustomClosureRepository.saveAll(customClosureEntities)

        return workingHours
    }

    @Transactional
    override fun deleteBySalonId(salonId: SalonId) {
        val salonIdValue = salonId.value

        springWeeklyScheduleRepository.deleteBySalonId(salonIdValue)
        springHolidayRepository.deleteBySalonId(salonIdValue)
        springCustomClosureRepository.deleteBySalonId(salonIdValue)
        springWorkingHoursRepository.deleteBySalonId(salonIdValue)
    }

    override fun existsBySalonId(salonId: SalonId): Boolean {
        return springWorkingHoursRepository.existsBySalonId(salonId.value)
    }
}
