package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

@Entity
@Table(
    name = "clients",
    indexes = [
        Index(name = "idx_clients_salon_active", columnList = "salon_id, is_active"),
        Index(name = "idx_clients_phone_active", columnList = "phone, is_active"),
        Index(name = "idx_clients_email_active", columnList = "email, is_active"),
        Index(name = "idx_clients_name", columnList = "name")
    ]
)
data class Client(
    @Id
    val id: String,
    @Column(name = "salon_id", nullable = true)
    val salonId: String? = null,
    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @Column(nullable = false)
    val name: String,
    @field:Size(max = 50, message = "Phone must not exceed 50 characters")
    val phone: String? = null,
    @field:Size(max = 255, message = "Email must not exceed 255 characters")
    val email: String? = null,
    @Column(columnDefinition = "TEXT")
    val address: String? = null,
    @Column(columnDefinition = "TEXT")
    val notes: String? = null,
    @Column(name = "is_active", columnDefinition = "BOOLEAN DEFAULT true")
    val isActive: Boolean = true,
    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now(),
    @OneToMany(mappedBy = "client", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val pets: List<Pet> = emptyList(),
) {
    // Default constructor for JPA
    constructor() : this("", null, "", null, null, null, null, true, LocalDateTime.now(), LocalDateTime.now(), emptyList())
}
