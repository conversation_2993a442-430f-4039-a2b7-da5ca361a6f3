package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonSettings
import java.util.UUID

@Repository
interface SpringSalonSettingsRepository : JpaRepository<SalonSettings, UUID> {
    fun findByKey(key: String): SalonSettings?

    fun findByKeyIn(keys: List<String>): List<SalonSettings>

    fun existsByKey(key: String): <PERSON><PERSON><PERSON>
}
