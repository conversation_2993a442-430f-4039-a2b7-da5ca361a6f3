package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonSubscriptionEntity
import ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier
import java.time.LocalDateTime

/**
 * Spring Data JPA repository for salon subscription entities
 */
@Repository
interface SpringSalonSubscriptionRepository : JpaRepository<SalonSubscriptionEntity, String> {
    
    /**
     * Find active subscription for a salon
     */
    @Query("""
        SELECT s FROM SalonSubscriptionEntity s 
        WHERE s.salonId = :salonId 
        AND s.isActive = true 
        AND s.status = 'ACTIVE'
        AND s.startDate <= :now
        AND (s.endDate IS NULL OR s.endDate > :now)
        ORDER BY s.createdAt DESC
    """)
    fun findActiveBySalonId(
        @Param("salonId") salonId: String,
        @Param("now") now: LocalDateTime = LocalDateTime.now()
    ): SalonSubscriptionEntity?
    
    /**
     * Find all subscriptions for a salon
     */
    fun findAllBySalonIdOrderByCreatedAtDesc(salonId: String): List<SalonSubscriptionEntity>
    
    /**
     * Find subscriptions by status
     */
    fun findByStatusOrderByCreatedAtDesc(status: SubscriptionStatus): List<SalonSubscriptionEntity>
    
    /**
     * Find subscriptions by tier
     */
    fun findByTierOrderByCreatedAtDesc(tier: SubscriptionTier): List<SalonSubscriptionEntity>
    
    /**
     * Find subscriptions that need billing
     */
    @Query("""
        SELECT s FROM SalonSubscriptionEntity s 
        WHERE s.isActive = true 
        AND s.status = 'ACTIVE'
        AND s.nextBillingDate <= :beforeDate
        AND s.autoRenew = true
    """)
    fun findSubscriptionsForBilling(@Param("beforeDate") beforeDate: LocalDateTime): List<SalonSubscriptionEntity>
    
    /**
     * Find expired subscriptions
     */
    @Query("""
        SELECT s FROM SalonSubscriptionEntity s 
        WHERE s.endDate IS NOT NULL 
        AND s.endDate <= :now
        AND s.status != 'EXPIRED'
    """)
    fun findExpiredSubscriptions(@Param("now") now: LocalDateTime = LocalDateTime.now()): List<SalonSubscriptionEntity>
    
    /**
     * Find subscriptions ending trial period
     */
    @Query("""
        SELECT s FROM SalonSubscriptionEntity s 
        WHERE s.trialEndDate IS NOT NULL 
        AND s.trialEndDate <= :beforeDate
        AND s.status = 'TRIAL'
    """)
    fun findTrialEndingSubscriptions(@Param("beforeDate") beforeDate: LocalDateTime): List<SalonSubscriptionEntity>
    
    /**
     * Check if salon has any active subscription
     */
    @Query("""
        SELECT COUNT(s) > 0 FROM SalonSubscriptionEntity s 
        WHERE s.salonId = :salonId 
        AND s.isActive = true 
        AND s.status = 'ACTIVE'
        AND s.startDate <= :now
        AND (s.endDate IS NULL OR s.endDate > :now)
    """)
    fun hasActiveSubscription(
        @Param("salonId") salonId: String,
        @Param("now") now: LocalDateTime = LocalDateTime.now()
    ): Boolean
    
    /**
     * Count subscriptions by tier
     */
    fun countByTier(tier: SubscriptionTier): Long
    
    /**
     * Count active subscriptions
     */
    @Query("""
        SELECT COUNT(s) FROM SalonSubscriptionEntity s 
        WHERE s.isActive = true 
        AND s.status = 'ACTIVE'
        AND s.startDate <= :now
        AND (s.endDate IS NULL OR s.endDate > :now)
    """)
    fun countActiveSubscriptions(@Param("now") now: LocalDateTime = LocalDateTime.now()): Long
}
