package ro.animaliaprogramari.animalia.adapter.outbound.monitoring

import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.MonitoringService
import java.util.concurrent.TimeUnit

/**
 * Micrometer implementation of the MonitoringService port
 * This adapter uses Spring Boot Actuator and Micrometer to record metrics
 */
@Component
class MicrometerMonitoringAdapter(
    private val meterRegistry: MeterRegistry,
) : MonitoringService {
    private val logger = LoggerFactory.getLogger(MicrometerMonitoringAdapter::class.java)

    override fun recordMetric(
        name: String,
        value: Double,
        tags: Map<String, String>,
    ) {
        try {
            val tagList = convertToTags(tags)
            meterRegistry.gauge(name, tagList, value)
            logger.debug("Recorded metric: $name = $value with tags: $tags")
        } catch (e: Exception) {
            logger.error("Failed to record metric: $name", e)
        }
    }

    override fun incrementCounter(
        name: String,
        tags: Map<String, String>,
    ) {
        try {
            val tagList = convertToTags(tags)
            meterRegistry.counter(name, tagList).increment()
            logger.debug("Incremented counter: $name with tags: $tags")
        } catch (e: Exception) {
            logger.error("Failed to increment counter: $name", e)
        }
    }

    override fun recordTiming(
        name: String,
        timeMs: Long,
        tags: Map<String, String>,
    ) {
        try {
            val tagList = convertToTags(tags)
            meterRegistry.timer(name, tagList).record(timeMs, TimeUnit.MILLISECONDS)
            logger.debug("Recorded timing: $name = $timeMs ms with tags: $tags")
        } catch (e: Exception) {
            logger.error("Failed to record timing: $name", e)
        }
    }

    override fun recordEvent(
        eventType: String,
        details: Map<String, Any>,
    ) {
        try {
            // For events, we'll increment a counter with the event type as a tag
            val tags = mapOf("eventType" to eventType)
            incrementCounter("app.events", tags)

            // Log the event details for additional context
            logger.info("Event recorded: $eventType, details: $details")
        } catch (e: Exception) {
            logger.error("Failed to record event: $eventType", e)
        }
    }

    override fun startTimer(
        name: String,
        tags: Map<String, String>,
    ): MonitoringService.Timer {
        val startTime = System.currentTimeMillis()
        return object : MonitoringService.Timer {
            override fun stop() {
                val elapsedTime = System.currentTimeMillis() - startTime
                recordTiming(name, elapsedTime, tags)
            }

            override fun stop(success: Boolean, errorType: String?) {
                val elapsedTime = System.currentTimeMillis() - startTime
                val enhancedTags = tags.toMutableMap().apply {
                    put("success", success.toString())
                    errorType?.let { put("error_type", it) }
                }
                recordTiming(name, elapsedTime, enhancedTags)

                // Also record success/failure counter
                val counterTags = enhancedTags.toMutableMap().apply {
                    put("operation", name)
                }
                incrementCounter("operation.executions", counterTags)
            }
        }
    }

    override fun recordWorkflowExecution(
        workflowName: String,
        success: Boolean,
        durationMs: Long,
        errorType: String?,
        tags: Map<String, String>
    ) {
        try {
            val workflowTags = tags.toMutableMap().apply {
                put("workflow", workflowName)
                put("success", success.toString())
                errorType?.let { put("error_type", it) }
            }

            // Record timing
            recordTiming("workflow.execution", durationMs, workflowTags)

            // Record success/failure counter
            incrementCounter("workflow.executions", workflowTags)

            // Record success rate gauge
            val successValue = if (success) 1.0 else 0.0
            recordMetric("workflow.success_rate", successValue, mapOf("workflow" to workflowName))

            logger.debug("Recorded workflow execution: $workflowName, success: $success, duration: ${durationMs}ms")
        } catch (e: Exception) {
            logger.error("Failed to record workflow execution: $workflowName", e)
        }
    }

    override fun recordMethodExecution(
        methodName: String,
        className: String,
        success: Boolean,
        durationMs: Long,
        errorType: String?,
        tags: Map<String, String>
    ) {
        try {
            val methodTags = tags.toMutableMap().apply {
                put("method", methodName)
                put("class", className)
                put("success", success.toString())
                errorType?.let { put("error_type", it) }
            }

            // Record timing
            recordTiming("method.execution", durationMs, methodTags)

            // Record success/failure counter
            incrementCounter("method.executions", methodTags)

            // Record success rate gauge
            val successValue = if (success) 1.0 else 0.0
            recordMetric("method.success_rate", successValue, mapOf(
                "method" to methodName,
                "class" to className
            ))

            logger.debug("Recorded method execution: $className.$methodName, success: $success, duration: ${durationMs}ms")
        } catch (e: Exception) {
            logger.error("Failed to record method execution: $className.$methodName", e)
        }
    }

    /**
     * Convert a map of tags to a list of Micrometer Tag objects
     */
    private fun convertToTags(tags: Map<String, String>): List<Tag> {
        return tags.map { (key, value) -> Tag.of(key, value) }
    }
}
