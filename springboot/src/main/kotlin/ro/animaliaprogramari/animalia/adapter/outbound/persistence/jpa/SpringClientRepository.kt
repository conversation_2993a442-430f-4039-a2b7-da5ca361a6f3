package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Client

@Repository
interface SpringClientRepository : JpaRepository<Client, String> {
    @Query(
        "SELECT c FROM Client c WHERE " +
            "(:search IS NULL OR LOWER(c.name) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.phone) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.email) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%'))) AND " +
            "(:isActive IS NULL OR c.isActive = :isActive) AND " +
            "(:salonId IS NULL OR c.salonId = CAST(:salonId AS string))",
    )
    fun findBySearchAndActiveStatus(
        @Param("search") search: String?,
        @Param("isActive") isActive: Boolean?,
        @Param("salonId") salonId: String?,
    ): List<Client>

    @Query(
        "SELECT c FROM Client c WHERE c.salonId = :salonId AND " +
            "(:isActive IS NULL OR c.isActive = :isActive)",
    )
    fun findBySalonId(
        @Param("salonId") salonId: String,
        @Param("isActive") isActive: Boolean?,
    ): List<Client>

    fun findByEmailIgnoreCase(email: String): Client?

    fun findByPhoneAndSalonId(phone: String, salonId: String): Client?
}
