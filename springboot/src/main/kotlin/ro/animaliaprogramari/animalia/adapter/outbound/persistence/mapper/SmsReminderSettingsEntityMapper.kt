package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsReminderSettings as SmsReminderSettingsEntity
import ro.animaliaprogramari.animalia.domain.model.SmsReminderSettings as SmsReminderSettingsDomain

/**
 * Mapper between SMS reminder settings domain model and JPA entity
 */
@Component
class SmsReminderSettingsEntityMapper {
    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: SmsReminderSettingsDomain): SmsReminderSettingsEntity {
        return SmsReminderSettingsEntity(
            salonId = domain.salonId.value,
            enabled = domain.enabled,
            appointmentConfirmations = domain.appointmentConfirmations,
            dayBeforeReminders = domain.dayBeforeReminders,
            followUpMessages = domain.followUpMessages,
            selectedProvider = domain.selectedProvider,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SmsReminderSettingsEntity): SmsReminderSettingsDomain {
        return SmsReminderSettingsDomain(
            salonId = SalonId.of(entity.salonId),
            enabled = entity.enabled,
            appointmentConfirmations = entity.appointmentConfirmations,
            dayBeforeReminders = entity.dayBeforeReminders,
            followUpMessages = entity.followUpMessages,
            selectedProvider = entity.selectedProvider,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }
}
