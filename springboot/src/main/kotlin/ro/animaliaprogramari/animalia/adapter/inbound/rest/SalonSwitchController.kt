package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.SwitchCurrentSalonCommand
import ro.animaliaprogramari.animalia.application.port.inbound.UserSalonManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.query.GetUserStaffAssociationsQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for user salon management operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/user")
@CrossOrigin(origins = ["*"])
@Tag(name = "Salon Switch", description = "Operations for switching user's active salon")
class SalonSwitchController(
    private val userSalonManagementUseCase: UserSalonManagementUseCase,
    private val userRepository: UserRepository,
    private val salonRepository: SalonRepository,
    private val clientRepository: ClientRepository,
) {
    private val logger = LoggerFactory.getLogger(SalonSwitchController::class.java)

    /**
     * GET /api/user/{userId}/salons
     * Get user's salon associations
     */
    @Operation(summary = "List user's salons")
    @SwaggerApiResponse(responseCode = "200", description = "List retrieved")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping("/{userId}/salons")
    fun getUserSalons(
        @PathVariable userId: String,
    ): ResponseEntity<ApiResponse<List<UserSalonAssociationResponse>>> {
        logger.debug("REST request to get salons for user: $userId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Check if user can access this data (either their own data or admin)
            if (!currentUser.isAdmin() && currentUser.userId.value != userId) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Access denied"))
            }

            val query = GetUserStaffAssociationsQuery(UserId.of(userId))
            val staffAssociations = userSalonManagementUseCase.getUserStaffAssociations(query)

            // Get user to check current salon
            val user =
                userRepository.findById(UserId.of(userId))
                    ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("User not found"))

            val responses =
                staffAssociations.map { staff ->
                    val salon =
                        salonRepository.findById(staff.salonId)
                            ?: throw RuntimeException("Salon not found: ${staff.salonId.value}")

                    // Convert Staff to UserSalonAssociationResponse for API compatibility
                    UserSalonAssociationResponse(
                        id = "${staff.userId.value}_${staff.salonId.value}",
                        userId = staff.userId.value,
                        salonId = staff.salonId.value,
                        salon = mapToSalonResponse(salon),
                        groomerRole = staff.role.name,
                        clientDataPermission = staff.permissions.clientDataAccess.name,
                        isCurrentSalon = user.currentSalonId == staff.salonId,
                        clientCount = clientRepository.findBySalonId(salon.id).size,
                        createdAt = staff.createdAt,
                        updatedAt = staff.updatedAt,
                    )
                }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting user salons: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get user salons: ${e.message}"))
        }
    }

    /**
     * POST /api/user/{userId}/switch-salon
     * Switch active salon for user
     */
    @Operation(summary = "Switch current salon")
    @SwaggerApiResponse(responseCode = "200", description = "Salon switched")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @PostMapping("/{userId}/switch-salon")
    fun switchSalon(
        @PathVariable userId: String,
        @Valid @RequestBody request: SwitchSalonRequest,
    ): ResponseEntity<ApiResponse<SwitchSalonResponse>> {
        logger.debug("REST request to switch salon for user: $userId to salon: ${request.salonId}")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Check if user can perform this action (either their own data or admin)
            if (!currentUser.isAdmin() && currentUser.userId.value != userId) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Access denied"))
            }

            val command =
                SwitchCurrentSalonCommand(
                    userId = UserId.of(userId),
                    salonId = SalonId.of(request.salonId),
                )

            val updatedUser = userSalonManagementUseCase.switchCurrentSalon(command)

            val response =
                SwitchSalonResponse(
                    success = true,
                    currentSalonId = updatedUser.currentSalonId?.value,
                    message = "Successfully switched to salon",
                )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error switching salon: ${e.message}", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to switch salon: ${e.message}"))
        }
    }

    /**
     * Map salon domain model to response DTO
     */
    private fun mapToSalonResponse(salon: ro.animaliaprogramari.animalia.domain.model.Salon): SalonResponse {
        return SalonResponse(
            id = salon.id.value,
            name = salon.name,
            address = salon.address,
            city = salon.city,
            phone = salon.phone?.value,
            email = salon.email?.value,
            ownerId = salon.ownerId.value,
            isActive = salon.isActive,
            clientCount = clientRepository.findBySalonId(salon.id).size,
            createdAt = salon.createdAt,
            updatedAt = salon.updatedAt,
            description = salon.description,

        )
    }
}
