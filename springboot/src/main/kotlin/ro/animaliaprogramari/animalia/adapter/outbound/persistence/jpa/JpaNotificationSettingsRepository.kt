package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.NotificationSettingsEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationSettingsRepository
import ro.animaliaprogramari.animalia.domain.model.NotificationSettings
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * JPA adapter implementing the NotificationSettingsRepository port
 */
@Repository
class JpaNotificationSettingsRepository(
    private val springRepository: SpringNotificationSettingsRepository,
    private val mapper: NotificationSettingsEntityMapper,
) : NotificationSettingsRepository {
    override fun save(settings: NotificationSettings): NotificationSettings {
        val entity = mapper.toEntity(settings)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findByUserIdAndSalonId(userId: UserId, salonId: SalonId): NotificationSettings? {
        return springRepository.findByUserIdAndSalonId(userId.value, salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun existsByUserIdAndSalonId(userId: UserId, salonId: SalonId): Boolean {
        return springRepository.existsByUserIdAndSalonId(userId.value, salonId.value)
    }

    override fun deleteByUserIdAndSalonId(userId: UserId, salonId: SalonId) {
        springRepository.deleteByUserIdAndSalonId(userId.value, salonId.value)
    }

    override fun findBySalonId(salonId: SalonId): List<NotificationSettings> {
        return springRepository.findBySalonId(salonId.value)
            .map { mapper.toDomain(it) }
    }
}
