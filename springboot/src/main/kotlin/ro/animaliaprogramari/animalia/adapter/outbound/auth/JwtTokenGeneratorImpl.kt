package ro.animaliaprogramari.animalia.adapter.outbound.auth

import io.jsonwebtoken.*
import io.jsonwebtoken.security.Keys
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.JwtTokenGenerator
import ro.animaliaprogramari.animalia.application.port.outbound.JwtTokenValidationResult
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.JwtToken
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.time.ZoneId
import java.util.*
import javax.crypto.SecretKey

/**
 * JWT token generator implementation using JJWT library
 * This is an outbound adapter that implements the JwtTokenGenerator port
 */
@Component
class JwtTokenGeneratorImpl(
    @Value("\${jwt.secret}") private val jwtSecret: String,
    @Value("\${jwt.expiration:86400000}") private val jwtExpirationMs: Long,
) : JwtTokenGenerator {
    private val logger = LoggerFactory.getLogger(JwtTokenGeneratorImpl::class.java)
    private val secretKey: SecretKey = createSecureKey(jwtSecret)

    private fun createSecureKey(secret: String): SecretKey {
        // Create a secure key by hashing the secret to ensure it meets HS512 requirements
        val digest = MessageDigest.getInstance("SHA-512")
        val hashedSecret = digest.digest(secret.toByteArray(StandardCharsets.UTF_8))
        return Keys.hmacShaKeyFor(hashedSecret)
    }

    override fun generateToken(user: AuthenticatedUser): JwtToken {
        try {
            val now = Date()
            val expiryDate = Date(now.time + jwtExpirationMs)

            val token =
                Jwts.builder()
                    .setSubject(user.userId.value)
                    .claim("firebaseUid", user.firebaseUid)
                    .claim("email", user.email?.value ?: user.phoneNumber ?: "unknown")
                    .claim("phoneNumber", user.phoneNumber)
                    .claim("name", user.name)
                    .claim("role", user.role.name)
                    .claim("isActive", user.isActive)
                    .setIssuedAt(now)
                    .setExpiration(expiryDate)
                    .signWith(secretKey, SignatureAlgorithm.HS512)
                    .compact()

            val expiresAt =
                expiryDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime()

            logger.debug("Generated JWT token for user: ${user.userId.value}")

            return JwtToken.create(token, expiresAt)
        } catch (e: Exception) {
            logger.error("Failed to generate JWT token for user: ${user.userId.value}", e)
            throw RuntimeException("Token generation failed", e)
        }
    }

    override fun validateToken(token: String): JwtTokenValidationResult {
        try {
            logger.info("=== JWT TOKEN PARSING START ===")
            logger.info("Token length: ${token.length}")
            logger.info("Secret key configured: ${secretKey != null}")

            val claims =
                Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .body

            logger.info("=== JWT TOKEN PARSING SUCCESS ===")
            logger
                .info("Subject (User ID): ${claims.subject}")
            logger
                .info("Firebase UID: ${claims.get("firebaseUid", String::class.java)}")
            logger.info("Email: ${claims.get("email", String::class.java)}")
            logger.info("Name: ${claims.get("name", String::class.java)}")
            logger.info("Role: ${claims.get("role", String::class.java)}")
            logger.info("Is Active: ${claims.get("isActive")}")
            logger.info("Issued At: ${claims.issuedAt}")
            logger.info("Expires At: ${claims.expiration}")

            return JwtTokenValidationResult.success(
                userId = claims.subject,
                firebaseUid = claims.get("firebaseUid", String::class.java),
                email = claims.get("email", String::class.java),
                name = claims.get("name", String::class.java),
                role = claims.get("role", String::class.java),
                isActive = (claims.get("isActive") as? Boolean) ?: false,
            )
        } catch (e: ExpiredJwtException) {
            logger.error("=== JWT TOKEN EXPIRED ===")
            logger.error("Token expired at: ${e.claims.expiration}")
            logger.error("Current time: ${java.util.Date()}")
            return JwtTokenValidationResult.failure("Token expired")
        } catch (e: UnsupportedJwtException) {
            logger.error("=== JWT TOKEN UNSUPPORTED ===")
            logger.error("Unsupported JWT token: ${e.message}")
            return JwtTokenValidationResult.failure("Unsupported token")
        } catch (e: MalformedJwtException) {
            logger.error("=== JWT TOKEN MALFORMED ===")
            logger.error("Malformed JWT token: ${e.message}")
            return JwtTokenValidationResult.failure("Malformed token")
        } catch (e: SignatureException) {
            logger.error("=== JWT SIGNATURE INVALID ===")
            logger.error("Invalid JWT signature: ${e.message}")
            return JwtTokenValidationResult.failure("Invalid token signature")
        } catch (e: IllegalArgumentException) {
            logger.error("=== JWT TOKEN INVALID ARGUMENTS ===")
            logger.error("JWT token compact of handler are invalid: ${e.message}")
            return JwtTokenValidationResult.failure("Invalid token")
        } catch (e: Exception) {
            logger.error("=== JWT VALIDATION UNEXPECTED ERROR ===")
            logger.error("Exception type: ${e.javaClass.simpleName}")
            logger.error("Exception message: ${e.message}")
            logger.error("Stack trace: ", e)
            return JwtTokenValidationResult.failure("Token validation failed")
        }
    }

    override fun refreshToken(token: String): JwtToken? {
        try {
            val validationResult = validateToken(token)
            if (!validationResult.isValid) {
                return null
            }

            // Parse the original token to get all claims
            val claims =
                Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .body

            // Create a new token with extended expiration
            val now = Date()
            val expiryDate = Date(now.time + jwtExpirationMs)

            val newToken =
                Jwts.builder()
                    .setSubject(validationResult.userId)
                    .claim("firebaseUid", validationResult.firebaseUid)
                    .claim("email", validationResult.email)
                    .claim("phoneNumber", claims.get("phoneNumber", String::class.java))
                    .claim("name", validationResult.name)
                    .claim("role", validationResult.role)
                    .claim("isActive", validationResult.isActive)
                    .setIssuedAt(now)
                    .setExpiration(expiryDate)
                    .signWith(secretKey, SignatureAlgorithm.HS512)
                    .compact()

            val expiresAt =
                expiryDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime()

            return JwtToken.create(newToken, expiresAt)
        } catch (e: Exception) {
            logger.error("Failed to refresh JWT token", e)
            return null
        }
    }
}
