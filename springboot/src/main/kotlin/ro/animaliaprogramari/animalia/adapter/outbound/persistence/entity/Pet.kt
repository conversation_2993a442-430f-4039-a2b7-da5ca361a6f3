package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.time.LocalDateTime

@Entity
@Table(
    name = "pets",
    indexes = [
        Index(name = "idx_pets_client_active", columnList = "client_id, is_active"),
        Index(name = "idx_pets_name_client", columnList = "name, client_id"),
        Index(name = "idx_pets_breed_size", columnList = "breed, size"),
        Index(name = "idx_pets_species_breed", columnList = "species, breed")
    ]
)
data class Pet(
    @Id
    var id: String = "",
    @field:NotBlank(message = "Client ID is required")
    @Column(name = "client_id", nullable = false)
    var clientId: String = "",
    @field:NotBlank(message = "Pet name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @Column(nullable = false)
    var name: String = "",
    @field:Size(max = 255, message = "Breed must not exceed 255 characters")
    var breed: String? = null,
    @field:Size(max = 255, message = "Breed must not exceed 255 characters")
    var species: String? = null,
    @field:Size(max = 255, message = "Breed must not exceed 255 characters")
    var size: String? = null,
    @field:Positive(message = "Age must be positive")
    var age: Int? = null,
    @Column(precision = 5, scale = 2)
    var weight: BigDecimal? = null,
    @field:Size(max = 100, message = "Color must not exceed 100 characters")
    var color: String? = null,
    @field:Size(max = 20, message = "Gender must not exceed 20 characters")
    var gender: String? = null,
    @Column(columnDefinition = "TEXT")
    var notes: String? = null,
    @Column(name = "medical_conditions", columnDefinition = "TEXT")
    var medicalConditions: String? = null,
    @Column(name = "is_active", columnDefinition = "BOOLEAN DEFAULT true")
    var isActive: Boolean = true,
    @Column(name = "created_at")
    var createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now(),
    @field:Size(max = 500, message = "Photo URL must not exceed 500 characters")
    @Column(name = "photo_url")
    var photoUrl: String? = null,
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id", insertable = false, updatable = false)
    var client: Client? = null,
) {
    // Default constructor for JPA
    constructor() : this("", "", "", null, null, null, null, null, null, null, null, null, true, LocalDateTime.now(), LocalDateTime.now(), null, null)
}
