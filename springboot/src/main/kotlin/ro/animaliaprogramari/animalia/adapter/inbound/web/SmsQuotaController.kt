package ro.animaliaprogramari.animalia.adapter.inbound.web

import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.service.SmsQuotaService
import ro.animaliaprogramari.animalia.domain.service.SmsQuotaStatus

/**
 * REST controller for SMS quota management
 */
@RestController
@RequestMapping("/api/sms/quota")
class SmsQuotaController(
    private val smsQuotaService: SmsQuotaService
) {
    private val logger = LoggerFactory.getLogger(SmsQuotaController::class.java)

    /**
     * Get SMS quota status for a salon
     */
    @GetMapping("/{salonId}")
    fun getQuotaStatus(@PathVariable salonId: String): ResponseEntity<SmsQuotaStatus> {
        return try {
            val status = smsQuotaService.getQuotaStatus(SalonId.of(salonId))
            logger.debug("Retrieved SMS quota status for salon: $salonId")
            ResponseEntity.ok(status)
        } catch (e: Exception) {
            logger.error("Failed to get SMS quota status for salon: $salonId", e)
            ResponseEntity.internalServerError().build()
        }
    }

    /**
     * Update total quota for a salon
     */
    @PutMapping("/{salonId}/total")
    fun updateTotalQuota(
        @PathVariable salonId: String,
        @RequestBody request: UpdateQuotaRequest
    ): ResponseEntity<SmsQuotaStatus> {
        return try {
            val updatedQuota = smsQuotaService.updateTotalQuota(SalonId.of(salonId), request.totalQuota)
            val status = smsQuotaService.getQuotaStatus(SalonId.of(salonId))
            logger.info("Updated SMS quota for salon: $salonId to ${request.totalQuota}")
            ResponseEntity.ok(status)
        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid quota update request for salon: $salonId - ${e.message}")
            ResponseEntity.badRequest().build()
        } catch (e: Exception) {
            logger.error("Failed to update SMS quota for salon: $salonId", e)
            ResponseEntity.internalServerError().build()
        }
    }

    /**
     * Reset quota for a salon (start new period)
     */
    @PostMapping("/{salonId}/reset")
    fun resetQuota(@PathVariable salonId: String): ResponseEntity<SmsQuotaStatus> {
        return try {
            val resetQuota = smsQuotaService.resetQuota(SalonId.of(salonId))
            val status = smsQuotaService.getQuotaStatus(SalonId.of(salonId))
            logger.info("Reset SMS quota for salon: $salonId")
            ResponseEntity.ok(status)
        } catch (e: Exception) {
            logger.error("Failed to reset SMS quota for salon: $salonId", e)
            ResponseEntity.internalServerError().build()
        }
    }

    /**
     * Get remaining SMS count for a salon
     */
    @GetMapping("/{salonId}/remaining")
    fun getRemainingQuota(@PathVariable salonId: String): ResponseEntity<RemainingQuotaResponse> {
        return try {
            val remaining = smsQuotaService.getRemainingQuota(SalonId.of(salonId))
            val response = RemainingQuotaResponse(
                salonId = salonId,
                remainingQuota = remaining
            )
            logger.debug("Retrieved remaining SMS quota for salon: $salonId - $remaining")
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            logger.error("Failed to get remaining SMS quota for salon: $salonId", e)
            ResponseEntity.internalServerError().build()
        }
    }

    /**
     * Check if SMS can be sent for a salon
     */
    @GetMapping("/{salonId}/can-send")
    fun canSendSms(@PathVariable salonId: String): ResponseEntity<CanSendSmsResponse> {
        return try {
            val canSend = smsQuotaService.canSendSms(SalonId.of(salonId))
            val response = CanSendSmsResponse(
                salonId = salonId,
                canSend = canSend
            )
            logger.debug("Checked SMS send capability for salon: $salonId - $canSend")
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            logger.error("Failed to check SMS send capability for salon: $salonId", e)
            ResponseEntity.internalServerError().build()
        }
    }
}

/**
 * Request DTO for updating quota
 */
data class UpdateQuotaRequest(
    val totalQuota: Int
)

/**
 * Response DTO for remaining quota
 */
data class RemainingQuotaResponse(
    val salonId: String,
    val remainingQuota: Int
)

/**
 * Response DTO for can send SMS check
 */
data class CanSendSmsResponse(
    val salonId: String,
    val canSend: Boolean
)
