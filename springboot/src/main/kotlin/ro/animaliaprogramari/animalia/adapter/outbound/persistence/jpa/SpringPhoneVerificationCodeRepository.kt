package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.PhoneVerificationCode
import java.time.LocalDateTime

@Repository
interface SpringPhoneVerificationCodeRepository : JpaRepository<PhoneVerificationCode, String> {
    
    @Query(
        "SELECT pvc FROM PhoneVerificationCode pvc WHERE pvc.phoneNumber = :phoneNumber " +
        "ORDER BY pvc.createdAt DESC"
    )
    fun findByPhoneNumberOrderByCreatedAtDesc(@Param("phoneNumber") phoneNumber: String): List<PhoneVerificationCode>

    @Query(
        "SELECT pvc FROM PhoneVerificationCode pvc WHERE pvc.phoneNumber = :phoneNumber " +
        "AND pvc.isUsed = false AND pvc.expiresAt > :now " +
        "ORDER BY pvc.createdAt DESC"
    )
    fun findActiveByPhoneNumber(
        @Param("phoneNumber") phoneNumber: String,
        @Param("now") now: LocalDateTime
    ): List<PhoneVerificationCode>

    @Modifying
    @Query(
        "UPDATE PhoneVerificationCode pvc SET pvc.isUsed = true, pvc.updatedAt = :now " +
        "WHERE pvc.phoneNumber = :phoneNumber AND pvc.isUsed = false"
    )
    fun invalidateAllForPhoneNumber(
        @Param("phoneNumber") phoneNumber: String,
        @Param("now") now: LocalDateTime
    )

    @Modifying
    @Query("DELETE FROM PhoneVerificationCode pvc WHERE pvc.expiresAt < :now")
    fun deleteExpiredCodes(@Param("now") now: LocalDateTime)

    fun existsByPhoneNumber(phoneNumber: String): Boolean
}
