package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import kotlin.reflect.KClass

/**
 * Validation annotation for Romanian phone numbers
 * Accepts various formats and normalizes them to +40XXXXXXXXX
 */
@Target(AnnotationTarget.FIELD, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [RomanianPhoneNumberValidator::class])
@MustBeDocumented
annotation class ValidRomanianPhoneNumber(
    val message: String = "Phone number must be in Romanian format. Accepted formats: +40XXXXXXXXX, +40 XXX XXX XXX, 0XXXXXXXXX, etc.",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = []
)

/**
 * Validator for Romanian phone numbers
 */
class RomanianPhoneNumberValidator : ConstraintValidator<ValidRomanianPhoneNumber, String> {

    override fun isValid(value: String?, context: ConstraintValidatorContext?): Boolean {
        if (value.isNullOrBlank()) {
            return false
        }

        // Use PhoneNumber utility to validate and normalize
        return PhoneNumber.validateAndNormalize(value) != null
    }
}
