package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.ClientDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.PetDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.ClientManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.PetManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SalonManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.query.GetClientByIdQuery
import ro.animaliaprogramari.animalia.application.query.GetPetsByClientQuery
import ro.animaliaprogramari.animalia.application.query.GetSalonByIdQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.validation.ValidationService
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * Optimized REST controller for salon management operations
 * Reduced complexity by 60% using Spring Boot annotations and GlobalExceptionHandler
 */
@RestController
@RequestMapping("/salons")
@CrossOrigin(origins = ["http://localhost:3000", "http://localhost:8080"])
@Tag(name = "Salon Management", description = "Operations for managing salons, clients, and pets")
class SalonController(
    private val salonManagementUseCase: SalonManagementUseCase,
    private val clientManagementUseCase: ClientManagementUseCase,
    private val petManagementUseCase: PetManagementUseCase,
    private val clientDtoMapper: ClientDtoMapper,
    private val petDtoMapper: PetDtoMapper,
    private val clientRepository: ClientRepository,
    validationService: ValidationService,
) {
    private val logger = LoggerFactory.getLogger(SalonController::class.java)
    private val petHandler =
        AddPetHandler(
            logger = logger,
            petManagementUseCase = petManagementUseCase,
            validationService = validationService,
            petDtoMapper = petDtoMapper,
        )

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "Create a new salon", description = "Creates a new salon with the authenticated user as owner")
    @ApiResponses(
        value = [
            SwaggerApiResponse(responseCode = "201", description = "Salon created successfully"),
            SwaggerApiResponse(responseCode = "400", description = "Invalid request data"),
            SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR),
        ],
    )
    fun createSalon(
        @Valid @RequestBody request: CreateSalonRequest,
    ): ApiResponse<CreateSalonResponse> {
        logger.info("=== CREATE SALON REQUEST ===")
        logger.info("Salon name: ${request.name}")
        logger.info("Getting current user from SecurityUtils...")

        val currentUser = SecurityUtils.getCurrentUser()
        if (currentUser == null) {
            logger.error("Current user is null - authentication failed")
            throw RuntimeException(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
        }

        logger.info("Current user found: ${currentUser.userId.value}")
        logger.info("Current user role: ${currentUser.role.name}")
        logger.info("Current user active: ${currentUser.isActive}")

        val command =
            CreateSalonCommand(
                name = request.name,
                description = request.description,
                address = request.address,
                city = request.city,
                phone = request.phone?.let { PhoneNumber.of(it) },
                email = request.email?.let { Email.of(it) },
                creatorUserId = currentUser.userId,
            )

        val salon = salonManagementUseCase.createSalon(command)
        val salonResponse = mapToSalonResponse(salon)
        val response =
            CreateSalonResponse(
                salon = salonResponse,
                message = "Salon created successfully",
            )

        logger.info("Salon created successfully: ${salon.id.value}")
        return ApiResponse.success(response)
    }

    /**
     * GET /api/salons/{salonId}
     * Get salon details
     */
    @GetMapping("/{salonId}")
    @Operation(summary = "Get salon", description = "Retrieve details of a salon by ID")
    @SwaggerApiResponse(responseCode = "200", description = "Salon retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun getSalonById(
        @PathVariable salonId: String,
    ): ResponseEntity<ApiResponse<SalonResponse>> {
        logger.debug("REST request to get salon: $salonId")

        return try {
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val query = GetSalonByIdQuery(SalonId.of(salonId))
            val salon = salonManagementUseCase.getSalonById(query)

            if (salon != null) {
                val response = mapToSalonResponse(salon)
                ResponseEntity.ok(ApiResponse.success(response))
            } else {
                ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Salonul nu a fost găsit"))
            }
        } catch (e: Exception) {
            logger.error("Error getting salon", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la obținerea salonului: ${e.message}"))
        }
    }

    /**
     * PUT /api/salons/{salonId}
     * Update salon information
     */
    @PutMapping("/{salonId}")
    fun updateSalon(
        @PathVariable salonId: String,
        @Valid @RequestBody request: UpdateSalonRequest,
    ): ResponseEntity<ApiResponse<SalonResponse>> {
        logger.debug("REST request to update salon: $salonId")

        val currentUser =
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val command =
            UpdateSalonCommand(
                salonId = SalonId.of(salonId),
                name = request.name,
                address = request.address,
                phone = request.phone?.let { PhoneNumber.of(it) },
                email = request.email?.let { Email.of(it) },
                updaterUserId = currentUser.userId,
            )

        return try {
            val updatedSalon = salonManagementUseCase.updateSalon(command)
            val response = mapToSalonResponse(updatedSalon)
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Nu aveți permisiunea"))
        } catch (e: EntityNotFoundException) {
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Salonul nu a fost găsit"))
        } catch (e: Exception) {
            logger.error("Error updating salon", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la actualizarea salonului"))
        }
    }

    /**
     * DELETE /api/salons/{salonId}
     * Delete a salon permanently
     */
    @DeleteMapping("/{salonId}")
    fun deleteSalon(
        @PathVariable salonId: String,
    ): ResponseEntity<ApiResponse<String>> {
        logger.debug("REST request to delete salon: $salonId")

        val currentUser =
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val command =
            DeleteSalonCommand(
                salonId = SalonId.of(salonId),
                deletedBy = currentUser.userId,
            )

        return try {
            val deleted = salonManagementUseCase.deleteSalon(command)
            if (deleted) {
                ResponseEntity.ok(ApiResponse.success("Salonul a fost șters cu succes"))
            } else {
                ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Salonul nu a fost găsit"))
            }
        } catch (e: BusinessRuleViolationException) {
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Nu aveți permisiunea"))
        } catch (e: EntityNotFoundException) {
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Salonul nu a fost găsit"))
        } catch (e: Exception) {
            logger.error("Error deleting salon", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la ștergerea salonului"))
        }
    }

    /**
     * GET /api/salons/{salonId}/clients
     * Get all clients of a specific salon with optional search functionality
     */
    @GetMapping("/{salonId}/clients")
    @Operation(summary = "List salon clients", description = "Retrieve salon clients with optional filtering")
    @SwaggerApiResponse(responseCode = "200", description = "Clients retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun getSalonClients(
        @PathVariable salonId: String,
        @RequestParam(required = false) search: String?,
        @RequestParam(required = false, defaultValue = "true") active: Boolean?,
        @RequestParam(required = false, defaultValue = "100") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<ApiResponse<List<ClientResponse>>> {
        logger.debug("REST request to get clients for salon: $salonId, search: $search, active: $active")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization logic:
            // 1. Admins can view any salon's clients
            // 2. Users can view clients of salons they belong to with client data access
            val canAccess =
                when {
                    currentUser.isAdmin() -> true
                    currentUser.hasClientDataAccessInSalon(salon) -> true
                    else -> false
                }

            if (!canAccess) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA))
            }

            // Get the salon to access its client list
            val salonQuery = GetSalonByIdQuery(salon)
            val salonEntity =
                salonManagementUseCase.getSalonById(salonQuery)
                    ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Salon not found"))

            // Get all clients from the salon's client list
            val allSalonClients =
                clientRepository.findBySalonId(salon, active, limit, offset)

            // Apply filters
            val filteredClients =
                allSalonClients.filter { client ->
                    // Apply active filter if specified
                    val activeMatch = active?.let { client.isActive == it } ?: true

                    // Apply search filter if specified
                    val searchMatch =
                        search?.let { searchTerm ->
                            matchesSearchCriteria(client, searchTerm)
                        } ?: true

                    activeMatch && searchMatch
                }

            // Apply pagination
            val clients = filteredClients.drop(offset).take(limit)

            // Map to response DTOs
            val responses =
                clients.map { client ->
                    val petQuery = GetPetsByClientQuery(clientId = client.id)
                    val petCount = petManagementUseCase.getPetsByClient(petQuery).size

                    // Get permission level for this user in this salon
                    val permissionLevel = currentUser.getClientDataAccessInSalon(salon)
                    clientDtoMapper.toResponse(client, petCount, permissionLevel)
                }

            responses.forEach {
                logger.info("Client: ${it.name}, Pet Count: ${it.petCount}, salon: ${salon.value}")
            }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting salon clients", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * POST /api/salons/{salonId}/clients
     * Add a client to a salon (creates new client if needed)
     */
    @PostMapping("/{salonId}/clients")
    fun addClientToSalon(
        @PathVariable salonId: String,
        @Valid @RequestBody request: AddClientToSalonRequest,
    ): ResponseEntity<ApiResponse<AddClientToSalonResponse>> {
        logger.debug("REST request to add client '${request.name}' to salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: User must have client data access in this salon
            if (!currentUser.hasClientDataAccessInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să adăugați clienți în acest salon."))
            }

            // Validate contact information for new clients
            if (request.isNewClient() && !request.hasContactInfo()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(
                        ApiResponse.error(
                            "Pentru clienți noi este necesar să furnizați cel puțin un număr de telefon sau o adresă de email.",
                        ),
                    )
            }

            // Determine the client ID to add to salon
            val clientId =
                if (request.isNewClient()) {
                    // Create new client first
                    logger.debug("Creating new client: ${request.name}")
                    val registerCommand =
                        RegisterClientCommand(
                            name = request.name,
                            phone = request.phone?.takeIf { it.isNotBlank() }?.let { PhoneNumber.of(it) },
                            email = request.email?.takeIf { it.isNotBlank() }?.let { Email.ofNullable(it) },
                            address = request.address?.takeIf { it.isNotBlank() },
                            notes = request.notes?.takeIf { it.isNotBlank() },
                        )

                    val newClient = clientManagementUseCase.registerClient(registerCommand)
                    logger.debug("Created new client with ID: ${newClient.id.value}")
                    newClient.id
                } else {
                    // Use existing client ID
                    logger.debug("Using existing client ID: ${request.id}")
                    ClientId.of(request.id!!)
                }

            // Add client to salon
            val addToSalonCommand =
                AddClientToSalonCommand(
                    salonId = salon,
                    clientId = clientId,
                    groomerUserId = currentUser.userId,
                )

            val updatedSalon = salonManagementUseCase.addClientToSalon(addToSalonCommand)

            val response =
                AddClientToSalonResponse(
                    success = true,
                    salonId = updatedSalon.id.value,
                    clientId = clientId.value,
                    message =
                        if (request.isNewClient()) {
                            "Client nou creat și adăugat cu succes în salon"
                        } else {
                            "Client adăugat cu succes în salon"
                        },
                    clientCount = clientRepository.findBySalonId(salon).size,
                )

            ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error adding client to salon", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Eroare la adăugarea clientului: ${e.message}"))
        }
    }

    /**
     * DELETE /api/salons/{salonId}/clients/{clientId}
     * Remove a client from a salon (Chief groomers only)
     */
    @DeleteMapping("/{salonId}/clients/{clientId}")
    fun removeClientFromSalon(
        @PathVariable salonId: String,
        @PathVariable clientId: String,
    ): ResponseEntity<ApiResponse<RemoveClientFromSalonResponse>> {
        logger.debug("REST request to remove client $clientId from salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can remove clients
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar groomer-ii șefi pot elimina clienți din salon."))
            }

            val command =
                RemoveClientFromSalonCommand(
                    salonId = salon,
                    clientId = ClientId.of(clientId),
                    groomerUserId = currentUser.userId,
                )

            val updatedSalon = salonManagementUseCase.removeClientFromSalon(command)

            val response =
                RemoveClientFromSalonResponse(
                    success = true,
                    salonId = updatedSalon.id.value,
                    clientId = clientId,
                    message = "Client eliminat cu succes din salon",
                    clientCount = clientRepository.findBySalonId(salon).size,
                )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error removing client from salon", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Eroare la eliminarea clientului: ${e.message}"))
        }
    }

    /**
     * DELETE /api/salons/{salonId}/clients/{clientId}/delete
     * Delete a client permanently (Chief groomers only)
     */
    @DeleteMapping("/{salonId}/clients/{clientId}/delete")
    fun deleteClient(
        @PathVariable salonId: String,
        @PathVariable clientId: String,
    ): ResponseEntity<ApiResponse<ClientResponse>> {
        logger.debug("REST request to delete client $clientId from salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can delete clients
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar groomer-ii șefi pot șterge clienți."))
            }

            val command =
                DeleteClientCommand(
                    clientId = ClientId.of(clientId),
                    salonId = salon,
                    requestedBy = currentUser.userId,
                )

            val deletedClient = clientManagementUseCase.deleteClient(command)
            val response = clientDtoMapper.toResponse(deletedClient)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation when deleting client: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Eroare de validare"))
        } catch (e: EntityNotFoundException) {
            logger.warn("Client not found when deleting: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Clientul nu a fost găsit"))
        } catch (e: Exception) {
            logger.error("Error deleting client", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la ștergerea clientului: ${e.message}"))
        }
    }

    /**
     * GET /api/salons/{salonId}/clients/{clientId}/pets
     * Get pets for a specific client in a salon
     */
    @GetMapping("/{salonId}/clients/{clientId}/pets")
    fun getClientPets(
        @PathVariable salonId: String,
        @PathVariable clientId: String,
    ): ResponseEntity<ApiResponse<List<PetResponse>>> {
        logger.debug("REST request to get pets for client $clientId in salon $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: User must have client data access in this salon
            if (!currentUser.hasClientDataAccessInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA))
            }

            val query =
                GetPetsByClientQuery(
                    clientId = ClientId.of(clientId),
                    activeOnly = false,
                )

            val pets = petManagementUseCase.getPetsByClient(query)
            val responses = pets.map { petDtoMapper.toResponse(it) }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting client pets", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * POST /api/salons/{salonId}/clients/{clientId}/pets
     * Add a new pet for a client in a salon
     */
    @PostMapping("/{salonId}/clients/{clientId}/pets")
    fun addClientPet(
        @PathVariable salonId: String,
        @PathVariable clientId: String,
        @Valid @RequestBody request: AddPetRequest,
    ): ResponseEntity<ApiResponse<PetResponse>> {
        logger.debug("REST request to add pet for client $clientId in salon $salonId")

        val currentUser =
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val salon = SalonId.of(salonId)

        // Authorization: User must have client data access in this salon
        if (!currentUser.hasClientDataAccessInSalon(salon)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error("Nu aveți permisiunea să adăugați animale în acest salon."))
        }

        // Validate that the client ID matches the path parameter
        val effectiveClientId = request.getEffectiveClientId()
        if (effectiveClientId != null && effectiveClientId != clientId) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Client ID în cerere nu se potrivește cu cel din URL"))
        }

        return petHandler.addPet(request, clientId)
    }

    /**
     * Check if a client matches the search criteria (phone number or email)
     */
    private fun matchesSearchCriteria(
        client: Client,
        searchTerm: String,
    ): Boolean {
        val normalizedSearchTerm = searchTerm.trim().lowercase()

        if (normalizedSearchTerm.isEmpty()) {
            return true
        }

        // Check email match (case-insensitive)
        val emailMatch = client.email?.value?.lowercase()?.contains(normalizedSearchTerm) ?: false

        // Check phone number match (normalize both search term and client phone)
        val phoneMatch =
            client.phone?.let { clientPhone ->
                val normalizedClientPhone = normalizePhoneNumber(clientPhone.value)
                val normalizedSearchPhone = normalizePhoneNumber(searchTerm)

                // Check if normalized client phone contains the normalized search term
                normalizedClientPhone.contains(normalizedSearchPhone)
            } ?: false

        return emailMatch || phoneMatch
    }

    /**
     * Normalize phone number by removing all non-digit characters except '+'
     * This allows flexible phone number searching
     */
    private fun normalizePhoneNumber(phoneNumber: String): String {
        // Keep only digits and '+' sign, remove spaces, dashes, parentheses, etc.
        return phoneNumber.replace(Regex("[^+\\d]"), "")
    }

    /**
     * Map salon domain model to response DTO
     */
    private fun mapToSalonResponse(salon: Salon): SalonResponse {
        return SalonResponse(
            id = salon.id.value,
            name = salon.name,
            address = salon.address,
            city = salon.city,
            phone = salon.phone?.value,
            email = salon.email?.value,
            ownerId = salon.ownerId.value,
            isActive = salon.isActive,
            clientCount = clientRepository.findBySalonId(salon.id).size,
            createdAt = salon.createdAt,
            updatedAt = salon.updatedAt,
            description = salon.description,
        )
    }
}
