package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.PetResponse
import ro.animaliaprogramari.animalia.domain.model.Gender
import ro.animaliaprogramari.animalia.domain.model.Pet
import java.time.LocalDateTime

/**
 * Mapper for converting between domain models and DTOs for pets
 */
@Component
class PetDtoMapper {
    /**
     * Convert domain model to response DTO
     */
    fun toResponse(pet: Pet): PetResponse {
        // Calculate birth date from age if available
        val birthDate =
            pet.age?.let { age ->
                LocalDateTime.now().minusYears(age.toLong())
            }

        return PetResponse(
            id = pet.id.value,
            clientId = pet.clientId.value,
            ownerId = pet.clientId.value, // Same as clientId for Flutter compatibility
            name = pet.name,
            species = inferSpeciesFromBreed(pet.breed), // Try to infer species from breed
            breed = pet.breed,
            gender = pet.gender?.name?.lowercase(),
            birthDate = birthDate,
            age = pet.age,
            weight = pet.weight?.toDouble(),
            color = pet.color,
            microchipNumber = null, // TODO: Add to domain model if needed
            vaccinations = emptyList(), // TODO: Add to domain model if needed
            notes = pet.notes,
            photoUrl = pet.photoUrl,
            medicalConditions = pet.medicalConditions,
            isActive = pet.isActive,
            createdAt = pet.createdAt,
            updatedAt = pet.updatedAt,
            isSenior = pet.isSenior(),
            hasMedicalConditions = pet.hasMedicalConditions(),
        )
    }

    private fun inferSpeciesFromBreed(breed: String?): String? {
        // TODO
        return ""
    }

    /**
     * Convert string to Gender enum
     */
    fun toGender(gender: String?): Gender? {
        return gender?.let {
            try {
                Gender.valueOf(it.uppercase())
            } catch (e: IllegalArgumentException) {
                null
            }
        }
    }
}
