package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Appointment
import java.time.LocalDate
import java.time.LocalTime

@Repository
interface SpringAppointmentRepository : JpaRepository<Appointment, String> {
    fun findByAppointmentDateBetween(
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<Appointment>

    fun findByClientId(clientId: String): List<Appointment>

    fun findByPetId(petId: String): List<Appointment>

    fun findByStatus(status: String): List<Appointment>

    fun findByStatusIn(statuses: List<String>): List<Appointment>

    // Remove problematic query with nullable parameters

    // New salon-specific queries

    // Simple queries without nullable parameters
    fun findBySalonId(salonId: String): List<Appointment>

    fun findByStaffId(staffId: String): List<Appointment>

    fun findBySalonIdAndStaffIdAndAppointmentDate(
        salonId: String,
        staffId: String,
        appointmentDate: LocalDate,
    ): List<Appointment>

    fun findByStaffIdAndAppointmentDate(
        staffId: String,
        appointmentDate: LocalDate,
    ): List<Appointment>

    fun findByAppointmentDateAndStatus(
        appointmentDate: LocalDate,
        status: String,
    ): List<Appointment>

    fun findByStaffIdAndAppointmentDateBetween(
        staffId: String,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<Appointment>

    /**
     * Find appointments by salon ID and date range
     */
    @Query(
        "SELECT a FROM Appointment a WHERE a.salonId = :salonId " +
            "AND a.appointmentDate BETWEEN :startDate AND :endDate",
    )
    fun findBySalonIdAndDateRange(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate,
    ): List<Appointment>

    // COMMENTED OUT: These queries cause PostgreSQL parameter type determination issues
    // Use programmatic filtering in JpaAppointmentRepository instead

    /*
    /**
     * Optimized query for salon appointments with filters - DISABLED due to PostgreSQL issues
     */
    @Query(
        "SELECT a FROM Appointment a WHERE a.salonId = :salonId " +
            "AND (:date IS NULL OR a.appointmentDate = :date) " +
            "AND (:startDate IS NULL OR a.appointmentDate >= :startDate) " +
            "AND (:endDate IS NULL OR a.appointmentDate <= :endDate) " +
            "AND (:status IS NULL OR a.status = :status) " +
            "AND (:clientId IS NULL OR a.clientId = :clientId) " +
            "AND (:staffId IS NULL OR a.staffId = :staffId) " +
            "ORDER BY a.appointmentDate, a.startTime",
    )
    fun findBySalonIdWithFiltersOptimized(
        @Param("salonId") salonId: String,
        @Param("date") date: LocalDate?,
        @Param("startDate") startDate: LocalDate?,
        @Param("endDate") endDate: LocalDate?,
        @Param("status") status: String?,
        @Param("clientId") clientId: String?,
        @Param("staffId") staffId: String?,
    ): List<Appointment>

    /**
     * Optimized query for staff appointments with filters - DISABLED due to PostgreSQL issues
     */
    @Query(
        "SELECT a FROM Appointment a WHERE a.staffId = :staffId " +
            "AND (:startDate IS NULL OR a.appointmentDate >= :startDate) " +
            "AND (:endDate IS NULL OR a.appointmentDate <= :endDate) " +
            "AND (:status IS NULL OR a.status = :status) " +
            "ORDER BY a.appointmentDate, a.startTime",
    )
    fun findByStaffIdWithFiltersOptimized(
        @Param("staffId") staffId: String,
        @Param("startDate") startDate: LocalDate?,
        @Param("endDate") endDate: LocalDate?,
        @Param("status") status: String?,
    ): List<Appointment>
    */

    /**
     * Optimized conflict detection query - finds overlapping appointments
     */
    @Query(
        "SELECT a FROM Appointment a WHERE a.staffId = :staffId " +
            "AND a.appointmentDate = :date " +
            "AND a.status != 'CANCELLED' " +
            "AND ((a.startTime < :endTime AND a.endTime > :startTime)) " +
            "ORDER BY a.startTime",
    )
    fun findConflictingAppointments(
        @Param("staffId") staffId: String,
        @Param("date") date: LocalDate,
        @Param("startTime") startTime: LocalTime,
        @Param("endTime") endTime: LocalTime,
    ): List<Appointment>

    /**
     * Optimized query for appointment suggestions - finds available slots
     */
    @Query(
        "SELECT a FROM Appointment a WHERE a.salonId = :salonId " +
            "AND a.appointmentDate BETWEEN :startDate AND :endDate " +
            "AND a.staffId IN :staffIds " +
            "AND a.status != 'CANCELLED' " +
            "ORDER BY a.appointmentDate, a.startTime",
    )
    fun findAppointmentsForSuggestions(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate,
        @Param("staffIds") staffIds: List<String>,
    ): List<Appointment>
}
