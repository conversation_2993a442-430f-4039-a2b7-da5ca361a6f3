package ro.animaliaprogramari.animalia.adapter.inbound.rest

import org.slf4j.Logger
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.AddPetRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CreatePetRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.PetResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.PetDtoMapper
import ro.animaliaprogramari.animalia.application.command.AddPetCommand
import ro.animaliaprogramari.animalia.application.port.inbound.PetManagementUseCase
import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.validation.ValidationService

class AddPetHandler(
    private val logger: Logger,
    private val petManagementUseCase: PetManagementUseCase,
    private val validationService: ValidationService,
    private val petDtoMapper: PetDtoMapper,
) {
    fun addPet(
        request: AddPetRequest,
        clientId: String,
    ): ResponseEntity<ApiResponse<PetResponse>> {
        val createPetRequest =
            CreatePetRequest(
                name = request.name,
                breed = request.breed,
                age = request.getCalculatedAge(),
                weight = request.getWeightAsBigDecimal(),
                color = request.color,
                notes = request.notes,
                medicalConditions = request.medicalConditions,
                photoUrl = request.photoUrl,
            )
        validationService.validateCreatePet(createPetRequest).throwIfInvalid()

        val command =
            AddPetCommand(
                clientId = ClientId.of(clientId),
                name = request.name,
                breed = request.breed,
                age = request.getCalculatedAge(),
                weight = request.getWeightAsBigDecimal(),
                color = request.color?.takeIf { it.isNotBlank() },
                gender = petDtoMapper.toGender(request.gender),
                notes = request.notes?.takeIf { it.isNotBlank() },
                medicalConditions = request.medicalConditions?.takeIf { it.isNotBlank() },
                photoUrl = request.photoUrl?.takeIf { it.isNotBlank() },
            )

        val pet = petManagementUseCase.addPet(command)
        val response = petDtoMapper.toResponse(pet)

        logger.info("Pet added successfully: ${pet.id.value}")
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(response))
    }
}
