package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.*
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Request DTO for scheduling an appointment in a salon
 */
@Schema(description = "Request payload for scheduling an appointment")
data class ScheduleAppointmentRequest(
    @field:NotBlank(message = "Client ID is required")
    val clientId: String,
    // Pet ID is optional - if not provided, a new pet will be created
    val petId: String? = null,
    @field:NotBlank(message = "Staff ID is required")
    val staffId: String,
    @field:NotNull(message = "Appointment date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val appointmentDate: LocalDate,
    @field:NotNull(message = "Start time is required")
    @JsonFormat(pattern = "HH:mm:ss")
    val startTime: LocalTime,
    @field:NotNull(message = "End time is required")
    @JsonFormat(pattern = "HH:mm:ss")
    val endTime: LocalTime,
    @field:NotEmpty(message = "At least one service is required")
    val serviceIds: List<String>,
    val notes: String? = null,
    @field:Pattern(regexp = "daily|weekly|biweekly|monthly", message = "Invalid repetition frequency")
    val repetitionFrequency: String? = null,
    val clientName: String?,
    val clientPhone: PhoneNumber?,
    val petName: String?,
    val petSpecies: String?,
    val petBreed: String?,
    @field:Pattern(regexp = "S|M|L|XL", message = "Invalid pet size")
    val petSize: String?,
)

/**
 * Response DTO for appointment information with salon context
 */
@Schema(description = "Response details for a scheduled appointment")
data class AppointmentResponse(
    val id: String,
    val salonId: String,
    val client: ClientSummary,
    val pet: PetSummary,
    val staff: StaffSummary,
    @JsonFormat(pattern = "yyyy-MM-dd")
    val appointmentDate: LocalDate,
    @JsonFormat(pattern = "HH:mm:ss")
    val startTime: LocalTime,
    @JsonFormat(pattern = "HH:mm:ss")
    val endTime: LocalTime,
    val services: List<ServiceSummary>,
    val status: String,
    val notes: String?,
    val repetitionFrequency: String?,
    val totalPrice: BigDecimal,
    val totalDuration: Int, // in minutes
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val createdAt: LocalDateTime,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val updatedAt: LocalDateTime,
)

/**
 * Summary DTO for client information
 */
data class ClientSummary(
    val id: String,
    val name: String,
    val phone: String?,
    val email: String?,
)

/**
 * Summary DTO for pet information
 */
data class PetSummary(
    val id: String,
    val name: String,
    val breed: String?,
    val age: Int?,
    val weight: Double?,
)

/**
 * Summary DTO for staff information
 */
data class StaffSummary(
    val id: String,
    val name: String,
    val role: String,
    val specialties: List<String> = emptyList(),
)

/**
 * Summary DTO for service information
 */
data class ServiceSummary(
    val id: String,
    val name: String,
    val price: BigDecimal,
    val duration: Int, // in minutes
)

/**
 * Request DTO for updating an appointment
 */
data class UpdateAppointmentRequest(
    val clientId: String? = null,
    val petId: String? = null,
    val staffId: String? = null,
    @JsonFormat(pattern = "yyyy-MM-dd")
    val appointmentDate: LocalDate? = null,
    @JsonFormat(pattern = "HH:mm:ss")
    val startTime: LocalTime? = null,
    @JsonFormat(pattern = "HH:mm:ss")
    val endTime: LocalTime? = null,
    val serviceIds: List<String>? = null,
    val notes: String? = null,
    val repetitionFrequency: String? = null,
)

/**
 * Request DTO for cancelling an appointment
 */
data class CancelAppointmentRequest(
    val reason: String? = null,
)

/**
 * Request DTO for completing an appointment
 */
data class CompleteAppointmentRequest(
    val notes: String? = null,
    val actualServiceIds: List<String>? = null,
)

/**
 * Request DTO for rescheduling an appointment
 */
data class RescheduleAppointmentRequest(
    @field:NotNull(message = "Start time is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val startTime: LocalDateTime,
    @field:NotNull(message = "End time is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val endTime: LocalDateTime,
    val staffId: String? = null,
    val reason: String? = null,
)

/**
 * Response DTO for availability check
 */
data class AvailabilityResponse(
    val isAvailable: Boolean,
    val reason: String? = null,
    val conflictingAppointments: List<String> = emptyList(),
    val suggestedTimes: List<String> = emptyList(),
)

/**
 * Response DTO for available time slots
 */
data class TimeSlotResponse(
    @JsonFormat(pattern = "HH:mm:ss")
    val startTime: LocalTime,
    @JsonFormat(pattern = "HH:mm:ss")
    val endTime: LocalTime,
    val isAvailable: Boolean,
    val staffId: String? = null,
)

/**
 * Response DTO for appointment summary/statistics
 */
data class AppointmentSummaryResponse(
    val totalAppointments: Int,
    val completedAppointments: Int,
    val cancelledAppointments: Int,
    val noShowAppointments: Int,
    val totalRevenue: BigDecimal,
    val averageAppointmentDuration: Int, // in minutes
    val busyDays: List<LocalDate>,
    val topServices: List<ServiceUsageResponse>,
    val staffUtilization: Map<String, Double>, // staff ID and utilization percentage
)

/**
 * Response DTO for service usage statistics
 */
data class ServiceUsageResponse(
    val serviceId: String,
    val serviceName: String,
    val count: Int,
    val revenue: BigDecimal,
)
