package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.notification.SmsQuota as DomainSmsQuota
import ro.animaliaprogramari.animalia.domain.model.notification.SmsQuotaId
import ro.animaliaprogramari.animalia.domain.model.notification.QuotaPeriod as DomainQuotaPeriod
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsQuota as EntitySmsQuota
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.QuotaPeriod as EntityQuotaPeriod

/**
 * Mapper between SMS quota domain model and JPA entity
 */
@Component
class SmsQuotaEntityMapper {

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: DomainSmsQuota): EntitySmsQuota {
        return EntitySmsQuota(
            id = domain.id.value,
            salonId = domain.salonId.value,
            totalQuota = domain.totalQuota,
            usedQuota = domain.usedQuota,
            resetDate = domain.resetDate,
            quotaPeriod = toEntityQuotaPeriod(domain.quotaPeriod),
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: EntitySmsQuota): DomainSmsQuota {
        return DomainSmsQuota(
            id = SmsQuotaId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            totalQuota = entity.totalQuota,
            usedQuota = entity.usedQuota,
            resetDate = entity.resetDate,
            quotaPeriod = toDomainQuotaPeriod(entity.quotaPeriod),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }

    /**
     * Convert domain quota period to entity quota period
     */
    private fun toEntityQuotaPeriod(domain: DomainQuotaPeriod): EntityQuotaPeriod {
        return when (domain) {
            DomainQuotaPeriod.DAILY -> EntityQuotaPeriod.DAILY
            DomainQuotaPeriod.WEEKLY -> EntityQuotaPeriod.WEEKLY
            DomainQuotaPeriod.MONTHLY -> EntityQuotaPeriod.MONTHLY
        }
    }

    /**
     * Convert entity quota period to domain quota period
     */
    private fun toDomainQuotaPeriod(entity: EntityQuotaPeriod): DomainQuotaPeriod {
        return when (entity) {
            EntityQuotaPeriod.DAILY -> DomainQuotaPeriod.DAILY
            EntityQuotaPeriod.WEEKLY -> DomainQuotaPeriod.WEEKLY
            EntityQuotaPeriod.MONTHLY -> DomainQuotaPeriod.MONTHLY
        }
    }
}
