package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import ro.animaliaprogramari.animalia.domain.model.SmsProvider
import java.time.LocalDateTime

@Entity
@Table(name = "sms_reminder_settings")
data class SmsReminderSettings(
    @Id
    @Column(name = "salon_id", nullable = false)
    val salonId: String,
    @Column(name = "enabled", nullable = false)
    val enabled: Boolean = true,
    @Column(name = "appointment_confirmations", nullable = false)
    val appointmentConfirmations: Boolean = true,
    @Column(name = "day_before_reminders", nullable = false)
    val dayBeforeReminders: Boolean = true,
    @Column(name = "follow_up_messages", nullable = false)
    val followUpMessages: Boolean = false,
    @Enumerated(EnumType.STRING)
    @Column(name = "selected_provider", nullable = true)
    val selectedProvider: SmsProvider = SmsProvider.ANIMALIA,
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        "",
        true,
        true,
        true,
        false,
        SmsProvider.ANIMALIA,
        LocalDateTime.now(),
        LocalDateTime.now(),
    )
}
