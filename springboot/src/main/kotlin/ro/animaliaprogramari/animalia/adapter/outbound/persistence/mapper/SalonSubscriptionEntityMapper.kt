package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonSubscriptionEntity
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Mapper between SalonSubscription domain model and SalonSubscriptionEntity
 */
@Component
class SalonSubscriptionEntityMapper {

    /**
     * Convert domain model to entity
     */
    fun toEntity(subscription: SalonSubscription): SalonSubscriptionEntity {
        return SalonSubscriptionEntity(
            id = subscription.id.value,
            salonId = subscription.salonId.value,
            tier = subscription.tier,
            status = subscription.status,
            startDate = subscription.startDate,
            endDate = subscription.endDate,
            isActive = subscription.isActive,
            autoRenew = subscription.autoRenew,
            monthlyPrice = subscription.monthlyPrice,
            billingCycle = subscription.billingCycle,
            createdAt = subscription.createdAt,
            updatedAt = subscription.updatedAt,
            lastBillingDate = subscription.lastBillingDate,
            nextBillingDate = subscription.nextBillingDate,
            trialEndDate = subscription.trialEndDate,
            cancelledAt = subscription.cancelledAt,
            cancellationReason = subscription.cancellationReason
        )
    }

    /**
     * Convert entity to domain model
     */
    fun toDomain(entity: SalonSubscriptionEntity): SalonSubscription {
        return SalonSubscription(
            id = SalonSubscriptionId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            tier = entity.tier,
            status = entity.status,
            startDate = entity.startDate,
            endDate = entity.endDate,
            isActive = entity.isActive,
            autoRenew = entity.autoRenew,
            monthlyPrice = entity.monthlyPrice,
            billingCycle = entity.billingCycle,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            lastBillingDate = entity.lastBillingDate,
            nextBillingDate = entity.nextBillingDate,
            trialEndDate = entity.trialEndDate,
            cancelledAt = entity.cancelledAt,
            cancellationReason = entity.cancellationReason
        )
    }

    /**
     * Convert list of entities to domain models
     */
    fun toDomainList(entities: List<SalonSubscriptionEntity>): List<SalonSubscription> {
        return entities.map { toDomain(it) }
    }

    /**
     * Convert list of domain models to entities
     */
    fun toEntityList(subscriptions: List<SalonSubscription>): List<SalonSubscriptionEntity> {
        return subscriptions.map { toEntity(it) }
    }
}
