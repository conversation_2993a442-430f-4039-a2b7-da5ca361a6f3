package ro.animaliaprogramari.animalia.adapter.inbound.web

import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.search.Search
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * REST controller for monitoring and metrics endpoints
 * Provides workflow and method processing time information
 */
@RestController
@RequestMapping("/api/monitoring")
class MonitoringController(
    private val meterRegistry: MeterRegistry
) {

    /**
     * Get workflow processing times and success rates
     */
    @GetMapping("/workflows")
    fun getWorkflowMetrics(): ResponseEntity<WorkflowMetricsResponse> {
        val workflowMetrics = mutableMapOf<String, WorkflowMetric>()
        
        // Get workflow execution timers
        Search.`in`(meterRegistry)
            .name("workflow.execution")
            .timers()
            .forEach { timer ->
                val workflowName = timer.id.getTag("workflow") ?: "unknown"
                val success = timer.id.getTag("success") ?: "unknown"
                
                val existing = workflowMetrics[workflowName] ?: WorkflowMetric(
                    name = workflowName,
                    totalExecutions = 0,
                    successfulExecutions = 0,
                    failedExecutions = 0,
                    averageProcessingTimeMs = 0.0,
                    maxProcessingTimeMs = 0.0,
                    minProcessingTimeMs = Double.MAX_VALUE
                )
                
                val count = timer.count().toInt()
                val meanTime = timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS)
                val maxTime = timer.max(java.util.concurrent.TimeUnit.MILLISECONDS)
                
                workflowMetrics[workflowName] = existing.copy(
                    totalExecutions = existing.totalExecutions + count,
                    successfulExecutions = if (success == "true") existing.successfulExecutions + count else existing.successfulExecutions,
                    failedExecutions = if (success == "false") existing.failedExecutions + count else existing.failedExecutions,
                    averageProcessingTimeMs = meanTime,
                    maxProcessingTimeMs = maxOf(existing.maxProcessingTimeMs, maxTime),
                    minProcessingTimeMs = if (meanTime > 0) minOf(existing.minProcessingTimeMs, meanTime) else existing.minProcessingTimeMs
                )
            }
        
        return ResponseEntity.ok(WorkflowMetricsResponse(
            timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            workflows = workflowMetrics.values.toList()
        ))
    }

    /**
     * Get method processing times and success rates
     */
    @GetMapping("/methods")
    fun getMethodMetrics(): ResponseEntity<MethodMetricsResponse> {
        val methodMetrics = mutableMapOf<String, MethodMetric>()
        
        // Get method execution timers
        Search.`in`(meterRegistry)
            .name("method.execution")
            .timers()
            .forEach { timer ->
                val className = timer.id.getTag("class") ?: "unknown"
                val methodName = timer.id.getTag("method") ?: "unknown"
                val success = timer.id.getTag("success") ?: "unknown"
                val key = "$className.$methodName"
                
                val existing = methodMetrics[key] ?: MethodMetric(
                    className = className,
                    methodName = methodName,
                    totalExecutions = 0,
                    successfulExecutions = 0,
                    failedExecutions = 0,
                    averageProcessingTimeMs = 0.0,
                    maxProcessingTimeMs = 0.0,
                    minProcessingTimeMs = Double.MAX_VALUE
                )
                
                val count = timer.count().toInt()
                val meanTime = timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS)
                val maxTime = timer.max(java.util.concurrent.TimeUnit.MILLISECONDS)
                
                methodMetrics[key] = existing.copy(
                    totalExecutions = existing.totalExecutions + count,
                    successfulExecutions = if (success == "true") existing.successfulExecutions + count else existing.successfulExecutions,
                    failedExecutions = if (success == "false") existing.failedExecutions + count else existing.failedExecutions,
                    averageProcessingTimeMs = meanTime,
                    maxProcessingTimeMs = maxOf(existing.maxProcessingTimeMs, maxTime),
                    minProcessingTimeMs = if (meanTime > 0) minOf(existing.minProcessingTimeMs, meanTime) else existing.minProcessingTimeMs
                )
            }
        
        return ResponseEntity.ok(MethodMetricsResponse(
            timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            methods = methodMetrics.values.toList()
        ))
    }

    /**
     * Get error statistics
     */
    @GetMapping("/errors")
    fun getErrorMetrics(): ResponseEntity<ErrorMetricsResponse> {
        val errorMetrics = mutableMapOf<String, ErrorMetric>()
        
        // Get error counters
        Search.`in`(meterRegistry)
            .name { it.contains("execution") }
            .counters()
            .filter { it.id.getTag("success") == "false" }
            .forEach { counter ->
                val errorType = counter.id.getTag("error_type") ?: "unknown"
                val operation = counter.id.getTag("workflow") ?: counter.id.getTag("method") ?: "unknown"
                
                val existing = errorMetrics[errorType] ?: ErrorMetric(
                    errorType = errorType,
                    totalOccurrences = 0,
                    affectedOperations = setOf()
                )

                errorMetrics[errorType] = existing.copy(
                    totalOccurrences = existing.totalOccurrences + counter.count().toInt(),
                    affectedOperations = existing.affectedOperations + operation
                )
            }
        
        return ResponseEntity.ok(ErrorMetricsResponse(
            timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            errors = errorMetrics.values.toList()
        ))
    }

    /**
     * Get appointment-specific workflow metrics
     */
    @GetMapping("/appointments")
    fun getAppointmentMetrics(): ResponseEntity<AppointmentMetricsResponse> {
        val appointmentMetrics = mutableMapOf<String, AppointmentOperationMetric>()
        
        // Get appointment operation timers
        Search.`in`(meterRegistry)
            .name { it.startsWith("appointment.") }
            .timers()
            .forEach { timer ->
                val operation = timer.id.getTag("operation") ?: "unknown"
                
                val existing = appointmentMetrics[operation] ?: AppointmentOperationMetric(
                    operation = operation,
                    totalExecutions = 0,
                    averageProcessingTimeMs = 0.0,
                    maxProcessingTimeMs = 0.0,
                    conflictsDetected = 0,
                    alternativesGenerated = 0
                )
                
                val count = timer.count().toInt()
                val meanTime = timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS)
                val maxTime = timer.max(java.util.concurrent.TimeUnit.MILLISECONDS)
                
                appointmentMetrics[operation] = existing.copy(
                    totalExecutions = existing.totalExecutions + count,
                    averageProcessingTimeMs = meanTime,
                    maxProcessingTimeMs = maxOf(existing.maxProcessingTimeMs, maxTime)
                )
            }
        
        // Get conflict and alternative metrics
        Search.`in`(meterRegistry)
            .name { it.contains("conflicts_detected") || it.contains("alternatives_generated") }
            .gauges()
            .forEach { gauge ->
                val operation = gauge.id.getTag("operation") ?: "unknown"
                val existing = appointmentMetrics[operation]
                
                if (existing != null) {
                    when {
                        gauge.id.name.contains("conflicts") -> {
                            appointmentMetrics[operation] = existing.copy(
                                conflictsDetected = existing.conflictsDetected + gauge.value().toInt()
                            )
                        }
                        gauge.id.name.contains("alternatives") -> {
                            appointmentMetrics[operation] = existing.copy(
                                alternativesGenerated = existing.alternativesGenerated + gauge.value().toInt()
                            )
                        }
                    }
                }
            }
        
        return ResponseEntity.ok(AppointmentMetricsResponse(
            timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            operations = appointmentMetrics.values.toList()
        ))
    }

    /**
     * Get overall system health metrics
     */
    @GetMapping("/health")
    fun getHealthMetrics(): ResponseEntity<HealthMetricsResponse> {
        val totalOperations = Search.`in`(meterRegistry)
            .name { it.contains("execution") }
            .counters()
            .sumOf { it.count() }.toInt()
        
        val successfulOperations = Search.`in`(meterRegistry)
            .name { it.contains("execution") }
            .counters()
            .filter { it.id.getTag("success") == "true" }
            .sumOf { it.count() }.toInt()
        
        val failedOperations = totalOperations - successfulOperations
        val successRate = if (totalOperations > 0) (successfulOperations.toDouble() / totalOperations) * 100 else 0.0
        
        val averageResponseTime = Search.`in`(meterRegistry)
            .name { it.contains("execution") }
            .timers()
            .map { it.mean(java.util.concurrent.TimeUnit.MILLISECONDS) }
            .let { times -> if (times.isNotEmpty()) times.average() else 0.0 }
        
        return ResponseEntity.ok(HealthMetricsResponse(
            timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            totalOperations = totalOperations,
            successfulOperations = successfulOperations,
            failedOperations = failedOperations,
            successRate = successRate,
            averageResponseTimeMs = averageResponseTime
        ))
    }
}

// Data classes for responses
data class WorkflowMetricsResponse(
    val timestamp: String,
    val workflows: List<WorkflowMetric>
)

data class WorkflowMetric(
    val name: String,
    val totalExecutions: Int,
    val successfulExecutions: Int,
    val failedExecutions: Int,
    val averageProcessingTimeMs: Double,
    val maxProcessingTimeMs: Double,
    val minProcessingTimeMs: Double
) {
    val successRate: Double = if (totalExecutions > 0) (successfulExecutions.toDouble() / totalExecutions) * 100 else 0.0
}

data class MethodMetricsResponse(
    val timestamp: String,
    val methods: List<MethodMetric>
)

data class MethodMetric(
    val className: String,
    val methodName: String,
    val totalExecutions: Int,
    val successfulExecutions: Int,
    val failedExecutions: Int,
    val averageProcessingTimeMs: Double,
    val maxProcessingTimeMs: Double,
    val minProcessingTimeMs: Double
) {
    val successRate: Double = if (totalExecutions > 0) (successfulExecutions.toDouble() / totalExecutions) * 100 else 0.0
}

data class ErrorMetricsResponse(
    val timestamp: String,
    val errors: List<ErrorMetric>
)

data class ErrorMetric(
    val errorType: String,
    val totalOccurrences: Int,
    val affectedOperations: Set<String>
)

data class AppointmentMetricsResponse(
    val timestamp: String,
    val operations: List<AppointmentOperationMetric>
)

data class AppointmentOperationMetric(
    val operation: String,
    val totalExecutions: Int,
    val averageProcessingTimeMs: Double,
    val maxProcessingTimeMs: Double,
    val conflictsDetected: Int,
    val alternativesGenerated: Int
)

data class HealthMetricsResponse(
    val timestamp: String,
    val totalOperations: Int,
    val successfulOperations: Int,
    val failedOperations: Int,
    val successRate: Double,
    val averageResponseTimeMs: Double
)
