package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

@Entity
@Table(
    name = "phone_verification_codes",
    indexes = [
        Index(name = "idx_phone_verification_phone", columnList = "phone_number"),
        Index(name = "idx_phone_verification_expires", columnList = "expires_at"),
        Index(name = "idx_phone_verification_phone_active", columnList = "phone_number, is_used, expires_at")
    ]
)
data class PhoneVerificationCode(
    @Id
    val id: String,
    @field:NotBlank(message = "Phone number is required")
    @field:Size(max = 20, message = "Phone number must not exceed 20 characters")
    @Column(name = "phone_number", nullable = false)
    val phoneNumber: String,
    @field:NotBlank(message = "Verification code is required")
    @field:Size(min = 6, max = 6, message = "Verification code must be exactly 6 digits")
    @Column(name = "code", nullable = false, length = 6)
    val code: String,
    @Column(name = "expires_at", nullable = false)
    val expiresAt: LocalDateTime,
    @Column(name = "is_used", nullable = false)
    val isUsed: Boolean = false,
    @Column(name = "used_at")
    val usedAt: LocalDateTime? = null,
    @Column(name = "attempts", nullable = false)
    val attempts: Int = 0,
    @Column(name = "max_attempts", nullable = false)
    val maxAttempts: Int = 3,
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        phoneNumber = "",
        code = "",
        expiresAt = LocalDateTime.now(),
        isUsed = false,
        usedAt = null,
        attempts = 0,
        maxAttempts = 3,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as PhoneVerificationCode
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}
