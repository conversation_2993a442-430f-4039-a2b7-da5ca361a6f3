package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import ro.animaliaprogramari.animalia.domain.model.SalonStaffInfo
import ro.animaliaprogramari.animalia.domain.model.Staff
import java.time.LocalDateTime

/**
 * Response DTO for salon information
 */
@Schema(description = "Salon details")
data class SalonResponse(
    val id: String,
    val name: String,
    val address: String?,
    val city: String?,
    val phone: String?,
    val email: String?,
    val ownerId: String,
    val isActive: Boolean,
    val clientCount: Int,
    val description: String?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

/**
 * Request DTO for creating a salon
 */
@Schema(description = "Request to create a salon")
data class CreateSalonRequest(
    @field:NotBlank(message = "Salon name is required")
    val name: String,
    val description: String? = null,
    val address: String?,
    val city: String? = null,
    val phone: String?,
    val email: String?,
)

/**
 * Response DTO for salon creation
 */
data class CreateSalonResponse(
    val salon: SalonResponse,
    val message: String,
)

/**
 * Request DTO for updating a salon
 */
data class UpdateSalonRequest(
    val name: String? = null,
    val address: String? = null,
    val city: String? = null,
    val phone: String? = null,
    val email: String? = null,
)

/**
 * Response for salon staff information
 */
data class SalonStaffResponse(val staff: Staff, val staffInfo: SalonStaffInfo) {
    val id: String = staff.id.value
    val userId: String = staff.userId.value
    val salonId: String = staff.salonId.value
    val userName: String = staffInfo.userName
    val userEmail: String? = staffInfo.userEmail
    val userPhone: String? = staffInfo.userPhone
    val nickname: String? = staff.nickname
    val role: String = staff.role.name
    val permissions: StaffPermissionsResponse =
        StaffPermissionsResponse(
            clientDataAccess = staff.permissions.clientDataAccess.name,
        )
    val specializations: List<String> = staff.specializations.map { it.name }
    val isActive: Boolean = staff.isActive
    val hiredAt: LocalDateTime = staff.hiredAt
    val createdAt: LocalDateTime = staff.createdAt
    val updatedAt: LocalDateTime = staff.updatedAt

    init {
        assert(staff.id.value.isNotBlank()) { "Staff ID cannot be blank" }
    }
}

/**
 * Response for staff permissions
 */
data class StaffPermissionsResponse(
    val clientDataAccess: String,
)

/**
 * Request to add staff to salon
 */
data class AddStaffToSalonRequest(
    @field:NotBlank(message = "Phone number is required")
    val phoneNumber: String = "",
    @field:NotBlank(message = "Groomer role is required")
    val groomerRole: String = "",
    @field:NotBlank(message = "Client data permission is required")
    val clientDataPermission: String = "",
    val proposedNickname: String? = null,
    val notes: String? = null,
)

/**
 * Request to update staff
 */
data class UpdateStaffRequest(
    @field:NotBlank(message = "Groomer role is required")
    val groomerRole: String = "",
    @field:NotBlank(message = "Client data permission is required")
    val clientDataPermission: String = "",
    val notes: String? = null,
    val nickname: String?,
)
