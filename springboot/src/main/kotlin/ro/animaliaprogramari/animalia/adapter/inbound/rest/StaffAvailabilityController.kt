package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.StaffWorkingHoursDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.StaffWorkingHoursManagementUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import java.time.LocalDate
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for staff availability operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/salons/{salonId}/staff/availability")
@CrossOrigin(origins = ["*"])
@Tag(name = "Staff Availability", description = "Operations for retrieving staff availability")
class StaffAvailabilityController(
    private val staffWorkingHoursManagementUseCase: StaffWorkingHoursManagementUseCase,
    private val staffWorkingHoursDtoMapper: StaffWorkingHoursDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(StaffAvailabilityController::class.java)

    /**
     * Get staff availability for a specific date and optional time range
     * Authorization: Any authenticated user with access to the salon
     */
    @GetMapping
    @Operation(summary = "Get staff availability", description = "Retrieve staff availability for a date")
    @SwaggerApiResponse(responseCode = "200", description = "Availability retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun getStaffAvailability(
        @PathVariable salonId: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
        @RequestParam(required = false) startTime: String?,
        @RequestParam(required = false) endTime: String?,
    ): ResponseEntity<ApiResponse<StaffAvailabilityResponse>> {
        logger.debug("Getting staff availability for salon: $salonId on date: $date")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Validate time parameters
            if ((startTime != null && endTime == null) || (startTime == null && endTime != null)) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Atât startTime cât și endTime trebuie specificate împreună sau omise"))
            }

            if (startTime != null && endTime != null) {
                try {
                    val start = java.time.LocalTime.parse(startTime)
                    val end = java.time.LocalTime.parse(endTime)

                    if (!start.isBefore(end)) {
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponse.error("startTime trebuie să fie înainte de endTime"))
                    }
                } catch (e: Exception) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Format invalid pentru timp. Folosiți formatul HH:MM"))
                }
            }

            val query =
                staffWorkingHoursDtoMapper.toGetAvailabilityQuery(
                    salonId,
                    date,
                    startTime,
                    endTime,
                    currentUser.userId,
                )
            val availabilityReport = staffWorkingHoursManagementUseCase.getStaffAvailability(query)
            val response = staffWorkingHoursDtoMapper.toAvailabilityResponse(availabilityReport)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Operațiunea nu este permisă"))
        } catch (e: EntityNotFoundException) {
            logger.warn("Entity not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Salonul nu a fost găsit"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting staff availability", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}
