package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.PetDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.PetManagementUseCase
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.validation.ValidationService
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for pet management
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/pets")
@CrossOrigin(origins = ["*"])
@Tag(name = "Pet Management", description = "Operations for managing pets")
class PetController(
    private val petManagementUseCase: PetManagementUseCase,
    private val petDtoMapper: PetDtoMapper,
    validationService: ValidationService,
) {
    private val logger = LoggerFactory.getLogger(PetController::class.java)
    private val petHandler =
        AddPetHandler(
            logger = logger,
            petManagementUseCase = petManagementUseCase,
            validationService = validationService,
            petDtoMapper = petDtoMapper,
        )

    /**
     * GET /pets
     * Get all pets with pagination
     */
    @GetMapping
    @Operation(summary = "List pets", description = "Retrieve all pets with optional filters")
    @SwaggerApiResponse(responseCode = "200", description = "Pets retrieved successfully")
    @SwaggerApiResponse(responseCode = "500", description = AnimaliaConstants.INTERNAL_SERVER_ERROR)
    fun getAllPets(
        @RequestParam(required = false) clientId: String?,
        @RequestParam(required = false) active: Boolean?,
        @RequestParam(required = false, defaultValue = "100") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<ApiResponse<List<PetResponse>>> {
        logger.debug("REST request to get all pets")

        return try {
            val query =
                GetAllPetsQuery(
                    clientId = clientId?.let { ClientId.of(it) },
                    isActive = active,
                    limit = limit,
                    offset = offset,
                )

            val pets = petManagementUseCase.getAllPets(query)
            val responses = pets.map { petDtoMapper.toResponse(it) }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting all pets", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /pets/{id}
     * Get pet by ID
     */
    @GetMapping("/{id}")
    fun getPetById(
        @PathVariable id: String,
    ): ResponseEntity<ApiResponse<PetResponse>> {
        logger.debug("REST request to get pet by ID: $id")

        return try {
            val query = GetPetByIdQuery(PetId.of(id))
            val pet = petManagementUseCase.getPetById(query)

            if (pet != null) {
                val response = petDtoMapper.toResponse(pet)
                ResponseEntity.ok(ApiResponse.success(response))
            } else {
                ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Pet not found"))
            }
        } catch (e: Exception) {
            logger.error("Error getting pet by ID", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /pets/search
     * Search pets
     */
    @GetMapping("/search")
    fun searchPets(
        @RequestParam searchTerm: String,
        @RequestParam(required = false) clientId: String?,
        @RequestParam(required = false) active: Boolean?,
        @RequestParam(required = false, defaultValue = "100") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<ApiResponse<List<PetResponse>>> {
        logger.debug("REST request to search pets with term: $searchTerm")

        return try {
            val query =
                SearchPetsQuery(
                    searchTerm = searchTerm,
                    clientId = clientId?.let { ClientId.of(it) },
                    isActive = active,
                    limit = limit,
                    offset = offset,
                )

            val pets = petManagementUseCase.searchPets(query)
            val responses = pets.map { petDtoMapper.toResponse(it) }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error searching pets", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /pets/client/{clientId}
     * Get pets by client
     */
    @GetMapping("/client/{clientId}")
    fun getPetsByClient(
        @PathVariable clientId: String,
        @RequestParam(required = false, defaultValue = "false") activeOnly: Boolean,
    ): ResponseEntity<ApiResponse<List<PetResponse>>> {
        logger.debug("REST request to get pets by client ID: $clientId")

        return try {
            val query =
                GetPetsByClientQuery(
                    clientId = ClientId.of(clientId),
                    activeOnly = activeOnly,
                )

            val pets = petManagementUseCase.getPetsByClient(query)
            val responses = pets.map { petDtoMapper.toResponse(it) }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting pets by client", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * POST /pets
     * Add a new pet
     */
    @PostMapping
    @Deprecated("Use /salons/{salonId}/clients/{clientId}/pets instead")
    fun addPet(
        @Valid @RequestBody request: AddPetRequest,
    ): ResponseEntity<ApiResponse<PetResponse>> {
        logger.debug("REST request to add pet: {}", request)

        // Get client ID from request (supports both old and new format)
        val clientId =
            request.getEffectiveClientId()
                ?: return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Client ID is required"))

        // Validate pet data using ValidationService
        return petHandler.addPet(request, clientId)
    }

    /**
     * PUT /pets/{id}
     * Update pet information
     */
    @PutMapping("/{id}")
    fun updatePet(
        @PathVariable id: String,
        @Valid @RequestBody request: UpdatePetRequest,
    ): ResponseEntity<ApiResponse<PetResponse>> {
        logger.debug("REST request to update pet: $id")

        return try {
            val command =
                UpdatePetCommand(
                    petId = PetId.of(id),
                    name = request.name,
                    breed = request.breed,
                    age = request.age,
                    weight = request.weight,
                    color = request.color,
                    gender = petDtoMapper.toGender(request.gender),
                    notes = request.notes,
                    medicalConditions = request.medicalConditions,
                    photoUrl = request.photoUrl,
                )

            val pet = petManagementUseCase.updatePet(command)
            val response = petDtoMapper.toResponse(pet)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error updating pet", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to update pet"))
        } catch (e: Exception) {
            logger.error("Unexpected error updating pet", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * PUT /pets/{id}/activate
     * Activate a pet
     */
    @PutMapping("/{id}/activate")
    fun activatePet(
        @PathVariable id: String,
    ): ResponseEntity<ApiResponse<PetResponse>> {
        logger.debug("REST request to activate pet: $id")

        return try {
            val command = ActivatePetCommand(PetId.of(id))
            val pet = petManagementUseCase.activatePet(command)
            val response = petDtoMapper.toResponse(pet)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error activating pet", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to activate pet"))
        } catch (e: Exception) {
            logger.error("Unexpected error activating pet", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * PUT /pets/{id}/deactivate
     * Deactivate a pet
     */
    @PutMapping("/{id}/deactivate")
    fun deactivatePet(
        @PathVariable id: String,
    ): ResponseEntity<ApiResponse<PetResponse>> {
        logger.debug("REST request to deactivate pet: $id")

        return try {
            val command = DeactivatePetCommand(PetId.of(id))
            val pet = petManagementUseCase.deactivatePet(command)
            val response = petDtoMapper.toResponse(pet)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error deactivating pet", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to deactivate pet"))
        } catch (e: Exception) {
            logger.error("Unexpected error deactivating pet", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * DELETE /pets/{id}
     * Delete a pet
     */
    @DeleteMapping("/{id}")
    fun deletePet(
        @PathVariable id: String,
    ): ResponseEntity<ApiResponse<Nothing?>> {
        logger.debug("REST request to delete pet: $id")
        return try {
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // For now, we'll implement a simple deactivation
            // TODO: Implement proper delete command and authorization
            val command = DeactivatePetCommand(PetId.of(id))
            petManagementUseCase.deactivatePet(command)

            ResponseEntity.ok(ApiResponse.success(null))
        } catch (e: Exception) {
            logger.error("Error deleting pet", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}
