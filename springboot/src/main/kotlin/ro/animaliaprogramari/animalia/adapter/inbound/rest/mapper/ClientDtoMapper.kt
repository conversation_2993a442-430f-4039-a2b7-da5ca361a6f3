package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ClientResponse
import ro.animaliaprogramari.animalia.domain.model.Client
import ro.animaliaprogramari.animalia.domain.model.ClientDataAccess
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber

/**
 * Mapper for converting between domain models and DTOs for clients
 */
@Component
class ClientDtoMapper {
    /**
     * Convert domain model to response DTO
     */
    fun toResponse(
        client: Client,
        petCount: Int = 0,
    ): ClientResponse {
        return ClientResponse(
            id = client.id.value,
            name = client.name,
            phone = client.phone?.value,
            email = client.email?.value,
            address = client.address,
            notes = client.notes,
            isActive = client.isActive,
            createdAt = client.createdAt,
            updatedAt = client.updatedAt,
            petCount = petCount,
        )
    }

    /**
     * Convert domain model to response DTO with permission-based filtering
     */
    fun toResponse(
        client: Client,
        petCount: Int = 0,
        permission: ClientDataAccess,
    ): ClientResponse {
        return when (permission) {
            ClientDataAccess.NONE -> {
                // Should not happen - if no access, client shouldn't be returned
                throw IllegalStateException("Cannot create response for client with no access permission")
            }
            ClientDataAccess.LIMITED -> {
                ClientResponse(
                    id = client.id.value,
                    name = client.name,
                    phone = client.phone?.value,
                    email = null, // Hide email for limited access
                    address = null, // Hide address for limited access
                    notes = null, // Hide notes for limited access
                    isActive = client.isActive,
                    createdAt = client.createdAt,
                    updatedAt = client.updatedAt,
                    petCount = petCount,
                )
            }
            ClientDataAccess.FULL -> {
                ClientResponse(
                    id = client.id.value,
                    name = client.name,
                    phone = client.phone?.value,
                    email = client.email?.value,
                    address = client.address,
                    notes = client.notes,
                    isActive = client.isActive,
                    createdAt = client.createdAt,
                    updatedAt = client.updatedAt,
                    petCount = petCount,
                )
            }
        }
    }

    /**
     * Convert string to PhoneNumber value object
     */
    fun toPhoneNumber(phone: String?): PhoneNumber? {
        return phone?.let { PhoneNumber.of(it) }
    }

    /**
     * Convert string to Email value object
     */
    fun toEmail(email: String?): Email? {
        return email?.let { Email.ofNullable(it) }
    }
}
