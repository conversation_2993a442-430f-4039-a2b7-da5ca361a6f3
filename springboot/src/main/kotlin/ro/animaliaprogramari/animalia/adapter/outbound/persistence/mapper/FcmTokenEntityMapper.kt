package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.model.notification.FcmTokenId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.FcmToken as FcmTokenEntity
import ro.animaliaprogramari.animalia.domain.model.notification.FcmToken as FcmTokenDomain

/**
 * Mapper between FcmToken domain model and FcmToken JPA entity
 * This handles the translation between the pure domain model and the persistence layer
 */
@Component
class FcmTokenEntityMapper {
    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: FcmTokenDomain): FcmTokenEntity {
        return FcmTokenEntity(
            id = domain.id.value,
            userId = domain.userId.value,
            salonId = domain.salonId.value,
            fcmToken = domain.token,
            deviceId = domain.deviceId,
            deviceType = domain.deviceType,
            isActive = domain.isActive,
            lastUsed = domain.lastUsed,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: FcmTokenEntity): FcmTokenDomain {
        return FcmTokenDomain(
            id = FcmTokenId.of(entity.id),
            userId = UserId.of(entity.userId),
            salonId = SalonId.of(entity.salonId),
            token = entity.fcmToken,
            deviceId = entity.deviceId,
            deviceType = entity.deviceType,
            isActive = entity.isActive,
            lastUsed = entity.lastUsed,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }

    /**
     * Convert list of entities to domain models
     */
    fun toDomainList(entities: List<FcmTokenEntity>): List<FcmTokenDomain> {
        return entities.map { toDomain(it) }
    }

    /**
     * Convert list of domain models to entities
     */
    fun toEntityList(domains: List<FcmTokenDomain>): List<FcmTokenEntity> {
        return domains.map { toEntity(it) }
    }
}
