package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import ro.animaliaprogramari.animalia.domain.model.BillingCycle
import ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * JPA entity for salon subscription persistence
 */
@Entity
@Table(
    name = "salon_subscriptions",
    indexes = [
        Index(name = "idx_salon_subscription_salon_id", columnList = "salon_id"),
        Index(name = "idx_salon_subscription_status", columnList = "status"),
        Index(name = "idx_salon_subscription_tier", columnList = "tier"),
        Index(name = "idx_salon_subscription_active", columnList = "is_active"),
        Index(name = "idx_salon_subscription_next_billing", columnList = "next_billing_date")
    ]
)
data class SalonSubscriptionEntity(
    @Id
    @Column(name = "id", length = 36)
    val id: String,

    @Column(name = "salon_id", nullable = false, length = 36)
    val salonId: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "tier", nullable = false, length = 20)
    val tier: SubscriptionTier,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    val status: SubscriptionStatus,

    @Column(name = "start_date", nullable = false)
    val startDate: LocalDateTime,

    @Column(name = "end_date")
    val endDate: LocalDateTime?,

    @Column(name = "is_active", nullable = false)
    val isActive: Boolean = true,

    @Column(name = "auto_renew", nullable = false)
    val autoRenew: Boolean = true,

    @Column(name = "monthly_price", nullable = false, precision = 10, scale = 2)
    val monthlyPrice: BigDecimal,

    @Enumerated(EnumType.STRING)
    @Column(name = "billing_cycle", nullable = false, length = 20)
    val billingCycle: BillingCycle = BillingCycle.MONTHLY,

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "last_billing_date")
    val lastBillingDate: LocalDateTime?,

    @Column(name = "next_billing_date")
    val nextBillingDate: LocalDateTime?,

    @Column(name = "trial_end_date")
    val trialEndDate: LocalDateTime?,

    @Column(name = "cancelled_at")
    val cancelledAt: LocalDateTime?,

    @Column(name = "cancellation_reason", length = 500)
    val cancellationReason: String?
) {
    constructor() : this(
        id = "",
        salonId = "",
        tier = SubscriptionTier.FREELANCER,
        status = SubscriptionStatus.ACTIVE,
        startDate = LocalDateTime.now(),
        endDate = null,
        isActive = true,
        autoRenew = true,
        monthlyPrice = BigDecimal.ZERO,
        billingCycle = BillingCycle.MONTHLY,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        lastBillingDate = null,
        nextBillingDate = null,
        trialEndDate = null,
        cancelledAt = null,
        cancellationReason = null
    )

    /**
     * Check if subscription is currently active
     */
    fun isCurrentlyActive(): Boolean {
        val now = LocalDateTime.now()
        return isActive && 
               status == SubscriptionStatus.ACTIVE &&
               now.isAfter(startDate) &&
               (endDate == null || now.isBefore(endDate))
    }

    /**
     * Check if subscription is in trial period
     */
    fun isInTrial(): Boolean {
        val now = LocalDateTime.now()
        return trialEndDate?.let { now.isBefore(it) } ?: false
    }

    /**
     * Get SMS quota for this subscription tier
     */
    fun getSmsQuota(): Int = tier.maxSmsPerMonth

    /**
     * Check if this subscription allows team member invitations
     */
    fun allowsTeamInvitations(): Boolean = tier.canAddTeamMembers

    /**
     * Check if this subscription allows salon changes
     */
    fun allowsSalonChanges(): Boolean = tier.canChangeSalon
}
