package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.*
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Response DTO for staff working hours settings
 */
@Schema(description = "Staff working hours settings")
data class StaffWorkingHoursResponse(
    @Schema(description = "Staff ID")
    @JsonProperty("staffId")
    val staffId: String,
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    @Schema(description = "Weekly schedule")
    @JsonProperty("weeklySchedule")
    val weeklySchedule: Map<String, DayScheduleResponse>,
    @Schema(description = "List of holidays")
    @JsonProperty("holidays")
    val holidays: List<StaffHolidayResponse>,
    @Schema(description = "Custom closures")
    @JsonProperty("customClosures")
    val customClosures: List<StaffCustomClosureResponse>,
    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime,
)

/**
 * Response DTO for batch staff working hours settings
 */
@Schema(description = "Batch staff working hours settings")
data class BatchStaffWorkingHoursResponse(
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    @Schema(description = "List of staff working hours")
    @JsonProperty("staffWorkingHours")
    val staffWorkingHours: List<StaffWorkingHoursResponse>,
)

/**
 * Response DTO for staff holiday
 */
@Schema(description = "Staff holiday")
data class StaffHolidayResponse(
    @Schema(description = "Holiday name")
    @JsonProperty("name")
    val name: String,
    @Schema(description = "Holiday date")
    @JsonProperty("date")
    val date: LocalDate,
    @Schema(description = "Whether it's a working day")
    @JsonProperty("isWorkingDay")
    val isWorkingDay: Boolean,
    @Schema(description = "Holiday type")
    @JsonProperty("type")
    val type: String,
)

/**
 * Response DTO for staff custom closure
 */
@Schema(description = "Custom closure entry")
data class StaffCustomClosureResponse(
    @Schema(description = "Closure reason")
    @JsonProperty("reason")
    val reason: String,
    @Schema(description = "Closure date")
    @JsonProperty("date")
    val date: LocalDate,
    @Schema(description = "Additional description")
    @JsonProperty("description")
    val description: String?,
)

/**
 * Request DTO for updating staff working hours
 */
@Schema(description = "Request to update staff working hours")
data class UpdateStaffWorkingHoursRequest(
    @Schema(description = "Weekly schedule")
    @field:Valid
    @JsonProperty("weeklySchedule")
    val weeklySchedule: Map<String, DayScheduleRequest>,
    @Schema(description = "Holiday list")
    @field:Valid
    @JsonProperty("holidays")
    val holidays: List<StaffHolidayRequest> = emptyList(),
    @Schema(description = "Custom closures")
    @field:Valid
    @JsonProperty("customClosures")
    val customClosures: List<StaffCustomClosureRequest> = emptyList(),
)

/**
 * Request DTO for staff holiday
 */
@Schema(description = "Holiday request")
data class StaffHolidayRequest(
    @field:NotBlank(message = "Holiday name is required")
    @field:Size(max = 255, message = "Holiday name must not exceed 255 characters")
    @Schema(description = "Holiday name")
    @JsonProperty("name")
    val name: String,
    @field:NotNull(message = "Holiday date is required")
    @Schema(description = "Holiday date")
    @JsonProperty("date")
    val date: LocalDate,
    @Schema(description = "Whether it's a working day")
    @JsonProperty("isWorkingDay")
    val isWorkingDay: Boolean = false,
    @field:NotBlank(message = "Holiday type is required")
    @field:Pattern(
        regexp = "^(LEGAL|RELIGIOUS|NATIONAL)$",
        message = "Holiday type must be LEGAL, RELIGIOUS, or NATIONAL",
    )
    @Schema(description = "Holiday type")
    @JsonProperty("type")
    val type: String,
)

/**
 * Request DTO for staff custom closure
 */
@Schema(description = "Custom closure request")
data class StaffCustomClosureRequest(
    @field:NotBlank(message = "Closure reason is required")
    @field:Size(max = 255, message = "Closure reason must not exceed 255 characters")
    @Schema(description = "Closure reason")
    @JsonProperty("reason")
    val reason: String,
    @field:NotNull(message = "Closure date is required")
    @Schema(description = "Closure date")
    @JsonProperty("date")
    val date: LocalDate,
    @field:Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Schema(description = "Optional description")
    @JsonProperty("description")
    val description: String? = null,
)

/**
 * Response DTO for staff availability check
 */
@Schema(description = "Staff availability response")
data class StaffAvailabilityResponse(
    @Schema(description = "Date for availability")
    @JsonProperty("date")
    val date: LocalDate,
    @Schema(description = "List of staff availability data")
    @JsonProperty("staffAvailability")
    val staffAvailability: List<StaffMemberAvailabilityResponse>,
)

/**
 * Response DTO for individual staff member availability
 */
@Schema(description = "Individual staff availability details")
data class StaffMemberAvailabilityResponse(
    @Schema(description = "Staff ID")
    @JsonProperty("staffId")
    val staffId: String,
    @Schema(description = "Staff name")
    @JsonProperty("staffName")
    val staffName: String,
    @Schema(description = "Availability flag")
    @JsonProperty("isAvailable")
    val isAvailable: Boolean,
    @Schema(description = "Working hours")
    @JsonProperty("workingHours")
    val workingHours: DayScheduleResponse?,
    @Schema(description = "Conflicting appointments")
    @JsonProperty("conflictingAppointments")
    val conflictingAppointments: List<ConflictingAppointmentResponse>,
    @Schema(description = "Reason if not available")
    @JsonProperty("reason")
    val reason: String?,
)

/**
 * Response DTO for conflicting appointment
 */
@Schema(description = "Conflicting appointment")
data class ConflictingAppointmentResponse(
    @Schema(description = "Appointment ID")
    @JsonProperty("appointmentId")
    val appointmentId: String,
    @Schema(description = "Start time")
    @JsonProperty("startTime")
    val startTime: String,
    @Schema(description = "End time")
    @JsonProperty("endTime")
    val endTime: String,
    @Schema(description = "Client name")
    @JsonProperty("clientName")
    val clientName: String,
)
