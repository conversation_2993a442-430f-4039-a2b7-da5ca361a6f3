package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import jakarta.validation.Valid
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.AuthenticateWithFirebaseCommand
import ro.animaliaprogramari.animalia.application.command.ConfirmPhoneCommand
import ro.animaliaprogramari.animalia.application.command.DeleteUserAccountCommand
import ro.animaliaprogramari.animalia.application.command.RefreshTokenCommand
import ro.animaliaprogramari.animalia.application.command.ResendPhoneVerificationCommand
import ro.animaliaprogramari.animalia.application.command.UpdateUserCommand
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationResult
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.PhoneVerificationUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.UserManagementUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.FirebaseToken
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.validation.ValidationService
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for authentication operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/auth")
@CrossOrigin(origins = ["*"])
@Tag(name = "Authentication", description = "Operations for user authentication and profile management")
class AuthenticationController(
    private val authenticationUseCase: AuthenticationUseCase,
    private val userManagementUseCase: UserManagementUseCase,
    private val phoneVerificationUseCase: PhoneVerificationUseCase,
    private val validationService: ValidationService,
) {
    private val logger = LoggerFactory.getLogger(AuthenticationController::class.java)

    /**
     * POST /auth/firebase-login
     * Authenticate user with Firebase ID token
     */
    @Operation(summary = "Authenticate with Firebase")
    @SwaggerApiResponse(responseCode = "200", description = "Authentication successful")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request")
    @PostMapping("/firebase-login")
    fun authenticateWithFirebase(
        @RequestBody request: FirebaseAuthRequest,
    ): ResponseEntity<AuthenticationResponse> {
        return try {
            logger.info("=== FIREBASE LOGIN ATTEMPT ===")
            logger.info("Platform: ${request.platform}")
            logger.info("App Version: ${request.appVersion}")
            logger.info("Firebase Token Length: ${request.firebaseToken.length}")
            logger.info("Firebase Token Preview: ${request.firebaseToken.take(50)}...")

            val command =
                AuthenticateWithFirebaseCommand(
                    firebaseToken = FirebaseToken.of(request.firebaseToken),
                    platform = request.platform,
                    appVersion = request.appVersion,
                )

            logger.info("Command created successfully, calling authentication use case...")
            val result = authenticationUseCase.authenticateWithFirebase(command)

            logger.info("Authentication use case completed. Success: ${result.success}")
            logger.info("Result has user: ${result.user != null}")
            logger.info("Result has JWT token: ${result.jwtToken != null}")
            if (!result.success || result.user == null || result.jwtToken == null) {
                logger.error("Authentication failed with error: ${result.errorMessage}")
                logger.error("=== FIREBASE LOGIN FAILED ===")
                return createAuthenticationResponse(success = false, errorMessage = result.errorMessage)
            }

            logger.info("Authentication successful!")
            logger.info("User ID: ${result.user.userId.value}")
            logger.info("Firebase UID: ${result.user.firebaseUid}")
            logger.info("User Role: ${result.user.role.name}")
            logger.info("Staff Associations Count: ${result.user.staffAssociations.size}")
            // Get primary salon association for groomer users

            val authData = createAuthenticationData(result)
            logger.info("=== FIREBASE LOGIN SUCCESS ===")
            return createAuthenticationResponse(success = true, data = authData)
        } catch (e: Exception) {
            logger.error("=== FIREBASE LOGIN EXCEPTION ===")
            logger.error("Exception type: ${e.javaClass.simpleName}")
            logger.error("Exception message: ${e.message}")
            logger.error("Stack trace: ", e)

            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(
                    AuthenticationResponse(
                        success = false,
                        error = "Invalid request: ${e.message}",
                    ),
                )
        }
    }

    /**
     * POST /auth/refresh
     * Refresh JWT token
     */
    @Operation(summary = "Refresh JWT token")
    @SwaggerApiResponse(responseCode = "200", description = "Token refreshed")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request")
    @PostMapping("/refresh")
    fun refreshToken(
        @RequestBody request: RefreshTokenRequest,
    ): ResponseEntity<AuthenticationResponse> {
        return try {
            val command = RefreshTokenCommand(request.refreshToken)
            val result = authenticationUseCase.refreshToken(command)
            if (!result.success || result.user == null || result.jwtToken == null) {
                return createAuthenticationResponse(success = false, errorMessage = result.errorMessage)
            }

            // Get primary salon association for groomer users
            return createAuthenticationResponse(success = true, data = createAuthenticationData(result))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(
                    AuthenticationResponse(
                        success = false,
                        error = "Invalid request: ${e.message}",
                    ),
                )
        }
    }

    /**
     * GET /auth/profile
     * Get current user profile
     */
    @GetMapping("/profile")
    @Operation(summary = "Get current user profile")
    @SwaggerApiResponse(responseCode = "200", description = "Profile retrieved")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun getCurrentUserProfile(): ResponseEntity<ApiResponse<UserProfileDto>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()

            if (currentUser != null) {
                val userProfile =
                    UserProfileDto(
                        id = currentUser.userId.value,
                        firebaseUid = currentUser.firebaseUid,
                        email = currentUser.email?.value ?: currentUser.phoneNumber ?: "unknown",
                        name = currentUser.name,
                        role = currentUser.role.name,
                        isActive = currentUser.isActive,
                        createdAt = java.time.LocalDateTime.now().toString(), // In real implementation, get from user entity
                        updatedAt = java.time.LocalDateTime.now().toString(),
                    )

                ResponseEntity.ok(ApiResponse.success(userProfile))
            } else {
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))
            }
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get user profile: ${e.message}"))
        }
    }

    /**
     * PATCH /auth/profile/name
     * Update current user's name
     */
    @PatchMapping("/profile/name")
    @Operation(summary = "Update current user's name")
    @SwaggerApiResponse(responseCode = "200", description = "Name updated")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid data")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun updateCurrentUserName(
        @RequestBody request: UpdateUserNameRequest,
    ): ResponseEntity<ApiResponse<UserProfileDto>> {
        val currentUser =
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        // Validate request using ValidationService
        validationService.validateUpdateUserName(request).throwIfInvalid()

        val command =
            UpdateUserCommand(
                userId = currentUser.userId,
                name = request.name,
            )

        val updatedUser = userManagementUseCase.updateUser(command)

        val userProfile =
            UserProfileDto(
                id = updatedUser.id.value,
                firebaseUid = updatedUser.firebaseUid,
                email = updatedUser.email?.value ?: updatedUser.phoneNumber ?: "unknown",
                name = updatedUser.name,
                role = updatedUser.role.name,
                isActive = updatedUser.isActive,
                createdAt = updatedUser.createdAt.toString(),
                updatedAt = updatedUser.updatedAt.toString(),
            )

        return ResponseEntity.ok(ApiResponse.success(userProfile))
    }


    /**
     * POST /auth/logout
     * Logout user (client-side token removal)
     */
    @PostMapping("/logout")
    @Operation(summary = "Logout user")
    @SwaggerApiResponse(responseCode = "200", description = "Logout successful")
    fun logout(): ResponseEntity<ApiResponse<Map<String, Any>>> {
        return try {
            // In a stateless JWT implementation, logout is typically handled client-side
            // by removing the token from storage. Server-side logout would require
            // token blacklisting which is more complex.

            val result =
                mapOf(
                    "message" to "Logout successful",
                    "timestamp" to java.time.LocalDateTime.now().toString(),
                )

            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Logout failed: ${e.message}"))
        }
    }

    /**
     * DELETE /auth/delete-account
     * Delete user account permanently
     * Requires confirmation text "confirm" to proceed
     */
    @DeleteMapping("/delete-account")
    @Operation(summary = "Delete user account")
    @SwaggerApiResponse(responseCode = "200", description = "Account deleted")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun deleteAccount(
        @RequestBody request: DeleteAccountRequest,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("DELETE /auth/delete-account - Account deletion request received")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Validate confirmation text
            if (request.confirmationText != "confirm") {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(
                        ApiResponse.error(
                            "Confirmation text must be exactly 'confirm' to proceed with account deletion",
                        ),
                    )
            }

            val command =
                DeleteUserAccountCommand(
                    userId = currentUser.userId,
                    confirmationText = request.confirmationText,
                )

            val deleted = userManagementUseCase.deleteUserAccount(command)

            if (deleted) {
                val result =
                    mapOf(
                        "message" to "Account deleted successfully. All your data has been permanently removed.",
                        "timestamp" to java.time.LocalDateTime.now().toString(),
                        "userId" to currentUser.userId.value,
                    )
                ResponseEntity.ok(ApiResponse.success(result))
            } else {
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to delete account. Please try again or contact support."))
            }
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Account deletion blocked due to business rule: ${e.message}")
            ResponseEntity.status(HttpStatus.CONFLICT)
                .body(ApiResponse.error(e.message ?: "Cannot delete account due to business constraints"))
        } catch (e: EntityNotFoundException) {
            logger.error("User not found during account deletion: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("User account not found"))
        } catch (e: Exception) {
            logger.error("Unexpected error during account deletion", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Account deletion failed: ${e.message}"))
        }
    }

    private fun createAuthenticationData(result: AuthenticationResult): AuthenticationData {
        val primaryAssociation = result.user?.staffAssociations?.firstOrNull { it.isActive }

        return AuthenticationData(
            accessToken = result.jwtToken?.token ?: "",
            userId = result.user?.userId?.value ?: "",
            userPhone = result.user?.phoneNumber,
            userName = result.user?.name ?: "",
            userRole = result.user?.role?.name ?: "",
            salonId = primaryAssociation?.salonId?.value,
            groomerPermissions =
                primaryAssociation?.let {
                    GroomerPermissionsDto(
                        groomerRole = it.role.name,
                        clientDataPermission = it.permissions.clientDataAccess.name,
                    )
                },
            expiresIn = result.jwtToken?.getRemainingValiditySeconds() ?: 0,
            isActive = result.user?.isActive ?: false,
        )
    }

    private fun createAuthenticationResponse(
        success: Boolean,
        data: AuthenticationData? = null,
        errorMessage: String? = null,
    ): ResponseEntity<AuthenticationResponse> {
        return if (success) {
            ResponseEntity.ok(AuthenticationResponse(success = true, data = data))
        } else {
            ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(AuthenticationResponse(success = false, error = errorMessage ?: "Authentication failed"))
        }
    }
}
