package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.notification

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import ro.animaliaprogramari.animalia.domain.model.notification.*
import java.time.LocalDateTime

/**
 * Request to register FCM token
 */
@Schema(description = "Request to register FCM token")
data class RegisterFcmTokenRequest(
    @Schema(description = "Salon ID")
    @field:NotBlank(message = "Salon ID is required")
    val salonId: String,
    @Schema(description = "FCM token")
    @field:NotBlank(message = "FCM token is required")
    val token: String,
    @Schema(description = "Device identifier")
    val deviceId: String? = null,
    @Schema(description = "Device type")
    @field:NotBlank(message = "Device type is required")
    val deviceType: String = "MOBILE",
)



/**
 * Response for FCM token
 */
@Schema(description = "FCM token response")
data class FcmTokenResponse(
    @Schema(description = "Token ID")
    val id: String,
    @Schema(description = "User ID")
    val userId: String,
    @Schema(description = "Salon ID")
    val salonId: String,
    @Schema(description = "Device type")
    val deviceType: String,
    @Schema(description = "Whether token is active")
    val isActive: Boolean,
    @Schema(description = "Last used timestamp")
    val lastUsed: LocalDateTime,
    @Schema(description = "Creation timestamp")
    val createdAt: LocalDateTime,
) {
    companion object {
        fun from(fcmToken: FcmToken): FcmTokenResponse {
            return FcmTokenResponse(
                id = fcmToken.id.value,
                userId = fcmToken.userId.value,
                salonId = fcmToken.salonId.value,
                deviceType = fcmToken.deviceType.name,
                isActive = fcmToken.isActive,
                lastUsed = fcmToken.lastUsed,
                createdAt = fcmToken.createdAt,
            )
        }
    }
}

/**
 * Request to update notification settings
 */
data class UpdateNotificationSettingsRequest(
    val smsSettings: SmsSettingsRequest? = null,
    val pushSettings: PushSettingsRequest? = null,
)

/**
 * SMS settings request
 */
@Schema(description = "SMS notification settings")
data class SmsSettingsRequest(
    @Schema(description = "Whether SMS notifications are enabled")
    val enabled: Boolean,
    @Schema(description = "Send confirmation SMS")
    val appointmentConfirmation: Boolean,
    @Schema(description = "Send cancellation SMS")
    val appointmentCancellation: Boolean,
    @Schema(description = "Send completion SMS")
    val appointmentCompletion: Boolean,
    @Schema(description = "Send reminder a day before")
    val dayBeforeReminder: Boolean,
    @Schema(description = "Send reminder an hour before")
    val hourBeforeReminder: Boolean,
    @Schema(description = "Custom templates")
    val templates: SmsTemplatesRequest? = null,
) {
    fun toDomain(): SmsSettings {
        return SmsSettings(
            enabled = enabled,
            appointmentConfirmation = appointmentConfirmation,
            appointmentCancellation = appointmentCancellation,
            appointmentCompletion = appointmentCompletion,
            dayBeforeReminder = dayBeforeReminder,
            hourBeforeReminder = hourBeforeReminder,
            templates = templates?.toDomain() ?: SmsTemplates.defaultRomanian(),
        )
    }
}

/**
 * Push settings request
 */
@Schema(description = "Push notification settings")
data class PushSettingsRequest(
    @Schema(description = "Whether push notifications are enabled")
    val enabled: Boolean,
    @Schema(description = "Notify when appointment assigned")
    val appointmentAssigned: Boolean,
    @Schema(description = "Notify when appointment cancelled")
    val appointmentCancelled: Boolean,
    @Schema(description = "Notify when appointment completed")
    val appointmentCompleted: Boolean,
    @Schema(description = "Notify when new appointment created")
    val newAppointmentCreated: Boolean,
) {
    fun toDomain(): PushSettings {
        return PushSettings(
            enabled = enabled,
            appointmentAssigned = appointmentAssigned,
            appointmentCancelled = appointmentCancelled,
            appointmentCompleted = appointmentCompleted,
            newAppointmentCreated = newAppointmentCreated,
        )
    }
}

/**
 * SMS templates request
 */
@Schema(description = "SMS templates")
data class SmsTemplatesRequest(
    @Schema(description = "Template for appointment confirmation")
    @field:Size(max = 500, message = "Template must not exceed 500 characters")
    val appointmentConfirmation: String,
    @Schema(description = "Template for appointment cancellation")
    @field:Size(max = 500, message = "Template must not exceed 500 characters")
    val appointmentCancellation: String,
    @Schema(description = "Template for appointment completion")
    @field:Size(max = 500, message = "Template must not exceed 500 characters")
    val appointmentCompletion: String,
    @Schema(description = "Template for day-before reminder")
    @field:Size(max = 500, message = "Template must not exceed 500 characters")
    val dayBeforeReminder: String,
    @Schema(description = "Template for hour-before reminder")
    @field:Size(max = 500, message = "Template must not exceed 500 characters")
    val hourBeforeReminder: String,
) {
    fun toDomain(): SmsTemplates {
        return SmsTemplates(
            appointmentConfirmation = appointmentConfirmation,
            appointmentCancellation = appointmentCancellation,
            appointmentCompletion = appointmentCompletion,
            dayBeforeReminder = dayBeforeReminder,
            hourBeforeReminder = hourBeforeReminder,
        )
    }
}

/**
 * Response for notification settings
 */
@Schema(description = "Notification settings response")
data class NotificationSettingsResponse(
    @Schema(description = "Settings ID")
    val id: String,
    @Schema(description = "Salon ID")
    val salonId: String,
    @Schema(description = "SMS settings")
    val smsSettings: SmsSettingsResponse,
    @Schema(description = "Push settings")
    val pushSettings: PushSettingsResponse,
    @Schema(description = "Updated by user")
    val updatedBy: String,
    @Schema(description = "Creation timestamp")
    val createdAt: LocalDateTime,
    @Schema(description = "Last update timestamp")
    val updatedAt: LocalDateTime,
) {
    companion object {
        fun from(settings: SmsNotificationSettings): NotificationSettingsResponse {
            return NotificationSettingsResponse(
                id = settings.id.value,
                salonId = settings.salonId.value,
                smsSettings = SmsSettingsResponse.from(settings.smsSettings),
                pushSettings = PushSettingsResponse.from(settings.pushSettings),
                updatedBy = settings.updatedBy.value,
                createdAt = settings.createdAt,
                updatedAt = settings.updatedAt,
            )
        }
    }
}

/**
 * SMS settings response
 */
@Schema(description = "SMS settings response")
data class SmsSettingsResponse(
    @Schema(description = "Whether SMS notifications are enabled")
    val enabled: Boolean,
    @Schema(description = "Appointment confirmation flag")
    val appointmentConfirmation: Boolean,
    @Schema(description = "Appointment cancellation flag")
    val appointmentCancellation: Boolean,
    @Schema(description = "Appointment completion flag")
    val appointmentCompletion: Boolean,
    @Schema(description = "Day-before reminder flag")
    val dayBeforeReminder: Boolean,
    @Schema(description = "Hour-before reminder flag")
    val hourBeforeReminder: Boolean,
    @Schema(description = "SMS templates")
    val templates: SmsTemplatesResponse,
) {
    companion object {
        fun from(smsSettings: SmsSettings): SmsSettingsResponse {
            return SmsSettingsResponse(
                enabled = smsSettings.enabled,
                appointmentConfirmation = smsSettings.appointmentConfirmation,
                appointmentCancellation = smsSettings.appointmentCancellation,
                appointmentCompletion = smsSettings.appointmentCompletion,
                dayBeforeReminder = smsSettings.dayBeforeReminder,
                hourBeforeReminder = smsSettings.hourBeforeReminder,
                templates = SmsTemplatesResponse.from(smsSettings.templates),
            )
        }
    }
}

/**
 * Push settings response
 */
@Schema(description = "Push settings response")
data class PushSettingsResponse(
    @Schema(description = "Whether push notifications are enabled")
    val enabled: Boolean,
    @Schema(description = "Notify when appointment assigned")
    val appointmentAssigned: Boolean,
    @Schema(description = "Notify when appointment cancelled")
    val appointmentCancelled: Boolean,
    @Schema(description = "Notify when appointment completed")
    val appointmentCompleted: Boolean,
    @Schema(description = "Notify when new appointment created")
    val newAppointmentCreated: Boolean,
) {
    companion object {
        fun from(pushSettings: PushSettings): PushSettingsResponse {
            return PushSettingsResponse(
                enabled = pushSettings.enabled,
                appointmentAssigned = pushSettings.appointmentAssigned,
                appointmentCancelled = pushSettings.appointmentCancelled,
                appointmentCompleted = pushSettings.appointmentCompleted,
                newAppointmentCreated = pushSettings.newAppointmentCreated,
            )
        }
    }
}

/**
 * SMS templates response
 */
@Schema(description = "SMS templates response")
data class SmsTemplatesResponse(
    @Schema(description = "Appointment confirmation template")
    val appointmentConfirmation: String,
    @Schema(description = "Appointment cancellation template")
    val appointmentCancellation: String,
    @Schema(description = "Appointment completion template")
    val appointmentCompletion: String,
    @Schema(description = "Day-before reminder template")
    val dayBeforeReminder: String,
    @Schema(description = "Hour-before reminder template")
    val hourBeforeReminder: String,
) {
    companion object {
        fun from(templates: SmsTemplates): SmsTemplatesResponse {
            return SmsTemplatesResponse(
                appointmentConfirmation = templates.appointmentConfirmation,
                appointmentCancellation = templates.appointmentCancellation,
                appointmentCompletion = templates.appointmentCompletion,
                dayBeforeReminder = templates.dayBeforeReminder,
                hourBeforeReminder = templates.hourBeforeReminder,
            )
        }
    }
}

/**
 * Response for notification (matching Flutter NotificationHistory model)
 */
@Schema(description = "Notification details")
data class NotificationResponse(
    @Schema(description = "Notification ID")
    val id: String,
    @Schema(description = "Notification title")
    val title: String,
    @Schema(description = "Notification message")
    val message: String,
    @Schema(description = "Notification type")
    val type: String,
    @Schema(description = "Whether notification is read")
    val read: Boolean,
    @Schema(description = "Notification timestamp")
    val timestamp: LocalDateTime,
    @Schema(description = "Additional metadata")
    val metadata: Map<String, Any>?
) {
    companion object {
        fun from(notification: Notification): NotificationResponse {
            // Extract title and message from notification content
            val (title, message) = when (notification.content) {
                is NotificationContent.Push -> notification.content.title to notification.content.body
            }

            // Use the actual isRead field from the notification domain model
            val isRead = notification.isRead

            // Build metadata with additional information
            val metadata = mutableMapOf<String, Any>()

            // Add appointment ID if present
            notification.appointmentId?.let { metadata["appointmentId"] = it.value }

            // Add salon ID
            metadata["salonId"] = notification.salonId.value

            // Add status information
            metadata["status"] = notification.status.name

            // Add timing information
            notification.sentAt?.let { metadata["sentAt"] = it.toString() }
            notification.deliveredAt?.let { metadata["deliveredAt"] = it.toString() }

            // Add failure information if present
            notification.failureReason?.let { metadata["failureReason"] = it }

            // Add retry information
            metadata["retryCount"] = notification.retryCount
            metadata["maxRetries"] = notification.maxRetries

            // Add push notification data if present
            if (notification.content.data.isNotEmpty()) {
                metadata["pushData"] = notification.content.data
            }

            return NotificationResponse(
                id = notification.id.value,
                title = title,
                message = message,
                type = notification.type.name,
                read = isRead,
                timestamp = notification.createdAt,
                metadata = metadata.takeIf { it.isNotEmpty() }
            )
        }
    }
}

/**
 * Request to send test notification
 */
@Schema(description = "Request to send test notification")
data class SendTestNotificationRequest(
    @Schema(description = "Notification title")
    @field:NotBlank(message = "Title is required")
    val title: String = "Test Notification",
    @Schema(description = "Notification message")
    @field:NotBlank(message = "Message is required")
    val message: String = "This is a test notification from Animalia",
    @Schema(description = "Notification type")
    val type: String = "PUSH"
)

/**
 * Response for test data generation
 */
@Schema(description = "Response for test data generation")
data class GenerateTestDataResponse(
    @Schema(description = "Number of notifications created")
    val notificationsCreated: Int,
    @Schema(description = "Number of FCM tokens created")
    val fcmTokensCreated: Int,
    @Schema(description = "Success message")
    val message: String,
    @Schema(description = "Created notification IDs")
    val notificationIds: List<String> = emptyList()
)

/**
 * Response for test notification sending
 */
@Schema(description = "Response for test notification sending")
data class SendTestNotificationResponse(
    @Schema(description = "Whether notification was sent successfully")
    val success: Boolean,
    @Schema(description = "Notification ID")
    val notificationId: String?,
    @Schema(description = "Number of devices notified")
    val devicesNotified: Int,
    @Schema(description = "Response message")
    val message: String
)

/**
 * Response for bulk mark as read operation
 */
@Schema(description = "Response for bulk mark as read operation")
data class BulkMarkAsReadResponse(
    @Schema(description = "Number of notifications marked as read")
    val notificationsMarked: Int,
    @Schema(description = "Success message")
    val message: String
)

/**
 * Notification statistics response (matching Flutter expectations)
 */
@Schema(description = "Notification statistics response")
data class NotificationStatsResponse(
    @Schema(description = "Total notifications")
    val totalNotifications: Long,
    @Schema(description = "Unread notifications count")
    val unreadCount: Long,
    @Schema(description = "Read notifications count")
    val readCount: Long,
    @Schema(description = "Notifications sent today")
    val todayCount: Long,
    @Schema(description = "Notifications sent this week")
    val weekCount: Long,
    @Schema(description = "Notifications sent this month")
    val monthCount: Long,
    @Schema(description = "Notifications by type")
    val byType: Map<String, Long>,
    @Schema(description = "Additional statistics")
    val additionalStats: Map<String, Any>? = null
)

/**
 * Request to update FCM token
 */
@Schema(description = "Request to update FCM token")
data class UpdateFcmTokenRequest(
    @Schema(description = "New FCM token")
    @field:NotBlank(message = "New token is required")
    val newToken: String
)

/**
 * Paginated notifications response
 */
@Schema(description = "Paginated notifications response")
data class PaginatedNotificationsResponse(
    @Schema(description = "List of notifications")
    val notifications: List<NotificationResponse>,
    @Schema(description = "Total count of notifications")
    val totalCount: Long,
    @Schema(description = "Count of unread notifications")
    val unreadCount: Long,
    @Schema(description = "Current page number")
    val page: Int,
    @Schema(description = "Page size")
    val pageSize: Int,
    @Schema(description = "Whether there are more notifications")
    val hasMore: Boolean
)
