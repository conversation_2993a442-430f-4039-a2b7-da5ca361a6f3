package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * JPA entity for staff working hours settings
 * This stores individual staff member working hours, holidays, and custom closures
 */
@Entity
@Table(name = "staff_working_hours")
data class StaffWorkingHoursSettingsEntity(
    @Id
    val id: String,
    @field:NotBlank(message = "Staff ID is required")
    @Column(name = "staff_id", nullable = false)
    val staffId: String,
    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,
    @Column(name = "weekly_schedule", columnDefinition = "TEXT")
    val weeklySchedule: String, // JSON representation as TEXT for H2 compatibility
    @Column(name = "holidays", columnDefinition = "TEXT")
    val holidays: String, // JSON representation as TEXT for H2 compatibility
    @Column(name = "custom_closures", columnDefinition = "TEXT")
    val customClosures: String, // JSON representation as TEXT for H2 compatibility
    @Column(name = "inherit_from_business", nullable = true)
    val inheritFromBusiness: Boolean? = true, // New field for inheritance control
    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        "", "", "", "{}", "[]", "[]", true,
        LocalDateTime.now(), LocalDateTime.now(),
    )
}
