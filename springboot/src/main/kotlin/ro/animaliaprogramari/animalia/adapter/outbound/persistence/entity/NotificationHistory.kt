package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * JPA entity for notification history
 * Optimized for fast queries with proper indexing
 */
@Entity
@Table(
    name = "notification_history",
    indexes = [
        Index(name = "idx_notification_salon_timestamp", columnList = "salon_id, timestamp"),
        Index(name = "idx_notification_type_read", columnList = "type, read_status"),
        Index(name = "idx_notification_appointment", columnList = "appointment_id"),
        Index(name = "idx_notification_client", columnList = "client_id"),
        Index(name = "idx_notification_timestamp", columnList = "timestamp"),
        Index(name = "idx_notification_salon_type", columnList = "salon_id, type")
    ]
)
class NotificationHistory(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Column(name = "title", nullable = false, length = 255)
    val title: String,

    @Column(name = "message", nullable = false, columnDefinition = "TEXT")
    val message: String,

    @Column(name = "type", nullable = false, length = 50)
    val type: String,

    @Column(name = "read_status", nullable = false)
    val readStatus: Boolean = false,

    @Column(name = "timestamp", nullable = false)
    val timestamp: LocalDateTime = LocalDateTime.now(),

    @Column(name = "appointment_id", nullable = true)
    val appointmentId: String? = null,

    @Column(name = "client_id", nullable = true)
    val clientId: String? = null,

    @Column(name = "metadata", columnDefinition = "TEXT")
    val metadata: String? = null,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = 0,
        salonId = "",
        title = "",
        message = "",
        type = "",
        readStatus = false,
        timestamp = LocalDateTime.now(),
        appointmentId = null,
        clientId = null,
        metadata = null,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NotificationHistory

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "NotificationHistory(id=$id, salonId='$salonId', title='$title', type='$type', timestamp=$timestamp)"
    }
}
