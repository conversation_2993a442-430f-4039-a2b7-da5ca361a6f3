package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffWorkingHoursSettingsEntity

/**
 * Spring Data JPA repository for staff working hours settings
 */
@Repository
interface SpringStaffWorkingHoursRepository : JpaRepository<StaffWorkingHoursSettingsEntity, String> {
    /**
     * Find working hours settings by staff ID and salon ID
     * Returns null if no working hours are configured for this staff member
     */
    fun findByStaffIdAndSalonId(
        staffId: String,
        salonId: String,
    ): StaffWorkingHoursSettingsEntity?

    /**
     * Check if working hours settings exist for staff
     */
    fun existsByStaffIdAndSalonId(
        staffId: String,
        salonId: String,
    ): Boolean

    /**
     * Delete working hours settings by staff ID and salon ID
     */
    fun deleteByStaffIdAndSalonId(
        staffId: String,
        salonId: String,
    )

    /**
     * Find all staff working hours for a salon
     */
    fun findBySalonId(salonId: String): List<StaffWorkingHoursSettingsEntity>

    /**
     * Find working hours settings by multiple staff IDs and salon ID
     * Returns list of working hours for the specified staff members
     */
    fun findByStaffIdInAndSalonId(
        staffIds: List<String>,
        salonId: String,
    ): List<StaffWorkingHoursSettingsEntity>

    /**
     * Optimized batch query with eager loading of related entities
     */
    @Query(
        "SELECT DISTINCT swh FROM StaffWorkingHoursSettingsEntity swh " +
            "LEFT JOIN FETCH swh.weeklySchedule " +
            "LEFT JOIN FETCH swh.holidays " +
            "LEFT JOIN FETCH swh.customClosures " +
            "WHERE swh.staffId IN :staffIds AND swh.salonId = :salonId",
    )
    fun findByStaffIdsWithEagerLoading(
        @Param("staffIds") staffIds: List<String>,
        @Param("salonId") salonId: String,
    ): List<StaffWorkingHoursSettingsEntity>

    /**
     * Check if any staff have working hours configured
     */
    fun existsByStaffIdInAndSalonId(
        staffIds: List<String>,
        salonId: String,
    ): Boolean
}
