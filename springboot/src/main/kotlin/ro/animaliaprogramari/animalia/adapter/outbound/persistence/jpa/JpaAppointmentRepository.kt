package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.AppointmentEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime

/**
 * JPA adapter implementing the AppointmentRepository port
 * This adapter translates between domain models and JPA entities
 */
@Repository
class JpaAppointmentRepository(
    private val springRepository: SpringAppointmentRepository,
    private val appointmentMapper: AppointmentEntityMapper,
) : AppointmentRepository {
    override fun save(appointment: Appointment): Appointment {
        val entity = appointmentMapper.toEntity(appointment)
        val savedEntity = springRepository.save(entity)
        return appointmentMapper.toDomain(savedEntity)
    }

    override fun findById(id: AppointmentId): Appointment? {
        return springRepository.findById(id.value)
            .map { appointmentMapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByDateRange(
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<Appointment> {
        return springRepository.findByAppointmentDateBetween(startDate, endDate)
            .map { appointmentMapper.toDomain(it) }
    }

    override fun findByGroomerAndDateRange(
        userId: UserId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<Appointment> {
        return springRepository.findByStaffIdAndAppointmentDateBetween(
            userId.value,
            startDate,
            endDate,
        ).map { appointmentMapper.toDomain(it) }
    }

    override fun findByClientId(clientId: ClientId): List<Appointment> {
        return springRepository.findByClientId(clientId.value)
            .map { appointmentMapper.toDomain(it) }
    }

    override fun findByPetId(petId: PetId): List<Appointment> {
        return springRepository.findByPetId(petId.value)
            .map { appointmentMapper.toDomain(it) }
    }

    override fun findByStatus(status: AppointmentStatus): List<Appointment> {
        return springRepository.findByStatus(status.name)
            .map { appointmentMapper.toDomain(it) }
    }

    override fun findByStatuses(statuses: List<AppointmentStatus>): List<Appointment> {
        val statusStrings = statuses.map { it.name.lowercase() } // Fix: Use lowercase to match database storage
        return springRepository.findByStatusIn(statusStrings)
            .map { appointmentMapper.toDomain(it) }
    }

    override fun findByDate(
        date: LocalDate,
        userId: UserId?,
        status: AppointmentStatus?,
    ): List<Appointment> {
        // Use programmatic filtering to avoid PostgreSQL parameter type issues
        var entities = springRepository.findByAppointmentDateBetween(date, date)

        // Apply filters programmatically
        if (userId != null) {
            entities = entities.filter { it.staffId == userId.value }
        }
        if (status != null) {
            entities = entities.filter { it.status == status.name.lowercase() }
        }

        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findAll(
        startDate: LocalDate?,
        endDate: LocalDate?,
        userId: UserId?,
        clientId: ClientId?,
        status: AppointmentStatus?,
        limit: Int?,
        offset: Int?,
    ): List<Appointment> {
        // For simplicity, using basic filtering
        // In a real implementation, you might use Criteria API or custom queries
        var appointments =
            if (startDate != null && endDate != null) {
                springRepository.findByAppointmentDateBetween(startDate, endDate)
            } else {
                springRepository.findAll()
            }

        return appointments
            .filter { userId == null || it.staffId == userId.value }
            .filter { clientId == null || it.clientId == clientId.value }
            .filter { status == null || it.status == status.name.lowercase() } // Fix: Use lowercase to match database storage
            .let { if (offset != null) it.drop(offset) else it }
            .let { if (limit != null) it.take(limit) else it }
            .map { appointmentMapper.toDomain(it) }
    }

    override fun existsById(id: AppointmentId): Boolean {
        return springRepository.existsById(id.value)
    }

    override fun deleteById(id: AppointmentId): Boolean {
        return if (springRepository.existsById(id.value)) {
            springRepository.deleteById(id.value)
            true
        } else {
            false
        }
    }

    override fun count(
        startDate: LocalDate?,
        endDate: LocalDate?,
        userId: UserId?,
        clientId: ClientId?,
        status: AppointmentStatus?,
    ): Long {
        // Simplified implementation
        return findAll(startDate, endDate, userId, clientId, status).size.toLong()
    }

    // Salon-specific methods implementation

    override fun findBySalonIdWithFilters(
        salonId: SalonId,
        date: LocalDate?,
        startDate: LocalDate?,
        endDate: LocalDate?,
        status: AppointmentStatus?,
        clientId: ClientId?,
        staffId: StaffId?,
    ): List<Appointment> {
        // Use programmatic filtering to avoid PostgreSQL parameter type issues
        // Start with base query for the salon
        var entities = if (date != null) {
            // If specific date is requested, use date range query
            springRepository.findBySalonIdAndDateRange(salonId.value, date, date)
        } else if (startDate != null && endDate != null) {
            // If date range is specified, use date range query
            springRepository.findBySalonIdAndDateRange(salonId.value, startDate, endDate)
        } else if (startDate != null) {
            // If only start date, get from start date to far future
            springRepository.findBySalonIdAndDateRange(salonId.value, startDate, LocalDate.now().plusYears(1))
        } else if (endDate != null) {
            // If only end date, get from far past to end date
            springRepository.findBySalonIdAndDateRange(salonId.value, LocalDate.now().minusYears(1), endDate)
        } else {
            // No date filters, get all appointments for salon
            springRepository.findBySalonId(salonId.value)
        }

        // Apply additional filters programmatically
        if (status != null) {
            entities = entities.filter { it.status == status.name.lowercase() }
        }
        if (clientId != null) {
            entities = entities.filter { it.clientId == clientId.value }
        }
        if (staffId != null) {
            entities = entities.filter { it.staffId == staffId.value }
        }

        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findBySalonIdAndStaffIdWithFilters(
        salonId: SalonId,
        staffId: StaffId,
        date: LocalDate?,
        startDate: LocalDate?,
        endDate: LocalDate?,
        status: AppointmentStatus?,
    ): List<Appointment> {
        // Use programmatic filtering to avoid PostgreSQL parameter type issues
        var entities =
            springRepository.findBySalonIdAndStaffIdAndAppointmentDate(
                salonId.value,
                staffId.value,
                date ?: LocalDate.now(), // Use a default date if null, then filter below
            )

        // If date was null, get all appointments for this salon/staff and filter programmatically
        if (date == null) {
            entities =
                springRepository.findBySalonId(salonId.value)
                    .filter { it.staffId == staffId.value }
        }

        // Apply additional filters programmatically
        if (date != null) {
            entities = entities.filter { it.appointmentDate == date }
        }
        if (startDate != null) {
            entities = entities.filter { it.appointmentDate >= startDate }
        }
        if (endDate != null) {
            entities = entities.filter { it.appointmentDate <= endDate }
        }
        if (status != null) {
            entities = entities.filter { it.status == status.name.lowercase() }
        }

        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findBySalonIdAndStaffIdAndDate(
        salonId: SalonId,
        staffId: StaffId,
        date: LocalDate,
    ): List<Appointment> {
        val entities =
            springRepository.findBySalonIdAndStaffIdAndAppointmentDate(
                salonId = salonId.value,
                staffId = staffId.value,
                appointmentDate = date,
            )
        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findByDateRange(
        startDate: LocalDate,
        endDate: LocalDate,
        staffId: StaffId?,
        status: AppointmentStatus?,
    ): List<Appointment> {
        // Use programmatic filtering to avoid PostgreSQL parameter type issues
        var entities = if (staffId != null) {
            // Get appointments for specific staff in date range
            springRepository.findByStaffIdAndAppointmentDateBetween(
                staffId = staffId.value,
                startDate = startDate,
                endDate = endDate,
            )
        } else {
            // For all staff, use date range query
            springRepository.findByAppointmentDateBetween(startDate, endDate)
        }

        // Apply status filter programmatically
        if (status != null) {
            entities = entities.filter { it.status == status.name.lowercase() }
        }

        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findByStaffId(
        staffId: StaffId,
        startDate: LocalDate?,
        endDate: LocalDate?,
        status: AppointmentStatus?,
    ): List<Appointment> {
        // Use programmatic filtering to avoid PostgreSQL parameter type issues
        var entities = if (startDate != null && endDate != null) {
            // Use date range query if both dates provided
            springRepository.findByStaffIdAndAppointmentDateBetween(
                staffId = staffId.value,
                startDate = startDate,
                endDate = endDate,
            )
        } else {
            // Get all appointments for staff and filter programmatically
            springRepository.findByStaffId(staffId.value)
                .let { appointments ->
                    var filtered = appointments
                    if (startDate != null) {
                        filtered = filtered.filter { it.appointmentDate >= startDate }
                    }
                    if (endDate != null) {
                        filtered = filtered.filter { it.appointmentDate <= endDate }
                    }
                    filtered
                }
        }

        // Apply status filter programmatically
        if (status != null) {
            entities = entities.filter { it.status == status.name.lowercase() }
        }

        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findByStaffIdAndDate(
        staffId: StaffId,
        date: LocalDate,
    ): List<Appointment> {
        val entities =
            springRepository.findByStaffIdAndAppointmentDate(
                staffId = staffId.value,
                appointmentDate = date,
            )
        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findByStaffIdAndDateAfter(
        staffId: StaffId,
        date: LocalDate,
    ): List<Appointment> {
        // Use programmatic filtering to avoid PostgreSQL parameter type issues
        val entities =
            springRepository.findByStaffId(staffId.value)
                .filter { it.appointmentDate > date }
        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findByStaffIdAndDateBetween(
        staffId: StaffId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<Appointment> {
        val entities =
            springRepository.findByStaffIdAndAppointmentDateBetween(
                staffId = staffId.value,
                startDate = startDate,
                endDate = endDate,
            )
        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findByDateAndStatus(
        date: LocalDate,
        status: AppointmentStatus,
    ): List<Appointment> {
        val entities =
            springRepository.findByAppointmentDateAndStatus(
                appointmentDate = date,
                status = status.name.lowercase(),
            )
        return entities.map { appointmentMapper.toDomain(it) }
    }

    override fun findBySalonIdAndStaffIdsAndTimeRange(
        salonId: SalonId,
        staffIds: Set<StaffId>,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
    ): List<Appointment> {
        // Convert ZonedDateTime to LocalDate for the query
        val startDate = startTime.toLocalDate()
        val endDate = endTime.toLocalDate()

        // Create a list of staff ID strings
        val staffIdValues = staffIds.map { it.value }

        // Use a custom query to find appointments by salon ID and date range
        val entities =
            springRepository.findBySalonIdAndDateRange(
                salonId = salonId.value,
                startDate = startDate,
                endDate = endDate,
            )

        // Filter by staff IDs and time overlap in memory
        return entities
            .filter { entity -> staffIdValues.contains(entity.staffId) }
            .filter { entity ->
                // Check if appointment time overlaps with the block time
                val appointmentStart = entity.appointmentDate.atTime(entity.startTime).atZone(startTime.zone)
                val appointmentEnd = entity.appointmentDate.atTime(entity.endTime).atZone(startTime.zone)
                appointmentStart.isBefore(endTime) && appointmentEnd.isAfter(startTime)
            }
            .map { appointmentMapper.toDomain(it) }
    }

    /**
     * Optimized method for conflict detection - finds overlapping appointments
     */
    fun findConflictingAppointments(
        staffId: StaffId,
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
    ): List<Appointment> {
        val entities = springRepository.findConflictingAppointments(
            staffId = staffId.value,
            date = date,
            startTime = startTime,
            endTime = endTime,
        )
        return entities.map { appointmentMapper.toDomain(it) }
    }

    /**
     * Optimized method for appointment suggestions
     */
    fun findAppointmentsForSuggestions(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate,
        staffIds: List<StaffId>,
    ): List<Appointment> {
        val entities = springRepository.findAppointmentsForSuggestions(
            salonId = salonId.value,
            startDate = startDate,
            endDate = endDate,
            staffIds = staffIds.map { it.value },
        )
        return entities.map { appointmentMapper.toDomain(it) }
    }
}
