package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * JPA entity for holidays
 * This is an infrastructure concern - data representation for persistence
 */
@Entity
@Table(
    name = "holidays",
    uniqueConstraints = [UniqueConstraint(columnNames = ["salon_id", "date"])],
)
data class Holiday(
    @Id
    val id: String,
    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,
    @field:NotBlank(message = "Holiday name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @Column(nullable = false)
    val name: String,
    @field:NotNull(message = "Holiday date is required")
    @Column(nullable = false)
    val date: LocalDate,
    @Column(name = "is_working_day")
    val isWorkingDay: Boolean = false,
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val type: HolidayType = HolidayType.LEGAL,
    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        "",
        "",
        "",
        LocalDate.now(),
        false,
        HolidayType.LEGAL,
        LocalDateTime.now(),
        LocalDateTime.now(),
    )
}

/**
 * Enumeration for holiday types
 */
enum class HolidayType {
    LEGAL,
    RELIGIOUS,
    NATIONAL,
}
