package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.command.UpdateNotificationSettingsCommand
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Mapper between notification settings DTOs and domain models
 */
@Component
class NotificationSettingsDtoMapper {
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    /**
     * Convert domain model to response DTO
     */
    fun toResponse(domain: NotificationSettings): NotificationSettingsResponse {
        return NotificationSettingsResponse(
            salonId = domain.salonId.value,
            pushNotificationsEnabled = domain.pushNotificationsEnabled,
            soundPreference = domain.soundPreference.displayName,
            vibrationEnabled = domain.vibrationEnabled,
            doNotDisturb =
                DoNotDisturbResponse(
                    enabled = domain.doNotDisturb.enabled,
                    startTime = domain.doNotDisturb.startTime.format(timeFormatter),
                    endTime = domain.doNotDisturb.endTime.format(timeFormatter),
                    allowCritical = domain.doNotDisturb.allowCritical,
                ),
            notificationRules =
                NotificationRulesResponse(
                    newAppointments = domain.notificationRules.newAppointments,
                    appointmentCancellations = domain.notificationRules.appointmentCancellations,
                    paymentConfirmations = domain.notificationRules.paymentConfirmations,
                    teamMemberUpdates = domain.notificationRules.teamMemberUpdates,
                    systemMaintenanceAlerts = domain.notificationRules.systemMaintenanceAlerts,
                    defaultPriority = domain.notificationRules.defaultPriority.name,
                ),
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert request DTO to command
     */
    fun toCommand(
        userId: UserId,
        salonId: SalonId,
        request: UpdateNotificationSettingsRequest,
    ): UpdateNotificationSettingsCommand {
        return UpdateNotificationSettingsCommand(
            userId = userId,
            salonId = salonId,
            pushNotificationsEnabled = request.pushNotificationsEnabled,
            soundPreference = SoundPreference.fromString(request.soundPreference),
            vibrationEnabled = request.vibrationEnabled,
            doNotDisturb =
                DoNotDisturbSettings(
                    enabled = request.doNotDisturb.enabled,
                    startTime = parseTime(request.doNotDisturb.startTime),
                    endTime = parseTime(request.doNotDisturb.endTime),
                    allowCritical = request.doNotDisturb.allowCritical,
                ),
            notificationRules =
                NotificationRules(
                    newAppointments = request.notificationRules.newAppointments,
                    appointmentCancellations = request.notificationRules.appointmentCancellations,
                    paymentConfirmations = request.notificationRules.paymentConfirmations,
                    teamMemberUpdates = request.notificationRules.teamMemberUpdates,
                    systemMaintenanceAlerts = request.notificationRules.systemMaintenanceAlerts,
                    defaultPriority = NotificationPriority.fromString(request.notificationRules.defaultPriority),
                ),
        )
    }

    /**
     * Parse time string in HH:MM format
     */
    private fun parseTime(timeString: String): LocalTime {
        return try {
            LocalTime.parse(timeString, timeFormatter)
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid time format. Expected HH:MM, got: $timeString")
        }
    }
}
