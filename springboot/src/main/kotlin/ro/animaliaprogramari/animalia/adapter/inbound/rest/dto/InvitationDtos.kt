package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

/**
 * Request DTO for sending salon invitation
 */
@Schema(description = "Request to send a salon invitation")
data class SendSalonInvitationRequest(
    @Schema(description = "Phone number of the invited user")
    @field:NotBlank(message = "Phone number is required")
    @JsonProperty("invitedUserPhone")
    val invitedUserPhone: String,
    @Schema(description = "Proposed staff role")
    @field:NotBlank(message = "Proposed role is required")
    @JsonProperty("proposedRole")
    val proposedRole: String,
    @Schema(description = "Proposed client data permission")
    @field:NotBlank(message = "Client data permission is required")
    @JsonProperty("proposedClientDataPermission")
    val proposedClientDataPermission: String,
    @Schema(description = "Proposed nickname for the invited user")
    @field:Size(max = 100, message = "Nickname must not exceed 100 characters")
    @JsonProperty("proposedNickname")
    val proposedNickname: String?,
    @Schema(description = "Optional invitation message")
    @field:Size(max = 500, message = "Message must not exceed 500 characters")
    @JsonProperty("message")
    val message: String?,
)

/**
 * Request DTO for resending invitation
 */
@Schema(description = "Request to resend an invitation")
data class ResendInvitationRequest(
    @Schema(description = "Optional invitation message")
    @field:Size(max = 500, message = "Message must not exceed 500 characters")
    @JsonProperty("message")
    val message: String?,
)

/**
 * Response DTO for salon invitation
 */
@Schema(description = "Invitation details")
data class SalonInvitationResponse(
    @Schema(description = "Invitation ID")
    @JsonProperty("id")
    val id: String,
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    @Schema(description = "Salon name")
    @JsonProperty("salonName")
    val salonName: String?,
    @Schema(description = "Inviter user ID")
    @JsonProperty("inviterUserId")
    val inviterUserId: String,
    @Schema(description = "Inviter name")
    @JsonProperty("inviterName")
    val inviterName: String?,
    @Schema(description = "Invited user's phone number")
    @JsonProperty("invitedUserPhone")
    val invitedUserPhone: String,
    @Schema(description = "Proposed staff role")
    @JsonProperty("proposedRole")
    val proposedRole: String,
    @Schema(description = "Proposed client data permission")
    @JsonProperty("proposedClientDataPermission")
    val proposedClientDataPermission: String,
    @Schema(description = "Proposed nickname for the invited user")
    @JsonProperty("proposedNickname")
    val proposedNickname: String?,
    @Schema(description = "Invitation status")
    @JsonProperty("status")
    val status: String,
    @Schema(description = "Invitation message")
    @JsonProperty("message")
    val message: String?,
    @Schema(description = "When the invitation was sent")
    @JsonProperty("invitedAt")
    val invitedAt: LocalDateTime,
    @Schema(description = "When the invitation was responded to")
    @JsonProperty("respondedAt")
    val respondedAt: LocalDateTime?,
    @Schema(description = "When the invitation expires")
    @JsonProperty("expiresAt")
    val expiresAt: LocalDateTime,
    @Schema(description = "Whether the invitation has expired")
    @JsonProperty("isExpired")
    val isExpired: Boolean,
    @Schema(description = "Whether the invitation can be responded to")
    @JsonProperty("canBeResponded")
    val canBeResponded: Boolean,
    @Schema(description = "How many times it was resent")
    @JsonProperty("resendCount")
    val resendCount: Int,
    @Schema(description = "Timestamp of last resend")
    @JsonProperty("lastResendAt")
    val lastResendAt: LocalDateTime?,
    @Schema(description = "When the invitation was cancelled")
    @JsonProperty("cancelledAt")
    val cancelledAt: LocalDateTime?,
    @Schema(description = "User who cancelled the invitation")
    @JsonProperty("cancelledBy")
    val cancelledBy: String?,
    @Schema(description = "Creation timestamp")
    @JsonProperty("createdAt")
    val createdAt: LocalDateTime,
    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime,
)

/**
 * Response DTO for groomer permissions after accepting invitation
 */
@Schema(description = "Permissions granted after accepting an invitation")
data class GroomerPermissionsResponse(
    @Schema(description = "User ID")
    @JsonProperty("userId")
    val userId: String,
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    @Schema(description = "Assigned groomer role")
    @JsonProperty("groomerRole")
    val groomerRole: String,
    @Schema(description = "Client data permission")
    @JsonProperty("clientDataPermission")
    val clientDataPermission: String,
    @Schema(description = "Whether the association is active")
    @JsonProperty("isActive")
    val isActive: Boolean,
    @Schema(description = "Creation timestamp")
    @JsonProperty("createdAt")
    val createdAt: LocalDateTime,
    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime,
)
