package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.*
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Response DTO for working hours settings
 */
@Schema(description = "Working hours settings")
data class WorkingHoursResponse(
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    @Schema(description = "Weekly schedule")
    @JsonProperty("weeklySchedule")
    val weeklySchedule: Map<String, DayScheduleResponse>,
    @Schema(description = "List of holidays")
    @JsonProperty("holidays")
    val holidays: List<HolidayResponse>,
    @Schema(description = "Custom closures")
    @JsonProperty("customClosures")
    val customClosures: List<CustomClosureResponse>,
    @Schema(description = "Last update timestamp")
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime,
)

/**
 * Response DTO for day schedule
 */
@Schema(description = "Day schedule")
data class DayScheduleResponse(
    @Schema(description = "Start time")
    @JsonProperty("startTime")
    val startTime: String?,
    @Schema(description = "End time")
    @JsonProperty("endTime")
    val endTime: String?,
    @Schema(description = "Whether it's a working day")
    @JsonProperty("isWorkingDay")
    val isWorkingDay: Boolean,
    @Schema(description = "Break start time")
    @JsonProperty("breakStart")
    val breakStart: String?,
    @Schema(description = "Break end time")
    @JsonProperty("breakEnd")
    val breakEnd: String?,
)

/**
 * Response DTO for holiday
 */
@Schema(description = "Holiday details")
data class HolidayResponse(
    @Schema(description = "Holiday name")
    @JsonProperty("name")
    val name: String,
    @Schema(description = "Holiday date")
    @JsonProperty("date")
    val date: LocalDate,
    @Schema(description = "Whether it's a working day")
    @JsonProperty("isWorkingDay")
    val isWorkingDay: Boolean,
    @Schema(description = "Holiday type")
    @JsonProperty("type")
    val type: String,
)

/**
 * Response DTO for custom closure
 */
@Schema(description = "Custom closure")
data class CustomClosureResponse(
    @Schema(description = "Closure reason")
    @JsonProperty("reason")
    val reason: String,
    @Schema(description = "Closure date")
    @JsonProperty("date")
    val date: LocalDate,
    @Schema(description = "Optional description")
    @JsonProperty("description")
    val description: String?,
)

/**
 * Request DTO for updating working hours
 */
@Schema(description = "Request to update working hours")
data class UpdateWorkingHoursRequest(
    @Schema(description = "Weekly schedule")
    @field:Valid
    @JsonProperty("weeklySchedule")
    val weeklySchedule: Map<String, DayScheduleRequest>,
    @Schema(description = "Holidays list")
    @field:Valid
    @JsonProperty("holidays")
    val holidays: List<HolidayRequest> = emptyList(),
    @Schema(description = "Custom closures")
    @field:Valid
    @JsonProperty("customClosures")
    val customClosures: List<CustomClosureRequest> = emptyList(),
)

/**
 * Request DTO for day schedule
 */
@Schema(description = "Day schedule request")
data class DayScheduleRequest(
    @field:Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "Start time must be in HH:MM format")
    @Schema(description = "Start time")
    @JsonProperty("startTime")
    val startTime: String?,
    @field:Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "End time must be in HH:MM format")
    @Schema(description = "End time")
    @JsonProperty("endTime")
    val endTime: String?,
    @Schema(description = "Whether it's a working day")
    @JsonProperty("isWorkingDay")
    val isWorkingDay: Boolean = false,
    @field:Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "Break start time must be in HH:MM format")
    @Schema(description = "Break start time")
    @JsonProperty("breakStart")
    val breakStart: String? = null,
    @field:Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "Break end time must be in HH:MM format")
    @Schema(description = "Break end time")
    @JsonProperty("breakEnd")
    val breakEnd: String? = null,
)

/**
 * Request DTO for holiday
 */
@Schema(description = "Holiday request")
data class HolidayRequest(
    @field:NotBlank(message = "Holiday name is required")
    @field:Size(max = 255, message = "Holiday name must not exceed 255 characters")
    @Schema(description = "Holiday name")
    @JsonProperty("name")
    val name: String,
    @field:NotNull(message = "Holiday date is required")
    @Schema(description = "Holiday date")
    @JsonProperty("date")
    val date: LocalDate,
    @Schema(description = "Whether it's a working day")
    @JsonProperty("isWorkingDay")
    val isWorkingDay: Boolean = false,
    @field:NotBlank(message = "Holiday type is required")
    @field:Pattern(
        regexp = "^(LEGAL|RELIGIOUS|NATIONAL)$",
        message = "Holiday type must be LEGAL, RELIGIOUS, or NATIONAL",
    )
    @Schema(description = "Holiday type")
    @JsonProperty("type")
    val type: String,
)

/**
 * Request DTO for custom closure
 */
@Schema(description = "Custom closure request")
data class CustomClosureRequest(
    @field:NotBlank(message = "Closure reason is required")
    @field:Size(max = 255, message = "Closure reason must not exceed 255 characters")
    @Schema(description = "Closure reason")
    @JsonProperty("reason")
    val reason: String,
    @field:NotNull(message = "Closure date is required")
    @Schema(description = "Closure date")
    @JsonProperty("date")
    val date: LocalDate,
    @field:Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Schema(description = "Optional description")
    @JsonProperty("description")
    val description: String? = null,
)
