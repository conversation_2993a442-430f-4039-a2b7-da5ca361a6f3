package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.InvitationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.query.GetInvitationByIdQuery
import ro.animaliaprogramari.animalia.application.query.GetInvitationsBySalonQuery
import ro.animaliaprogramari.animalia.application.query.GetPendingInvitationsByPhoneQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.model.*
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for invitation management
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping
@CrossOrigin(origins = ["*"])
@Tag(name = "Invitations", description = "Operations for managing salon invitations")
class InvitationController(
    private val invitationManagementUseCase: InvitationManagementUseCase,
    private val salonRepository: SalonRepository,
    private val userRepository: UserRepository,
) {
    private val logger = LoggerFactory.getLogger(InvitationController::class.java)

    /**
     * POST /api/salon/{salonId}/invitations
     * Send invitation to join salon
     */
    @PostMapping("/salon/{salonId}/invitations")
    @Operation(summary = "Send salon invitation", description = "Invite a user to join a salon")
    @SwaggerApiResponse(responseCode = "201", description = "Invitation sent successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun sendInvitation(
        @PathVariable salonId: String,
        @Valid @RequestBody request: SendSalonInvitationRequest,
    ): ResponseEntity<ApiResponse<SalonInvitationResponse>> {
        logger.debug("REST request to send invitation for salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.of(salonId),
                    inviterUserId = currentUser.userId,
                    invitedUserPhone = request.invitedUserPhone,
                    proposedRole = StaffRole.valueOf(request.proposedRole.uppercase()),
                    proposedPermissions =
                        StaffPermissions.fromClientDataPermission(
                            request.proposedClientDataPermission.uppercase(),
                        ),
                    proposedNickname = request.proposedNickname,
                    message = request.message,
                )

            val invitation = invitationManagementUseCase.sendInvitation(command)
            val response = mapToResponse(invitation)

            ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error sending invitation: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to send invitation"))
        } catch (e: Exception) {
            logger.error("Error sending invitation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to send invitation: ${e.message}"))
        }
    }

    /**
     * GET /api/user/{userId}/invitations/pending
     * Get pending invitations for current user
     */
    @GetMapping("/user/{userId}/invitations/pending")
    @Operation(summary = "Get pending invitations", description = "Retrieve invitations pending for a user")
    @SwaggerApiResponse(responseCode = "200", description = "Invitations retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun getPendingInvitations(
        @PathVariable userId: String,
    ): ResponseEntity<ApiResponse<List<SalonInvitationResponse>>> {
        logger.debug("REST request to get pending invitations for user: $userId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Verify user can access these invitations
            if (currentUser.userId.value != userId && !currentUser.isAdmin()) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Access denied"))
            }

            val user =
                userRepository.findById(UserId.of(userId))
                    ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("User not found"))

            val phoneNumber =
                user.phoneNumber
                    ?: return ResponseEntity.ok(ApiResponse.success(emptyList()))

            val query = GetPendingInvitationsByPhoneQuery(phoneNumber)
            val invitations = invitationManagementUseCase.getPendingInvitationsByPhone(query)
            val responses = invitations.map { mapToResponse(it) }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting pending invitations", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get pending invitations: ${e.message}"))
        }
    }

    /**
     * GET /api/salon/{salonId}/invitations
     * Get sent invitations for current salon
     */
    @GetMapping("/salon/{salonId}/invitations")
    fun getSentInvitations(
        @PathVariable salonId: String,
    ): ResponseEntity<ApiResponse<List<SalonInvitationResponse>>> {
        logger.debug("REST request to get sent invitations for salon: $salonId")

        return try {
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val query = GetInvitationsBySalonQuery(SalonId.of(salonId))
            val invitations = invitationManagementUseCase.getInvitationsBySalon(query)
            val responses = invitations.filter { it.status != InvitationStatus.ACCEPTED }.map { mapToResponse(it) }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting sent invitations", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get sent invitations: ${e.message}"))
        }
    }

    /**
     * POST /api/invitations/{invitationId}/accept
     * Accept salon invitation
     */
    @PostMapping("/invitations/{invitationId}/accept")
    @Operation(summary = "Accept invitation", description = "Accept a salon invitation")
    @SwaggerApiResponse(responseCode = "200", description = "Invitation accepted")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun acceptInvitation(
        @PathVariable invitationId: String,
    ): ResponseEntity<ApiResponse<GroomerPermissionsResponse>> {
        logger.debug("REST request to accept invitation: $invitationId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val command =
                AcceptSalonInvitationCommand(
                    invitationId = InvitationId.of(invitationId),
                    acceptingUserId = currentUser.userId,
                )

            val staff = invitationManagementUseCase.acceptInvitation(command)
            val response =
                GroomerPermissionsResponse(
                    userId = staff.userId.value,
                    salonId = staff.salonId.value,
                    groomerRole = staff.role.name,
                    clientDataPermission = staff.permissions.clientDataAccess.name,
                    isActive = staff.isActive,
                    createdAt = staff.createdAt,
                    updatedAt = staff.updatedAt,
                )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error accepting invitation: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to accept invitation"))
        } catch (e: Exception) {
            logger.error("Error accepting invitation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to accept invitation: ${e.message}"))
        }
    }

    /**
     * POST /api/invitations/{invitationId}/decline
     * Decline salon invitation
     */
    @PostMapping("/invitations/{invitationId}/decline")
    fun declineInvitation(
        @PathVariable invitationId: String,
    ): ResponseEntity<ApiResponse<Boolean>> {
        logger.debug("REST request to decline invitation: $invitationId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val command =
                DeclineSalonInvitationCommand(
                    invitationId = InvitationId.of(invitationId),
                    decliningUserId = currentUser.userId,
                )

            invitationManagementUseCase.declineInvitation(command)
            ResponseEntity.ok(ApiResponse.success(true))
        } catch (e: DomainException) {
            logger.warn("Domain error declining invitation: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to decline invitation"))
        } catch (e: Exception) {
            logger.error("Error declining invitation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to decline invitation: ${e.message}"))
        }
    }

    /**
     * DELETE /api/invitations/{invitationId}
     * Cancel sent invitation (admin only)
     */
    @DeleteMapping("/invitations/{invitationId}")
    fun cancelInvitation(
        @PathVariable invitationId: String,
    ): ResponseEntity<ApiResponse<Boolean>> {
        logger.debug("REST request to cancel invitation: $invitationId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val command =
                CancelSalonInvitationCommand(
                    invitationId = InvitationId.of(invitationId),
                    cancellingUserId = currentUser.userId,
                )

            invitationManagementUseCase.cancelInvitation(command)
            ResponseEntity.ok(ApiResponse.success(true))
        } catch (e: DomainException) {
            logger.warn("Domain error cancelling invitation: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to cancel invitation"))
        } catch (e: Exception) {
            logger.error("Error cancelling invitation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to cancel invitation: ${e.message}"))
        }
    }

    /**
     * POST /api/invitations/{invitationId}/resend
     * Resend invitation (admin only)
     */
    @PostMapping("/invitations/{invitationId}/resend")
    fun resendInvitation(
        @PathVariable invitationId: String,
        @RequestBody(required = false) request: ResendInvitationRequest?,
    ): ResponseEntity<ApiResponse<SalonInvitationResponse>> {
        logger.debug("REST request to resend invitation: $invitationId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val command =
                ResendSalonInvitationCommand(
                    invitationId = InvitationId.of(invitationId),
                    resendingUserId = currentUser.userId,
                    newMessage = request?.message,
                )

            val invitation = invitationManagementUseCase.resendInvitation(command)
            val response = mapToResponse(invitation)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error resending invitation: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to resend invitation"))
        } catch (e: Exception) {
            logger.error("Error resending invitation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to resend invitation: ${e.message}"))
        }
    }

    /**
     * GET /api/invitations/{invitationId}
     * Get invitation by ID
     */
    @GetMapping("/invitations/{invitationId}")
    @Operation(summary = "Get invitation", description = "Retrieve invitation details by ID")
    @SwaggerApiResponse(responseCode = "200", description = "Invitation retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun getInvitation(
        @PathVariable invitationId: String,
    ): ResponseEntity<ApiResponse<SalonInvitationResponse>> {
        logger.debug("REST request to get invitation: $invitationId")

        return try {
            val query = GetInvitationByIdQuery(InvitationId.of(invitationId))
            val invitation =
                invitationManagementUseCase.getInvitationById(query)
                    ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Invitation not found"))

            val response = mapToResponse(invitation)
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error getting invitation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get invitation: ${e.message}"))
        }
    }

    private fun mapToResponse(invitation: SalonInvitation): SalonInvitationResponse {
        // Get salon name
        val salon = salonRepository.findById(invitation.salonId)
        val salonName = salon?.name

        // Get inviter name
        val inviter = userRepository.findById(invitation.inviterUserId)
        val inviterName = inviter?.name

        return SalonInvitationResponse(
            id = invitation.id.value,
            salonId = invitation.salonId.value,
            salonName = salonName,
            inviterUserId = invitation.inviterUserId.value,
            inviterName = inviterName,
            invitedUserPhone = invitation.invitedUserPhone,
            proposedRole = invitation.proposedRole.name,
            proposedClientDataPermission = invitation.proposedPermissions.clientDataAccess.name,
            proposedNickname = invitation.proposedNickname,
            status = invitation.status.name,
            message = invitation.message,
            invitedAt = invitation.invitedAt,
            respondedAt = invitation.respondedAt,
            expiresAt = invitation.expiresAt,
            isExpired = invitation.isExpired(),
            canBeResponded = invitation.canBeResponded(),
            resendCount = invitation.resendCount,
            lastResendAt = invitation.lastResendAt,
            cancelledAt = invitation.cancelledAt,
            cancelledBy = invitation.cancelledBy?.value,
            createdAt = invitation.createdAt,
            updatedAt = invitation.updatedAt,
        )
    }
}
