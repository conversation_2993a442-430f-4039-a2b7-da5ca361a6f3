package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CancelSubscriptionRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CreateSubscriptionRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.SalonSubscriptionDto
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.SubscriptionFeaturesDto
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.UpgradeSubscriptionRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ValidationResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.SalonSubscriptionDtoMapper
import ro.animaliaprogramari.animalia.application.usecase.*
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier
import java.math.BigDecimal

/**
 * REST controller for salon subscription management
 */
@RestController
@RequestMapping("/api/v1/salons/{salonId}/subscription")
@Tag(name = "Salon Subscriptions", description = "Salon subscription management endpoints")
class SalonSubscriptionController(
    private val subscriptionUseCase: SubscriptionManagementUseCase,
    private val mapper: SalonSubscriptionDtoMapper
) {

    @GetMapping
    @Operation(summary = "Get current subscription for salon")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun getCurrentSubscription(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<SalonSubscriptionDto> {
        val subscription = subscriptionUseCase.getCurrentSubscription(SalonId.of(salonId))
        return if (subscription != null) {
            ResponseEntity.ok(mapper.toDto(subscription))
        } else {
            ResponseEntity.notFound().build()
        }
    }

    @GetMapping("/features")
    @Operation(summary = "Get subscription features for salon")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun getSubscriptionFeatures(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<SubscriptionFeaturesDto> {
        val features = subscriptionUseCase.getSubscriptionFeatures(SalonId.of(salonId))
        return ResponseEntity.ok(mapper.toFeaturesDto(features))
    }

    @GetMapping("/history")
    @Operation(summary = "Get subscription history for salon")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun getSubscriptionHistory(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<List<SalonSubscriptionDto>> {
        val history = subscriptionUseCase.getSubscriptionHistory(SalonId.of(salonId))
        return ResponseEntity.ok(history.map { mapper.toDto(it) })
    }

    @PostMapping
    @Operation(summary = "Create new subscription for salon")
    @PreAuthorize("hasRole('ADMIN')")
    fun createSubscription(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @RequestBody request: CreateSubscriptionRequest
    ): ResponseEntity<SalonSubscriptionDto> {
        val command = CreateSubscriptionCommand(
            salonId = SalonId.of(salonId),
            tier = request.tier,
            monthlyPrice = request.monthlyPrice,
            billingCycle = request.billingCycle,
            trialDays = request.trialDays
        )

        val subscription = subscriptionUseCase.createSubscription(command)
        return ResponseEntity.status(HttpStatus.CREATED).body(mapper.toDto(subscription))
    }

    @PutMapping("/upgrade")
    @Operation(summary = "Upgrade subscription to higher tier")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun upgradeSubscription(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @RequestBody request: UpgradeSubscriptionRequest
    ): ResponseEntity<SalonSubscriptionDto> {
        val command = UpgradeSubscriptionCommand(
            salonId = SalonId.of(salonId),
            newTier = request.newTier,
            newPrice = request.newPrice
        )

        val subscription = subscriptionUseCase.upgradeSubscription(command)
        return ResponseEntity.ok(mapper.toDto(subscription))
    }

    @PutMapping("/cancel")
    @Operation(summary = "Cancel subscription")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun cancelSubscription(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @RequestBody request: CancelSubscriptionRequest
    ): ResponseEntity<SalonSubscriptionDto> {
        val command = CancelSubscriptionCommand(
            salonId = SalonId.of(salonId),
            reason = request.reason
        )

        val subscription = subscriptionUseCase.cancelSubscription(command)
        return ResponseEntity.ok(mapper.toDto(subscription))
    }

    @PutMapping("/reactivate")
    @Operation(summary = "Reactivate cancelled subscription")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun reactivateSubscription(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<SalonSubscriptionDto> {
        val subscription = subscriptionUseCase.reactivateSubscription(SalonId.of(salonId))
        return ResponseEntity.ok(mapper.toDto(subscription))
    }

    @GetMapping("/validation/sms-quota")
    @Operation(summary = "Check if SMS quota allows sending messages")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun checkSmsQuota(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Parameter(description = "Current SMS usage") @RequestParam currentUsage: Int
    ): ResponseEntity<ValidationResponse> {
        val isValid = subscriptionUseCase.checkSmsQuota(SalonId.of(salonId), currentUsage)
        return ResponseEntity.ok(ValidationResponse(isValid))
    }

    @GetMapping("/validation/team-invitation")
    @Operation(summary = "Check if subscription allows team member invitations")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun checkTeamInvitation(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Parameter(description = "Current team size") @RequestParam currentTeamSize: Int
    ): ResponseEntity<ValidationResponse> {
        val isValid = subscriptionUseCase.checkTeamInvitation(SalonId.of(salonId), currentTeamSize)
        return ResponseEntity.ok(ValidationResponse(isValid))
    }

    @GetMapping("/validation/salon-change")
    @Operation(summary = "Check if subscription allows salon changes")
    @PreAuthorize("hasRole('GROOMER') or hasRole('ADMIN')")
    fun checkSalonChange(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<ValidationResponse> {
        val isValid = subscriptionUseCase.checkSalonChange(SalonId.of(salonId))
        return ResponseEntity.ok(ValidationResponse(isValid))
    }
}
