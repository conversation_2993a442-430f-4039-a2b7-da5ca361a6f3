package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.UpdateSalonSubscriptionCommand
import ro.animaliaprogramari.animalia.application.port.inbound.SalonManagementUseCase
import ro.animaliaprogramari.animalia.application.query.GetSalonByIdQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

@RestController
@RequestMapping("/salons")
@CrossOrigin(origins = ["*"])
@Tag(name = "Salon Subscription", description = "Manage salon subscription plan")
class SalonSubscriptionController(
    private val salonManagementUseCase: SalonManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(SalonSubscriptionController::class.java)

    @GetMapping("/{salonId}/subscription")
    @Operation(summary = "Get salon subscription")
    fun getSubscription(@PathVariable salonId: String): ResponseEntity<ApiResponse<SalonSubscriptionResponse>> {
        logger.debug("REST request to get subscription for salon: $salonId")

        val currentUser = SecurityUtils.getCurrentUser()
            ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val salon = salonManagementUseCase.getSalonById(GetSalonByIdQuery(SalonId.of(salonId)))
            ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Salon not found"))

        if (!currentUser.isChiefGroomerInSalon(salon.id)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error("Nu aveți permisiunea"))
        }

        val plan = salon.subscriptionPlan
        val response = SalonSubscriptionResponse(
            salonId = salon.id.value,
            plan = plan.name,
            canChangeSalon = plan.canChangeSalon,
            maxSmsReminders = plan.maxSmsReminders,
            canAddTeamMembers = plan.canAddTeamMembers,
        )

        return ResponseEntity.ok(ApiResponse.success(response))
    }

    @PutMapping("/{salonId}/subscription")
    @Operation(summary = "Update salon subscription")
    fun updateSubscription(
        @PathVariable salonId: String,
        @RequestBody request: UpdateSalonSubscriptionRequest,
    ): ResponseEntity<ApiResponse<SalonSubscriptionResponse>> {
        logger.debug("REST request to update subscription for salon: $salonId to ${'$'}{request.plan}")

        val currentUser = SecurityUtils.getCurrentUser()
            ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val plan = try {
            SalonSubscriptionPlan.fromString(request.plan)
        } catch (e: IllegalArgumentException) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Invalid plan"))
        }

        val command = UpdateSalonSubscriptionCommand(
            salonId = SalonId.of(salonId),
            plan = plan,
            updaterUserId = currentUser.userId,
        )

        val updated = salonManagementUseCase.updateSalonSubscription(command)
        val resp = SalonSubscriptionResponse(
            salonId = updated.id.value,
            plan = updated.subscriptionPlan.name,
            canChangeSalon = updated.subscriptionPlan.canChangeSalon,
            maxSmsReminders = updated.subscriptionPlan.maxSmsReminders,
            canAddTeamMembers = updated.subscriptionPlan.canAddTeamMembers,
        )
        return ResponseEntity.ok(ApiResponse.success(resp))
    }
}
