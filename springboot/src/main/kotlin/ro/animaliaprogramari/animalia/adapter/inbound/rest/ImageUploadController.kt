package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ImageUploadResponse
import ro.animaliaprogramari.animalia.adapter.outbound.storage.CloudinaryService
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

@RestController
@RequestMapping("/images")
@CrossOrigin(origins = ["*"])
@Tag(name = "Image Upload", description = "Operations for uploading images")
class ImageUploadController(
    private val cloudinaryService: CloudinaryService,
) {
    private val logger = LoggerFactory.getLogger(ImageUploadController::class.java)

    @PostMapping("/upload")
    @Operation(summary = "Upload image", description = "Upload an image to cloud storage")
    @SwaggerApiResponse(responseCode = "200", description = "Image uploaded successfully")
    @SwaggerApiResponse(responseCode = "500", description = AnimaliaConstants.INTERNAL_SERVER_ERROR)
    fun uploadImage(
        @RequestParam("file") file: MultipartFile,
    ): ResponseEntity<ApiResponse<ImageUploadResponse>> {
        logger.info("🚀 REST request to upload image")
        logger.info("📁 File name: ${file.originalFilename}")
        logger.info("📊 File size: ${file.size} bytes")
        logger.info("📋 Content type: ${file.contentType}")
        logger.info("🔍 Is empty: ${file.isEmpty}")

        return try {
            logger.info("📤 Uploading to Cloudinary...")
            val url = cloudinaryService.uploadImage(file)
            logger.info("✅ Image uploaded successfully to: $url")
            ResponseEntity.ok(ApiResponse.success(ImageUploadResponse(url)))
        } catch (e: Exception) {
            logger.error("❌ Error uploading image: ${e.message}", e)
            logger.error("💥 Exception type: ${e.javaClass.simpleName}")
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("${AnimaliaConstants.INTERNAL_SERVER_ERROR}: ${e.message}"))
        }
    }
}
