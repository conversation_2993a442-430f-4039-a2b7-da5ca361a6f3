package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import jakarta.validation.constraints.NotBlank
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Subscription details for a salon")
data class SalonSubscriptionResponse(
    val salonId: String,
    val plan: String,
    val canChangeSalon: Boolean,
    val maxSmsReminders: Int,
    val canAddTeamMembers: Boolean,
)

@Schema(description = "Request to update salon subscription")
data class UpdateSalonSubscriptionRequest(
    @field:NotBlank
    val plan: String,
)
