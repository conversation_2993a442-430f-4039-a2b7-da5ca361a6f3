package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import ro.animaliaprogramari.animalia.domain.model.BillingCycle
import ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * DTO for salon subscription
 */
@Schema(description = "Salon subscription information")
data class SalonSubscriptionDto(
    @Schema(description = "Subscription ID", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String,
    
    @Schema(description = "Salon ID", example = "123e4567-e89b-12d3-a456-426614174000")
    val salonId: String,
    
    @Schema(description = "Subscription tier", example = "TEAM")
    val tier: SubscriptionTier,
    
    @Schema(description = "Subscription status", example = "ACTIVE")
    val status: SubscriptionStatus,
    
    @Schema(description = "Subscription start date")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val startDate: LocalDateTime,
    
    @Schema(description = "Subscription end date (null for ongoing)")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val endDate: LocalDateTime?,
    
    @Schema(description = "Whether subscription is active", example = "true")
    val isActive: Boolean,
    
    @Schema(description = "Whether subscription auto-renews", example = "true")
    val autoRenew: Boolean,
    
    @Schema(description = "Monthly price", example = "29.99")
    val monthlyPrice: BigDecimal,
    
    @Schema(description = "Billing cycle", example = "MONTHLY")
    val billingCycle: BillingCycle,
    
    @Schema(description = "Creation timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val createdAt: LocalDateTime,
    
    @Schema(description = "Last update timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val updatedAt: LocalDateTime,
    
    @Schema(description = "Last billing date")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val lastBillingDate: LocalDateTime?,
    
    @Schema(description = "Next billing date")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val nextBillingDate: LocalDateTime?,
    
    @Schema(description = "Trial end date")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val trialEndDate: LocalDateTime?,
    
    @Schema(description = "Cancellation timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val cancelledAt: LocalDateTime?,
    
    @Schema(description = "Cancellation reason")
    val cancellationReason: String?
)

/**
 * DTO for subscription features
 */
@Schema(description = "Subscription features and limits")
data class SubscriptionFeaturesDto(
    @Schema(description = "Subscription tier", example = "TEAM")
    val tier: SubscriptionTier,
    
    @Schema(description = "Monthly SMS quota", example = "250")
    val smsQuota: Int,
    
    @Schema(description = "Can invite team members", example = "true")
    val canInviteTeamMembers: Boolean,
    
    @Schema(description = "Can change salon", example = "false")
    val canChangeSalon: Boolean,
    
    @Schema(description = "Maximum team members (null for unlimited)", example = "null")
    val maxTeamMembers: Int?,
    
    @Schema(description = "Feature description")
    val description: String
)

/**
 * Request DTO for creating subscription
 */
@Schema(description = "Request to create new subscription")
data class CreateSubscriptionRequest(
    @Schema(description = "Subscription tier", example = "TEAM", required = true)
    val tier: SubscriptionTier,
    
    @Schema(description = "Monthly price", example = "29.99", required = true)
    val monthlyPrice: BigDecimal,
    
    @Schema(description = "Billing cycle", example = "MONTHLY")
    val billingCycle: BillingCycle = BillingCycle.MONTHLY,
    
    @Schema(description = "Trial period in days", example = "14")
    val trialDays: Int? = null
)

/**
 * Request DTO for upgrading subscription
 */
@Schema(description = "Request to upgrade subscription")
data class UpgradeSubscriptionRequest(
    @Schema(description = "New subscription tier", example = "ENTERPRISE", required = true)
    val newTier: SubscriptionTier,
    
    @Schema(description = "New monthly price", example = "99.99", required = true)
    val newPrice: BigDecimal
)

/**
 * Request DTO for cancelling subscription
 */
@Schema(description = "Request to cancel subscription")
data class CancelSubscriptionRequest(
    @Schema(description = "Cancellation reason", example = "No longer needed")
    val reason: String? = null
)

/**
 * Response DTO for validation checks
 */
@Schema(description = "Validation response")
data class ValidationResponse(
    @Schema(description = "Whether the operation is allowed", example = "true")
    val isValid: Boolean,
    
    @Schema(description = "Error message if validation failed")
    val message: String? = null
)
