package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.*
import java.time.LocalDateTime

/**
 * Response DTO for salon services
 */
@Schema(description = "Details about a salon service")
data class ServiceResponse(
    @JsonProperty("id")
    val id: String,
    @JsonProperty("name")
    val name: String,
    @JsonProperty("description")
    val description: String?,
    @JsonProperty("price")
    val price: Double,
    @JsonProperty("duration")
    val duration: Int,
    @JsonProperty("category")
    val category: String,
    @JsonProperty("isActive")
    val isActive: Boolean,
    @JsonProperty("displayOrder")
    val displayOrder: Int,
    @JsonProperty("requirements")
    val requirements: List<String>,
    @JsonProperty("formattedPrice")
    val formattedPrice: String,
    @JsonProperty("formattedDuration")
    val formattedDuration: String,
    // Variable pricing fields
    @JsonProperty("sizePrices")
    val sizePrices: Map<String, Double>?,
    @JsonProperty("sizeDurations")
    val sizeDurations: Map<String, Int>?,
    @JsonProperty("minPrice")
    val minPrice: Double?,
    @JsonProperty("maxPrice")
    val maxPrice: Double?,
    @JsonProperty("hasVariablePricing")
    val hasVariablePricing: Boolean,
    @JsonProperty("hasMinMaxPricing")
    val hasMinMaxPricing: Boolean,
    @JsonProperty("sizeMinPrices")
    val sizeMinPrices: Map<String, Double>?,
    @JsonProperty("sizeMaxPrices")
    val sizeMaxPrices: Map<String, Double>?,
    @JsonProperty("hasSizeBasedMinMaxPricing")
    val hasSizeBasedMinMaxPricing: Boolean,
    @JsonProperty("createdAt")
    val createdAt: LocalDateTime,
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime,
)

/**
 * Request DTO for creating services
 */
@Schema(description = "Payload for creating a new salon service")
data class CreateServiceRequest(
    @field:NotBlank(message = "Service name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @JsonProperty("name")
    val name: String,
    @field:Size(max = 1000, message = "Description must not exceed 1000 characters")
    @JsonProperty("description")
    val description: String? = null,
    @field:NotNull(message = "Price is required")
    @field:DecimalMin(value = "0.01", message = "Price must be greater than 0")
    @JsonProperty("price")
    val price: Double,
    @field:NotNull(message = "Duration is required")
    @field:Min(value = 1, message = "Duration must be at least 1 minute")
    @field:Max(value = 480, message = "Duration cannot exceed 8 hours")
    @JsonProperty("duration")
    val duration: Int,
    @field:NotBlank(message = "Category is required")
    @JsonProperty("category")
    val category: String,
    @field:Min(value = 0, message = "Display order cannot be negative")
    @JsonProperty("displayOrder")
    val displayOrder: Int = 0,
    @JsonProperty("requirements")
    val requirements: List<String> = emptyList(),
    // Variable pricing fields
    @JsonProperty("sizePrices")
    val sizePrices: Map<String, Double>? = null,
    @JsonProperty("sizeDurations")
    val sizeDurations: Map<String, Int>? = null,
    @field:DecimalMin(value = "0.01", message = "Min price must be greater than 0")
    @JsonProperty("minPrice")
    val minPrice: Double? = null,
    @field:DecimalMin(value = "0.01", message = "Max price must be greater than 0")
    @JsonProperty("maxPrice")
    val maxPrice: Double? = null,
    @JsonProperty("sizeMinPrices")
    val sizeMinPrices: Map<String, Double>? = null,
    @JsonProperty("sizeMaxPrices")
    val sizeMaxPrices: Map<String, Double>? = null,
)

/**
 * Request DTO for updating services
 */
data class UpdateServiceRequest(
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @JsonProperty("name")
    val name: String? = null,
    @field:Size(max = 1000, message = "Description must not exceed 1000 characters")
    @JsonProperty("description")
    val description: String? = null,
    @field:DecimalMin(value = "0.01", message = "Price must be greater than 0")
    @JsonProperty("price")
    val price: Double? = null,
    @field:Min(value = 1, message = "Duration must be at least 1 minute")
    @field:Max(value = 480, message = "Duration cannot exceed 8 hours")
    @JsonProperty("duration")
    val duration: Int? = null,
    @JsonProperty("category")
    val category: String? = null,
    @field:Min(value = 0, message = "Display order cannot be negative")
    @JsonProperty("displayOrder")
    val displayOrder: Int? = null,
    @JsonProperty("requirements")
    val requirements: List<String>? = null,
    // Variable pricing fields
    @JsonProperty("sizePrices")
    val sizePrices: Map<String, Double>? = null,
    @JsonProperty("sizeDurations")
    val sizeDurations: Map<String, Int>? = null,
    @field:DecimalMin(value = "0.01", message = "Min price must be greater than 0")
    @JsonProperty("minPrice")
    val minPrice: Double? = null,
    @field:DecimalMin(value = "0.01", message = "Max price must be greater than 0")
    @JsonProperty("maxPrice")
    val maxPrice: Double? = null,
    @JsonProperty("sizeMinPrices")
    val sizeMinPrices: Map<String, Double>? = null,
    @JsonProperty("sizeMaxPrices")
    val sizeMaxPrices: Map<String, Double>? = null,
)

/**
 * Response DTO for service lists with pagination info
 */
data class ServiceListResponse(
    @JsonProperty("services")
    val services: List<ServiceResponse>,
    @JsonProperty("totalCount")
    val totalCount: Int,
    @JsonProperty("activeCount")
    val activeCount: Int,
    @JsonProperty("inactiveCount")
    val inactiveCount: Int,
)
