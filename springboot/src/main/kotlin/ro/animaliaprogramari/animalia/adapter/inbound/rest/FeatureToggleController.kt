package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

@RestController
@RequestMapping("/feature-toggles")
@Tag(name = "Feature Toggles", description = "Operations for retrieving available feature toggles")
class FeatureToggleController {
    @GetMapping
    @Operation(summary = "List feature toggles", description = "Retrieve available feature flags")
    @SwaggerApiResponse(responseCode = "200", description = "Feature flags retrieved successfully")
    fun getFeatureToggles(): ApiResponse<Map<String, Any>> {
        val features =
            mapOf(
                "monthly_view_enabled" to false,
                "theme_selection_enabled" to true,
                "translations_enabled" to false,
                "appointment_confirmations" to true,
                "day_before_reminders" to true,
                "sms_notifications" to false,
                "push_notifications" to true,
                "google_places_autocomplete" to true,
                "phone_verification_required" to false,
                "multi_salon_support" to true,
            )

        return ApiResponse.success(features)
    }
}
