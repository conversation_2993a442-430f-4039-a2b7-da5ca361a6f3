package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern

/**
 * Request DTO for confirming phone number with verification code
 */
@Schema(description = "Request to confirm phone number with verification code")
data class ConfirmPhoneRequest(
    @field:NotBlank(message = "Phone number is required")
    @Schema(
        description = "Phone number in Romanian format (supports spaces, dashes, dots: +40 XXX XXX XXX, +40-XXX-XXX-XXX, +40.XXX.XXX.XXX, or +40XXXXXXXXX)",
        example = "+40 712 345 678"
    )
    val phoneNumber: String,

    @field:NotBlank(message = "Confirmation code is required")
    @field:Pattern(
        regexp = "^[0-9]{6}$",
        message = "Confirmation code must be 6 digits"
    )
    @Schema(
        description = "6-digit verification code received via SMS",
        example = "123456",
        pattern = "^[0-9]{6}$"
    )
    val confirmationCode: String
)

/**
 * Response DTO for phone confirmation
 */
@Schema(description = "Response for phone confirmation")
data class ConfirmPhoneResponse(
    @Schema(description = "Whether the confirmation was successful")
    val success: Boolean,
    
    @Schema(description = "Response message")
    val message: String,
    
    @Schema(description = "Confirmed phone number")
    val phoneNumber: String? = null,
    
    @Schema(description = "Whether the phone number is verified")
    val verified: Boolean = false
)
