package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * JPA entity for notifications
 * This is an outbound adapter for persistence
 */
@Entity
@Table(name = "notifications")
class Notification {
    @Id
    @Column(name = "id", nullable = false)
    var id: String = ""

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    var type: NotificationType = NotificationType.PUSH

    @Column(name = "recipient_type", nullable = false)
    @Enumerated(EnumType.STRING)
    var recipientType: RecipientType = RecipientType.USER

    @Column(name = "recipient_value", nullable = false)
    var recipientValue: String = ""

    @Column(name = "content_type", nullable = false)
    @Enumerated(EnumType.STRING)
    var contentType: ContentType = ContentType.PUSH

    @Column(name = "title")
    var title: String? = null

    @Column(name = "body", nullable = false, columnDefinition = "TEXT")
    var body: String = ""

    @Column(name = "data", columnDefinition = "TEXT")
    var data: String? = null

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    var status: NotificationStatus = NotificationStatus.PENDING

    @Column(name = "appointment_id")
    var appointmentId: String? = null

    @Column(name = "salon_id", nullable = false)
    var salonId: String = ""

    @Column(name = "sent_at")
    var sentAt: LocalDateTime? = null

    @Column(name = "delivered_at")
    var deliveredAt: LocalDateTime? = null

    @Column(name = "failure_reason")
    var failureReason: String? = null

    @Column(name = "retry_count", nullable = false)
    var retryCount: Int = 0

    @Column(name = "max_retries", nullable = false)
    var maxRetries: Int = 3

    @Column(name = "is_read", nullable = false)
    var isRead: Boolean = false

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()

    // Default constructor for JPA
    constructor()

    /**
     * Constructor with all parameters for use in mappers
     */
    constructor(
        id: String,
        type: NotificationType,
        recipientType: RecipientType,
        recipientValue: String,
        contentType: ContentType,
        title: String?,
        body: String,
        data: String?,
        status: NotificationStatus,
        appointmentId: String?,
        salonId: String,
        sentAt: LocalDateTime?,
        deliveredAt: LocalDateTime?,
        failureReason: String?,
        retryCount: Int,
        maxRetries: Int,
        isRead: Boolean,
        createdAt: LocalDateTime,
        updatedAt: LocalDateTime,
    ) {
        this.id = id
        this.type = type
        this.recipientType = recipientType
        this.recipientValue = recipientValue
        this.contentType = contentType
        this.title = title
        this.body = body
        this.data = data
        this.status = status
        this.appointmentId = appointmentId
        this.salonId = salonId
        this.sentAt = sentAt
        this.deliveredAt = deliveredAt
        this.failureReason = failureReason
        this.retryCount = retryCount
        this.maxRetries = maxRetries
        this.isRead = isRead
        this.createdAt = createdAt
        this.updatedAt = updatedAt
    }
}

/**
 * Notification types for JPA entity
 */
enum class NotificationType {
    PUSH
}

/**
 * Notification status for JPA entity
 */
enum class NotificationStatus {
    PENDING,
    SENT,
    DELIVERED,
    FAILED,
    CANCELLED
}

/**
 * Recipient types for JPA entity
 */
enum class RecipientType {
    USER
}

/**
 * Content types for JPA entity
 */
enum class ContentType {
    PUSH
}
