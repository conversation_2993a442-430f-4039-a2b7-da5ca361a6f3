package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.SalonSubscriptionEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * JPA implementation of SalonSubscriptionRepository
 */
@Repository
class JpaSalonSubscriptionRepository(
    private val springRepository: SpringSalonSubscriptionRepository,
    private val mapper: SalonSubscriptionEntityMapper
) : SalonSubscriptionRepository {

    override fun save(subscription: SalonSubscription): SalonSubscription {
        val entity = mapper.toEntity(subscription)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findById(id: SalonSubscriptionId): SalonSubscription? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findActiveBySalonId(salonId: SalonId): SalonSubscription? {
        return springRepository.findActiveBySalonId(salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun findAllBySalonId(salonId: SalonId): List<SalonSubscription> {
        return springRepository.findAllBySalonIdOrderByCreatedAtDesc(salonId.value)
            .let { mapper.toDomainList(it) }
    }

    override fun findByStatus(status: SubscriptionStatus): List<SalonSubscription> {
        return springRepository.findByStatusOrderByCreatedAtDesc(status)
            .let { mapper.toDomainList(it) }
    }

    override fun findByTier(tier: SubscriptionTier): List<SalonSubscription> {
        return springRepository.findByTierOrderByCreatedAtDesc(tier)
            .let { mapper.toDomainList(it) }
    }

    override fun findSubscriptionsForBilling(beforeDate: LocalDateTime): List<SalonSubscription> {
        return springRepository.findSubscriptionsForBilling(beforeDate)
            .let { mapper.toDomainList(it) }
    }

    override fun findExpiredSubscriptions(): List<SalonSubscription> {
        return springRepository.findExpiredSubscriptions()
            .let { mapper.toDomainList(it) }
    }

    override fun findTrialEndingSubscriptions(beforeDate: LocalDateTime): List<SalonSubscription> {
        return springRepository.findTrialEndingSubscriptions(beforeDate)
            .let { mapper.toDomainList(it) }
    }

    override fun hasActiveSubscription(salonId: SalonId): Boolean {
        return springRepository.hasActiveSubscription(salonId.value)
    }

    override fun deleteById(id: SalonSubscriptionId) {
        springRepository.deleteById(id.value)
    }

    override fun countByTier(tier: SubscriptionTier): Long {
        return springRepository.countByTier(tier)
    }

    override fun countActiveSubscriptions(): Long {
        return springRepository.countActiveSubscriptions()
    }
}
