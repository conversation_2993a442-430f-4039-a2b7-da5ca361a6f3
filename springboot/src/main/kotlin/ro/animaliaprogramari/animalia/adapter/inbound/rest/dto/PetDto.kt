package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.PositiveOrZero
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * DTO for pet creation request
 * Updated to match Flutter app structure
 */
@Schema(description = "Request for adding a pet")
data class AddPetRequest(
    @JsonProperty("id")
    val id: String? = null, // Pet ID (empty for new pets)
    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @JsonProperty("name")
    val name: String,
    @JsonProperty("species")
    val species: String? = null, // e.g., "dog", "cat"
    @field:Size(max = 255, message = "Breed must not exceed 255 characters")
    @JsonProperty("breed")
    val breed: String? = null,
    @JsonProperty("gender")
    val gender: String? = null,
    @JsonProperty("birthDate")
    val birthDate: LocalDateTime? = null,
    @field:PositiveOrZero(message = "Weight must be positive")
    @JsonProperty("weight")
    val weight: Double? = null,
    @field:Size(max = 100, message = "Color must not exceed 100 characters")
    @JsonProperty("color")
    val color: String? = null,
    @JsonProperty("ownerId")
    val ownerId: String? = null, // Client ID from Flutter
    @JsonProperty("microchipNumber")
    val microchipNumber: String? = null,
    @JsonProperty("vaccinations")
    val vaccinations: List<String>? = null,
    @JsonProperty("notes")
    val notes: String? = null,
    @JsonProperty("photoUrl")
    val photoUrl: String? = null,
    // Legacy fields for backward compatibility
    val clientId: String? = null,
    val age: Int? = null,
    val medicalConditions: String? = null,
) {
    /**
     * Get the effective client ID (from ownerId or clientId)
     */
    fun getEffectiveClientId(): String? = ownerId ?: clientId

    /**
     * Calculate age from birth date
     */
    fun getCalculatedAge(): Int? {
        return birthDate?.let { birth ->
            val now = LocalDateTime.now()
            val years = now.year - birth.year
            if (now.monthValue < birth.monthValue ||
                (now.monthValue == birth.monthValue && now.dayOfMonth < birth.dayOfMonth)
            ) {
                years - 1
            } else {
                years
            }
        } ?: age
    }

    /**
     * Convert weight to BigDecimal
     */
    fun getWeightAsBigDecimal(): BigDecimal? = weight?.let { BigDecimal.valueOf(it) }
}

/**
 * DTO for pet creation request (for validation)
 */
data class CreatePetRequest(
    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    val name: String,
    @field:Size(max = 255, message = "Breed must not exceed 255 characters")
    val breed: String? = null,
    @field:PositiveOrZero(message = "Age must be positive")
    val age: Int? = null,
    @field:PositiveOrZero(message = "Weight must be positive")
    val weight: BigDecimal? = null,
    @field:Size(max = 100, message = "Color must not exceed 100 characters")
    val color: String? = null,
    @field:Size(max = 2000, message = "Notes must not exceed 2000 characters")
    val notes: String? = null,
    @field:Size(max = 2000, message = "Medical conditions must not exceed 2000 characters")
    val medicalConditions: String? = null,
    @field:Size(max = 500, message = "Photo URL must not exceed 500 characters")
    val photoUrl: String? = null,
)

/**
 * DTO for pet update request
 */
data class UpdatePetRequest(
    @field:NotBlank(message = "Name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    val name: String,
    @field:Size(max = 255, message = "Breed must not exceed 255 characters")
    val breed: String? = null,
    @field:PositiveOrZero(message = "Age must be positive")
    val age: Int? = null,
    @field:PositiveOrZero(message = "Weight must be positive")
    val weight: BigDecimal? = null,
    @field:Size(max = 100, message = "Color must not exceed 100 characters")
    val color: String? = null,
    val gender: String? = null,
    val notes: String? = null,
    val medicalConditions: String? = null,
    @field:Size(max = 500, message = "Photo URL must not exceed 500 characters")
    val photoUrl: String? = null,
)

/**
 * DTO for pet response
 * Updated to include Flutter app fields
 */
@Schema(description = "Pet response details")
data class PetResponse(
    @JsonProperty("id")
    val id: String,
    @JsonProperty("clientId")
    val clientId: String,
    @JsonProperty("ownerId") // Alias for clientId for Flutter compatibility
    val ownerId: String,
    @JsonProperty("name")
    val name: String,
    @JsonProperty("species")
    val species: String? = null,
    @JsonProperty("breed")
    val breed: String?,
    @JsonProperty("gender")
    val gender: String?,
    @JsonProperty("birthDate")
    val birthDate: LocalDateTime? = null,
    @JsonProperty("age")
    val age: Int?,
    @JsonProperty("weight")
    val weight: Double?,
    @JsonProperty("color")
    val color: String?,
    @JsonProperty("microchipNumber")
    val microchipNumber: String? = null,
    @JsonProperty("vaccinations")
    val vaccinations: List<String> = emptyList(),
    @JsonProperty("notes")
    val notes: String?,
    @JsonProperty("photoUrl")
    val photoUrl: String? = null,
    @JsonProperty("medicalConditions")
    val medicalConditions: String?,
    @JsonProperty("isActive")
    val isActive: Boolean,
    @JsonProperty("createdAt")
    val createdAt: LocalDateTime,
    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime,
    @JsonProperty("isSenior")
    val isSenior: Boolean,
    @JsonProperty("hasMedicalConditions")
    val hasMedicalConditions: Boolean,
)
