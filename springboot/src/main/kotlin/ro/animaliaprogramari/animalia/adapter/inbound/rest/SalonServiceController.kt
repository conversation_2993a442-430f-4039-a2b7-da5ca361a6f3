package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.ServiceDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.SalonServiceManagementUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for salon services management
 * This is an inbound adapter that translates HTTP requests to use case calls
 *
 * Provides complete CRUD operations for salon-scoped services:
 * - GET /api/salons/{salonId}/services - List all services for a salon
 * - POST /api/salons/{salonId}/services - Create new service
 * - PUT /api/salons/{salonId}/services/{serviceId} - Update existing service
 * - DELETE /api/salons/{salonId}/services/{serviceId} - Deactivate service
 * - PUT /api/salons/{salonId}/services/{serviceId}/activate - Activate service
 * - PATCH /api/salons/{salonId}/services/{serviceId}/toggle-status - Toggle active/inactive
 * - POST /api/salons/{salonId}/services/{serviceId}/duplicate - Duplicate service
 *
 * All endpoints enforce chief groomer authorization for the specific salon
 */
@RestController
@RequestMapping("/salons")
@CrossOrigin(origins = ["*"])
@Tag(name = "Salon Services", description = "Manage services offered by a salon")
class SalonServiceController(
    private val salonServiceManagementUseCase: SalonServiceManagementUseCase,
    private val serviceDtoMapper: ServiceDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(SalonServiceController::class.java)

    /**
     * GET /api/salons/{salonId}/services
     * List all services for a salon with optional filtering
     */
    @GetMapping("/{salonId}/services")
    @Operation(summary = "List services", description = "Retrieve services offered by a salon")
    @SwaggerApiResponse(responseCode = "200", description = "Services retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun getSalonServices(
        @PathVariable salonId: String,
        @RequestParam(required = false, defaultValue = "false") activeOnly: Boolean,
        @RequestParam(required = false) search: String?,
        @RequestParam(required = false) category: String?,
    ): ResponseEntity<ApiResponse<ServiceListResponse>> {
        logger.debug("REST request to get services for salon: $salonId")

        val currentUser =
            SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val salon = SalonId.of(salonId)

        // Authorization: Only chief groomers can manage services ?? why??
//        if (!currentUser.isChiefGroomerInSalon(salon)) {
//            return ResponseEntity.status(HttpStatus.FORBIDDEN)
//                .body(ApiResponse.error("Nu aveți permisiunea să gestionați serviciile acestui salon"))
//        }

        // Parse category if provided
        val serviceCategory =
            category?.let {
                try {
                    ServiceCategory.valueOf(it.uppercase())
                } catch (e: IllegalArgumentException) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Categoria de serviciu nu este validă: $it"))
                }
            }

        // Get services from use case
        val services =
            salonServiceManagementUseCase.getServicesBySalon(
                salonId = salon,
                activeOnly = activeOnly,
                search = search,
                category = serviceCategory,
            )

        val response = serviceDtoMapper.toListResponse(services)
        return ResponseEntity.ok(ApiResponse.success(response))
    }

    /**
     * POST /api/salons/{salonId}/services
     * Create a new service for the salon
     */
    @PostMapping("/{salonId}/services")
    fun createSalonService(
        @PathVariable salonId: String,
        @Valid @RequestBody request: CreateServiceRequest,
    ): ResponseEntity<ApiResponse<ServiceResponse>> {
        logger.debug("REST request to create service for salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can create services
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să creați servicii în acest salon"))
            }

            // Validate category
            val serviceCategory =
                try {
                    ServiceCategory.valueOf(request.category.uppercase())
                } catch (e: IllegalArgumentException) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Categoria de serviciu nu este validă: ${request.category}"))
                }

            // Create service using use case
            val params = serviceDtoMapper.fromCreateRequest(request, salon)
            val createdService =
                salonServiceManagementUseCase.createService(
                    salonId = params.salonId,
                    name = params.name,
                    description = params.description,
                    price = params.price,
                    duration = params.duration,
                    category = params.category,
                    displayOrder = params.displayOrder,
                    requirements = params.requirements,
                    sizePrices = params.sizePrices,
                    sizeDurations = params.sizeDurations,
                    minPrice = params.minPrice,
                    maxPrice = params.maxPrice,
                    sizeMinPrices = params.sizeMinPrices,
                    sizeMaxPrices = params.sizeMaxPrices,
                )

            val response = serviceDtoMapper.toResponse(createdService)
            ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Validation error creating service: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Datele introduse nu sunt valide"))
        } catch (e: Exception) {
            logger.error("Error creating salon service", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la crearea serviciului"))
        }
    }

    /**
     * PUT /api/salons/{salonId}/services/{serviceId}
     * Update an existing service
     */
    @PutMapping("/{salonId}/services/{serviceId}")
    fun updateSalonService(
        @PathVariable salonId: String,
        @PathVariable serviceId: String,
        @Valid @RequestBody request: UpdateServiceRequest,
    ): ResponseEntity<ApiResponse<ServiceResponse>> {
        logger.debug("REST request to update service $serviceId in salon $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can update services
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați serviciile acestui salon"))
            }

            // Validate category if provided
            val serviceCategory =
                request.category?.let {
                    try {
                        ServiceCategory.valueOf(it.uppercase())
                    } catch (e: IllegalArgumentException) {
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponse.error("Categoria de serviciu nu este validă: $it"))
                    }
                }

            // Update service using use case
            val params = serviceDtoMapper.fromUpdateRequest(request)
            val updatedService =
                salonServiceManagementUseCase.updateService(
                    serviceId = ServiceId.of(serviceId),
                    salonId = salon,
                    name = params.name,
                    description = params.description,
                    price = params.price,
                    duration = params.duration,
                    category = serviceCategory,
                    displayOrder = params.displayOrder,
                    requirements = params.requirements,
                    sizePrices = params.sizePrices,
                    sizeDurations = params.sizeDurations,
                    minPrice = params.minPrice,
                    maxPrice = params.maxPrice,
                    sizeMinPrices = params.sizeMinPrices,
                    sizeMaxPrices = params.sizeMaxPrices,
                )

            val response = serviceDtoMapper.toResponse(updatedService)
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Validation error updating service: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Datele introduse nu sunt valide"))
        } catch (e: Exception) {
            logger.error("Error updating salon service", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la actualizarea serviciului"))
        }
    }

    /**
     * POST /api/salons/{salonId}/services/{serviceId}
     * Deactivate a service (soft delete)
     */
    @PostMapping("/{salonId}/services/{serviceId}")
    fun deactivateSalonService(
        @PathVariable salonId: String,
        @PathVariable serviceId: String
    ): ResponseEntity<ApiResponse<String>> {
        logger.debug("REST request to deactivate service $serviceId in salon $salonId")
        val currentUser = SecurityUtils.getCurrentUser()
            ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val salon = SalonId.of(salonId)

        // Authorization: Only chief groomers can deactivate services
        if (!currentUser.isChiefGroomerInSalon(salon)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error("Nu aveți permisiunea să eliminați servicii din acest salon"))
        }

        // Deactivate service using use case
        salonServiceManagementUseCase.deactivateService(
            serviceId = ServiceId.of(serviceId),
            salonId = salon
        )

        return ResponseEntity.ok(ApiResponse.success("Serviciul a fost dezactivat cu succes"))
    }


    /**
     * DELETE /api/salons/{salonId}/services/{serviceId}
     * delete a service (hard delete)
     */
    @DeleteMapping("/{salonId}/services/{serviceId}")
    fun deleteSalonService(
        @PathVariable salonId: String,
        @PathVariable serviceId: String
    ): ResponseEntity<ApiResponse<String>> {
        logger.debug("REST request to deactivate service $serviceId in salon $salonId")
        val currentUser = SecurityUtils.getCurrentUser()
            ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

        val salon = SalonId.of(salonId)

        // Authorization: Only chief groomers can deactivate services
        if (!currentUser.isChiefGroomerInSalon(salon)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error("Nu aveți permisiunea să eliminați servicii din acest salon"))
        }

        // Deactivate service using use case
        salonServiceManagementUseCase.deleteService(
            serviceId = ServiceId.of(serviceId),
            salonId = salon
        )

        return ResponseEntity.ok(ApiResponse.success("Serviciul a fost dezactivat cu succes"))
    }

    /**
     * PUT /api/salons/{salonId}/services/{serviceId}/activate
     * Activate a previously deactivated service
     */
    @PutMapping("/{salonId}/services/{serviceId}/activate")
    fun activateSalonService(
        @PathVariable salonId: String,
        @PathVariable serviceId: String,
    ): ResponseEntity<ApiResponse<ServiceResponse>> {
        logger.debug("REST request to activate service $serviceId in salon $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can activate services
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să activați servicii în acest salon"))
            }

            // Activate service using use case
            val activatedService =
                salonServiceManagementUseCase.activateService(
                    serviceId = ServiceId.of(serviceId),
                    salonId = salon,
                )

            val response = serviceDtoMapper.toResponse(activatedService)
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Validation error activating service: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT))
        } catch (e: Exception) {
            logger.error("Error activating salon service", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la activarea serviciului"))
        }
    }

    /**
     * PATCH /api/salons/{salonId}/services/{serviceId}/toggle-status
     * Toggle service status (active/inactive)
     */
    @PatchMapping("/{salonId}/services/{serviceId}/toggle-status")
    fun toggleServiceStatus(
        @PathVariable salonId: String,
        @PathVariable serviceId: String,
    ): ResponseEntity<ApiResponse<ServiceResponse>> {
        logger.debug("REST request to toggle status for service $serviceId in salon $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can toggle service status
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați statusul serviciilor în acest salon"))
            }

            // Toggle service status using use case
            val toggledService =
                salonServiceManagementUseCase.toggleServiceStatus(
                    serviceId = ServiceId.of(serviceId),
                    salonId = salon,
                )

            val response = serviceDtoMapper.toResponse(toggledService)
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Validation error toggling service status: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT))
        } catch (e: Exception) {
            logger.error("Error toggling service status", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la modificarea statusului serviciului"))
        }
    }

    /**
     * POST /api/salons/{salonId}/services/{serviceId}/duplicate
     * Duplicate an existing service
     */
    @PostMapping("/{salonId}/services/{serviceId}/duplicate")
    fun duplicateService(
        @PathVariable salonId: String,
        @PathVariable serviceId: String,
    ): ResponseEntity<ApiResponse<ServiceResponse>> {
        logger.debug("REST request to duplicate service $serviceId in salon $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can duplicate services
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să duplicați servicii în acest salon"))
            }

            // Duplicate service using use case
            val duplicatedService =
                salonServiceManagementUseCase.duplicateService(
                    serviceId = ServiceId.of(serviceId),
                    salonId = salon,
                )

            val response = serviceDtoMapper.toResponse(duplicatedService)
            ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Validation error duplicating service: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT))
        } catch (e: Exception) {
            logger.error("Error duplicating service", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la duplicarea serviciului"))
        }
    }
}
