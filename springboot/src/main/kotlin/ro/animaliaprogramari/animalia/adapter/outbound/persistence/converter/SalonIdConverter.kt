package ro.animaliaprogramari.animalia.adapter.outbound.persistence.converter

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * JPA converter for SalonId value object
 * Converts between SalonId and its String representation in the database
 */
@Converter(autoApply = true)
class SalonIdConverter : AttributeConverter<SalonId, String> {
    override fun convertToDatabaseColumn(attribute: SalonId?): String? {
        return when (attribute) {
            null -> null
            is SalonId -> attribute.value
            else -> attribute.toString() // Handle case where Hibernate passes unexpected type
        }
    }

    override fun convertToEntityAttribute(dbData: String?): SalonId? {
        return dbData?.let {
            if (it.isBlank()) null else SalonId.of(it)
        }
    }
}
