package ro.animaliaprogramari.animalia.adapter.inbound.event

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.FcmToken

interface FcmTokenRepository : JpaRepository<FcmToken, String> {
    /**
     * Find all FCM tokens for a specific salon (case insensitive) - existing method
     */
    fun findBySalonIdIgnoreCase(salonId: String): List<FcmToken>

    /**
     * Find all active FCM tokens for a specific salon
     */
    fun findBySalonIdAndIsActiveTrue(salonId: String): List<FcmToken>

    /**
     * Find all active FCM tokens for a specific salon (case insensitive)
     */
    fun findBySalonIdIgnoreCaseAndIsActiveTrue(salonId: String): List<FcmToken>

    /**
     * Find all active FCM tokens for a specific user
     */
    fun findByUserIdAndIsActiveTrue(userId: String): List<FcmToken>

    /**
     * Find FCM token by user and salon
     */
    fun findByUserIdAndSalonIdAndIsActiveTrue(
        userId: String,
        salonId: String,
    ): List<FcmToken>

    /**
     * Find FCM token by device ID
     */
    fun findByDeviceIdAndIsActiveTrue(deviceId: String): List<FcmToken>

    /**
     * Delete inactive tokens older than specified days
     */
    @Query("DELETE FROM FcmToken f WHERE f.isActive = false AND f.updatedAt < :cutoffDate")
    fun deleteInactiveTokensOlderThan(
        @Param("cutoffDate") cutoffDate: java.time.LocalDateTime,
    ): Int
}
