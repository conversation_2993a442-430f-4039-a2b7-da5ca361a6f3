package ro.animaliaprogramari.animalia.application.command

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.ZonedDateTime

/**
 * Unit tests for Block Time command classes
 * Tests validation and business rules for commands
 */
@DisplayName("Block Time Commands")
class BlockTimeCommandsTest {
    @Nested
    @DisplayName("CreateBlockTimeCommand")
    inner class CreateBlockTimeCommandTest {
        @Test
        fun `should create valid command with required fields`() {
            // Given
            val salonId = SalonId.of("salon-1")
            val startTime = ZonedDateTime.now().plusHours(1)
            val endTime = startTime.plusHours(2)
            val staffIds = setOf(StaffId.of("staff-1"))
            val createdBy = UserId.of("user-1")

            // When
            val command =
                CreateBlockTimeCommand(
                    salonId = salonId,
                    startTime = startTime,
                    endTime = endTime,
                    reason = BlockReason.PAUZA,
                    staffIds = staffIds,
                    createdBy = createdBy,
                )

            // Then
            assertEquals(salonId, command.salonId)
            assertEquals(startTime, command.startTime)
            assertEquals(endTime, command.endTime)
            assertEquals(BlockReason.PAUZA, command.reason)
            assertEquals(staffIds, command.staffIds)
            assertEquals(createdBy, command.createdBy)
            assertFalse(command.isRecurring)
            assertNull(command.recurrencePattern)
        }

        @Test
        fun `should throw exception when staff IDs are empty`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    CreateBlockTimeCommand(
                        salonId = SalonId.of("salon-1"),
                        startTime = ZonedDateTime.now().plusHours(1),
                        endTime = ZonedDateTime.now().plusHours(2),
                        reason = BlockReason.PAUZA,
                        staffIds = emptySet(),
                        createdBy = UserId.of("user-1"),
                    )
                }
            assertEquals("At least one staff member must be specified", exception.message)
        }

        @Test
        fun `should throw exception when end time is not after start time`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(2)
            val endTime = startTime.minusHours(1)

            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    CreateBlockTimeCommand(
                        salonId = SalonId.of("salon-1"),
                        startTime = startTime,
                        endTime = endTime,
                        reason = BlockReason.PAUZA,
                        staffIds = setOf(StaffId.of("staff-1")),
                        createdBy = UserId.of("user-1"),
                    )
                }
            assertEquals("End time must be after start time", exception.message)
        }

        @Test
        fun `should require recurrence pattern when isRecurring is true`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    CreateBlockTimeCommand(
                        salonId = SalonId.of("salon-1"),
                        startTime = ZonedDateTime.now().plusHours(1),
                        endTime = ZonedDateTime.now().plusHours(2),
                        reason = BlockReason.PAUZA,
                        staffIds = setOf(StaffId.of("staff-1")),
                        isRecurring = true,
                        recurrencePattern = null,
                        createdBy = UserId.of("user-1"),
                    )
                }
            assertEquals("Recurrence pattern is required for recurring blocks", exception.message)
        }

        @Test
        fun `should require custom reason when reason is ALTELE`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    CreateBlockTimeCommand(
                        salonId = SalonId.of("salon-1"),
                        startTime = ZonedDateTime.now().plusHours(1),
                        endTime = ZonedDateTime.now().plusHours(2),
                        reason = BlockReason.ALTELE,
                        customReason = null,
                        staffIds = setOf(StaffId.of("staff-1")),
                        createdBy = UserId.of("user-1"),
                    )
                }
            assertEquals("Custom reason is required when reason is 'Altele'", exception.message)
        }

        @Test
        fun `should allow custom reason when reason is ALTELE`() {
            // When
            val command =
                CreateBlockTimeCommand(
                    salonId = SalonId.of("salon-1"),
                    startTime = ZonedDateTime.now().plusHours(1),
                    endTime = ZonedDateTime.now().plusHours(2),
                    reason = BlockReason.ALTELE,
                    customReason = "Custom reason",
                    staffIds = setOf(StaffId.of("staff-1")),
                    createdBy = UserId.of("user-1"),
                )

            // Then
            assertEquals(BlockReason.ALTELE, command.reason)
            assertEquals("Custom reason", command.customReason)
        }

        @Test
        fun `should create recurring command with pattern`() {
            // Given
            val recurrencePattern = TestDataBuilder.aRecurrencePattern()

            // When
            val command =
                CreateBlockTimeCommand(
                    salonId = SalonId.of("salon-1"),
                    startTime = ZonedDateTime.now().plusHours(1),
                    endTime = ZonedDateTime.now().plusHours(2),
                    reason = BlockReason.PAUZA,
                    staffIds = setOf(StaffId.of("staff-1")),
                    isRecurring = true,
                    recurrencePattern = recurrencePattern,
                    createdBy = UserId.of("user-1"),
                )

            // Then
            assertTrue(command.isRecurring)
            assertEquals(recurrencePattern, command.recurrencePattern)
        }
    }

    @Nested
    @DisplayName("UpdateBlockTimeCommand")
    inner class UpdateBlockTimeCommandTest {
        @Test
        fun `should create valid update command`() {
            // When
            val command =
                UpdateBlockTimeCommand(
                    salonId = SalonId.of("salon-1"),
                    blockId = BlockTimeId.of("block-1"),
                    reason = BlockReason.INTALNIRE,
                    notes = "Updated notes",
                    updatedBy = UserId.of("user-1"),
                )

            // Then
            assertEquals(SalonId.of("salon-1"), command.salonId)
            assertEquals(BlockTimeId.of("block-1"), command.blockId)
            assertEquals(BlockReason.INTALNIRE, command.reason)
            assertEquals("Updated notes", command.notes)
            assertEquals(UserId.of("user-1"), command.updatedBy)
        }

        @Test
        fun `should validate time range when both start and end time are provided`() {
            // Given
            val startTime = ZonedDateTime.now().plusHours(2)
            val endTime = startTime.minusHours(1)

            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    UpdateBlockTimeCommand(
                        salonId = SalonId.of("salon-1"),
                        blockId = BlockTimeId.of("block-1"),
                        startTime = startTime,
                        endTime = endTime,
                        updatedBy = UserId.of("user-1"),
                    )
                }
            assertEquals("End time must be after start time", exception.message)
        }

        @Test
        fun `should validate staff IDs when provided`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    UpdateBlockTimeCommand(
                        salonId = SalonId.of("salon-1"),
                        blockId = BlockTimeId.of("block-1"),
                        staffIds = emptySet(),
                        updatedBy = UserId.of("user-1"),
                    )
                }
            assertEquals("At least one staff member must be specified", exception.message)
        }

        @Test
        fun `should require custom reason when reason is ALTELE`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    UpdateBlockTimeCommand(
                        salonId = SalonId.of("salon-1"),
                        blockId = BlockTimeId.of("block-1"),
                        reason = BlockReason.ALTELE,
                        customReason = null,
                        updatedBy = UserId.of("user-1"),
                    )
                }
            assertEquals("Custom reason is required when reason is 'Altele'", exception.message)
        }
    }

    @Nested
    @DisplayName("DeleteBlockTimeCommand")
    inner class DeleteBlockTimeCommandTest {
        @Test
        fun `should create valid delete command`() {
            // When
            val command =
                DeleteBlockTimeCommand(
                    salonId = SalonId.of("salon-1"),
                    blockId = BlockTimeId.of("block-1"),
                    reason = "No longer needed",
                    notifyStaff = true,
                    deletedBy = UserId.of("user-1"),
                )

            // Then
            assertEquals(SalonId.of("salon-1"), command.salonId)
            assertEquals(BlockTimeId.of("block-1"), command.blockId)
            assertEquals("No longer needed", command.reason)
            assertTrue(command.notifyStaff)
            assertEquals(UserId.of("user-1"), command.deletedBy)
        }

        @Test
        fun `should create delete command with default values`() {
            // When
            val command =
                DeleteBlockTimeCommand(
                    salonId = SalonId.of("salon-1"),
                    blockId = BlockTimeId.of("block-1"),
                    deletedBy = UserId.of("user-1"),
                )

            // Then
            assertNull(command.reason)
            assertTrue(command.notifyStaff) // Default value
        }
    }

    @Nested
    @DisplayName("BulkBlockTimeOperationsCommand")
    inner class BulkBlockTimeOperationsCommandTest {
        @Test
        fun `should create valid bulk command`() {
            // Given
            val blocks =
                listOf(
                    BulkBlockTimeItem(
                        startTime = ZonedDateTime.now().plusHours(1),
                        endTime = ZonedDateTime.now().plusHours(2),
                        reason = BlockReason.PAUZA,
                        staffIds = setOf(StaffId.of("staff-1")),
                    ),
                )

            // When
            val command =
                BulkBlockTimeOperationsCommand(
                    salonId = SalonId.of("salon-1"),
                    operation = BulkOperation.CREATE,
                    blocks = blocks,
                    performedBy = UserId.of("user-1"),
                )

            // Then
            assertEquals(SalonId.of("salon-1"), command.salonId)
            assertEquals(BulkOperation.CREATE, command.operation)
            assertEquals(blocks, command.blocks)
            assertEquals(UserId.of("user-1"), command.performedBy)
        }

        @Test
        fun `should throw exception when blocks list is empty`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    BulkBlockTimeOperationsCommand(
                        salonId = SalonId.of("salon-1"),
                        operation = BulkOperation.CREATE,
                        blocks = emptyList(),
                        performedBy = UserId.of("user-1"),
                    )
                }
            assertEquals("At least one block must be specified", exception.message)
        }

        @Test
        fun `should throw exception when too many blocks`() {
            // Given
            val blocks =
                (1..101).map {
                    BulkBlockTimeItem(
                        startTime = ZonedDateTime.now().plusHours(it.toLong()),
                        endTime = ZonedDateTime.now().plusHours(it.toLong() + 1),
                        reason = BlockReason.PAUZA,
                        staffIds = setOf(StaffId.of("staff-1")),
                    )
                }

            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    BulkBlockTimeOperationsCommand(
                        salonId = SalonId.of("salon-1"),
                        operation = BulkOperation.CREATE,
                        blocks = blocks,
                        performedBy = UserId.of("user-1"),
                    )
                }
            assertEquals("Cannot process more than 100 blocks at once", exception.message)
        }
    }

    @Nested
    @DisplayName("BulkOperation")
    inner class BulkOperationTest {
        @Test
        fun `should parse operation from string`() {
            // When & Then
            assertEquals(BulkOperation.CREATE, BulkOperation.fromString("CREATE"))
            assertEquals(BulkOperation.UPDATE, BulkOperation.fromString("update"))
            assertEquals(BulkOperation.DELETE, BulkOperation.fromString("Delete"))
        }

        @Test
        fun `should throw exception for invalid operation`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    BulkOperation.fromString("INVALID")
                }
            assertEquals("Invalid bulk operation: INVALID", exception.message)
        }
    }
}
