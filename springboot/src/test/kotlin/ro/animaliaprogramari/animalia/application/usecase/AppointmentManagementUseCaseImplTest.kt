package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.exception.AppointmentSchedulingConflictException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.AppointmentSchedulingService
import ro.animaliaprogramari.animalia.domain.service.OptimizedSchedulingConflictService
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import ro.animaliaprogramari.animalia.application.monitoring.WorkflowMetrics
import ro.animaliaprogramari.animalia.adapter.outbound.sms.SmsoSmsSender
import ro.animaliaprogramari.animalia.domain.validation.ValidationService
import java.lang.reflect.InvocationTargetException
import java.time.LocalDate
import java.time.LocalTime

@DisplayName("AppointmentManagementUseCaseImpl - Recurring Validation")
class AppointmentManagementUseCaseImplTest {
    private lateinit var useCase: AppointmentManagementUseCaseImpl

    private val appointmentRepository = mockk<AppointmentRepository>(relaxed = true)
    private val clientRepository = mockk<ClientRepository>(relaxed = true)
    private val petRepository = mockk<PetRepository>(relaxed = true)
    private val userRepository = mockk<UserRepository>(relaxed = true)
    private val salonRepository = mockk<SalonRepository>(relaxed = true)
    private val salonServiceRepository = mockk<SalonServiceRepository>(relaxed = true)
    private val staffRepository = mockk<StaffRepository>(relaxed = true)
    private val blockTimeRepository = mockk<BlockTimeRepository>(relaxed = true)
    private val workingHoursRepository = mockk<WorkingHoursRepository>(relaxed = true)
    private val staffWorkingHoursRepository = mockk<StaffWorkingHoursRepository>(relaxed = true)
    private val domainEventPublisher = mockk<DomainEventPublisher>(relaxed = true)
    private val appointmentSchedulingService = mockk<AppointmentSchedulingService>(relaxed = true)
    private val optimizedConflictService = mockk<OptimizedSchedulingConflictService>(relaxed = true)
    private val validationService = mockk<ValidationService>(relaxed = true)
    private val appointmentAlternativeSuggestionService = mockk<AppointmentAlternativeSuggestionService>(relaxed = true)
    private val smsSender = mockk<SmsoSmsSender>(relaxed = true)
    private val workflowMetrics = mockk<WorkflowMetrics>(relaxed = true)

    @BeforeEach
    fun setUp() {
        useCase = AppointmentManagementUseCaseImpl(
            appointmentRepository,
            clientRepository,
            petRepository,
            userRepository,
            salonRepository,
            salonServiceRepository,
            staffRepository,
            blockTimeRepository,
            workingHoursRepository,
            staffWorkingHoursRepository,
            domainEventPublisher,
            appointmentSchedulingService,
            optimizedConflictService,
            validationService,
            appointmentAlternativeSuggestionService,
            smsSender,
            workflowMetrics,
        )
    }

    private fun invokeValidateRecurring(
        appointment: Appointment,
        frequency: RepetitionFrequency,
    ) {
        val method = useCase::class.java.getDeclaredMethod(
            "validateRecurringSlots",
            Appointment::class.java,
            RepetitionFrequency::class.java,
            Int::class.javaPrimitiveType,
        )
        method.isAccessible = true
        method.invoke(useCase, appointment, frequency, 12)
    }

    @Nested
    inner class ValidateRecurringSlots {
        @Test
        fun `should pass when all future slots are available`() {
            val salonId = SalonId.of("salon-1")
            val staffId = StaffId.of("staff-1")
            val appointment = TestDataBuilder.anAppointment()
                .withSalonId(salonId)
                .withStaffId(staffId)
                .withDate(LocalDate.of(2025, 6, 20))
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
                .build()

            every { staffRepository.findById(staffId) } returns TestDataBuilder.aStaff().withId(staffId.value).build()
            every { workingHoursRepository.findBySalonId(salonId) } returns WorkingHoursSettings.createDefault(salonId)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns StaffWorkingHoursSettings.createDefault(staffId, salonId)
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returns true

            assertDoesNotThrow {
                invokeValidateRecurring(appointment, RepetitionFrequency.WEEKLY)
            }

            verify(exactly = 12) { optimizedConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) }
        }

        @Test
        fun `should throw exception when any future slot is unavailable`() {
            val salonId = SalonId.of("salon-1")
            val staffId = StaffId.of("staff-1")
            val appointment = TestDataBuilder.anAppointment()
                .withSalonId(salonId)
                .withStaffId(staffId)
                .withDate(LocalDate.of(2025, 6, 20))
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
                .build()

            every { staffRepository.findById(staffId) } returns TestDataBuilder.aStaff().withId(staffId.value).build()
            every { workingHoursRepository.findBySalonId(salonId) } returns WorkingHoursSettings.createDefault(salonId)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns StaffWorkingHoursSettings.createDefault(staffId, salonId)
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returnsMany listOf(true, true, false) + List(9) { true }

            val ex = assertThrows<InvocationTargetException> {
                invokeValidateRecurring(appointment, RepetitionFrequency.WEEKLY)
            }
            val cause = ex.targetException
            assert(cause is AppointmentSchedulingConflictException)
            cause as AppointmentSchedulingConflictException
            kotlin.test.assertEquals(1, cause.conflicts.size)
        }
    }
}

