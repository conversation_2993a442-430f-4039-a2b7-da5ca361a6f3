package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.application.port.outbound.SalonServiceRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.ServicePricingValidationService
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@DisplayName("SalonServiceManagementUseCase")
class SalonServiceManagementUseCaseTest {
    private val salonServiceRepository = mockk<SalonServiceRepository>()
    private val pricingValidationService = mockk<ServicePricingValidationService>(relaxed = true)

    private lateinit var useCase: SalonServiceManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase =
            SalonServiceManagementUseCaseImpl(
                salonServiceRepository = salonServiceRepository,
                pricingValidationService = pricingValidationService,
            )
    }

    @Nested
    @DisplayName("Duplicate Service")
    inner class DuplicateServiceTests {
        @Test
        fun `should duplicate service with variable pricing`() {
            // Given
            val salonId = SalonId.generate()
            val serviceId = ServiceId.generate()
            val originalService =
                SalonService.create(
                    salonId = salonId,
                    name = "Bath",
                    description = "Desc",
                    basePrice = Money.of(50.0),
                    duration = Duration.ofMinutes(60),
                    category = ServiceCategory.GROOMING,
                    displayOrder = 1,
                    requirements = listOf("req"),
                    sizePrices = mapOf("S" to Money.of(40.0)),
                    sizeDurations = mapOf("S" to Duration.ofMinutes(45)),
                    minPrice = Money.of(30.0),
                    maxPrice = Money.of(70.0),
                    sizeMinPrices = mapOf("S" to Money.of(25.0)),
                    sizeMaxPrices = mapOf("S" to Money.of(65.0)),
                ).copy(id = serviceId)

            every { salonServiceRepository.findById(serviceId) } returns originalService
            every { salonServiceRepository.findBySalonId(salonId) } returns listOf(originalService)
            val slotService = slot<SalonService>()
            every { salonServiceRepository.save(capture(slotService)) } answers { slotService.captured }

            // When
            val result = useCase.duplicateService(serviceId, salonId)

            // Then
            assertNotNull(result)
            assertEquals(originalService.sizePrices, result.sizePrices)
            assertEquals(originalService.sizeDurations, result.sizeDurations)
            assertEquals(originalService.minPrice, result.minPrice)
            assertEquals(originalService.maxPrice, result.maxPrice)
            assertEquals(originalService.sizeMinPrices, result.sizeMinPrices)
            assertEquals(originalService.sizeMaxPrices, result.sizeMaxPrices)

            verify(exactly = 1) { salonServiceRepository.save(any()) }
        }
    }
}
