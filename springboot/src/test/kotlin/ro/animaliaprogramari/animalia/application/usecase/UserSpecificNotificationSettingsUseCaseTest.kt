package ro.animaliaprogramari.animalia.application.usecase

import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.NotificationSettingsEntityMapper
import ro.animaliaprogramari.animalia.application.command.UpdateNotificationSettingsCommand
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.query.GetNotificationSettingsQuery
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalTime

/**
 * Unit tests for user-specific notification settings use case
 */
@ExtendWith(MockitoExtension::class)
@DisplayName("User-Specific Notification Settings Use Case Tests")
@Disabled
class UserSpecificNotificationSettingsUseCaseTest {

    @Mock
    private lateinit var notificationSettingsRepository: NotificationSettingsRepository

    @Mock
    private lateinit var salonRepository: SalonRepository

    private lateinit var useCase: NotificationSettingsManagementUseCaseImpl

    private val testUserId = UserId.generate()
    private val testSalonId = SalonId.generate()
    private val testUserId2 = UserId.generate()
    private val testSalonId2 = SalonId.generate()

    @BeforeEach
    fun setUp() {
        useCase = NotificationSettingsManagementUseCaseImpl(
            notificationSettingsRepository,
            salonRepository
        )
    }

    @Nested
    @DisplayName("Get Notification Settings")
    inner class GetNotificationSettingsTests {

        @Test
        fun `should get existing notification settings for user and salon`() {
            // Given
            val salon = createTestSalon(testSalonId)
            val existingSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val query = GetNotificationSettingsQuery(testUserId, testSalonId)

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(existingSettings)

            // When
            val result = useCase.getNotificationSettings(query)

            // Then
            assertEquals(existingSettings, result)
            verify(salonRepository).findById(testSalonId)
            verify(notificationSettingsRepository).findByUserIdAndSalonId(testUserId, testSalonId)
            verify(notificationSettingsRepository, never()).save(any())
        }

        @Test
        fun `should create default settings when none exist for user and salon`() {
            // Given
            val salon = createTestSalon(testSalonId)
            val defaultSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val query = GetNotificationSettingsQuery(testUserId, testSalonId)

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(null)
            whenever(notificationSettingsRepository.save(any())).thenReturn(defaultSettings)

            // When
            val result = useCase.getNotificationSettings(query)

            // Then
            assertEquals(defaultSettings, result)
            verify(salonRepository).findById(testSalonId)
            verify(notificationSettingsRepository).findByUserIdAndSalonId(testUserId, testSalonId)
            verify(notificationSettingsRepository).save(argThat {
                userId == testUserId && salonId == testSalonId
            })
        }

        @Test
        fun `should throw exception when salon not found`() {
            // Given
            val query = GetNotificationSettingsQuery(testUserId, testSalonId)
            whenever(salonRepository.findById(testSalonId)).thenReturn(null)

            // When & Then
            val exception = assertThrows(EntityNotFoundException::class.java) {
                useCase.getNotificationSettings(query)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            verify(salonRepository).findById(testSalonId)
            verify(notificationSettingsRepository, never()).findByUserIdAndSalonId(any(), any())
        }

        @Test
        fun `should handle multiple users in same salon independently`() {
            // Given
            val salon = createTestSalon(testSalonId)
            val settings1 = NotificationSettings.createDefault(testUserId, testSalonId)
            val settings2 = NotificationSettings.createDefault(testUserId2, testSalonId)
            val query1 = GetNotificationSettingsQuery(testUserId, testSalonId)
            val query2 = GetNotificationSettingsQuery(testUserId2, testSalonId)

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(settings1)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId2, testSalonId))
                .thenReturn(settings2)

            // When
            val result1 = useCase.getNotificationSettings(query1)
            val result2 = useCase.getNotificationSettings(query2)

            // Then
            assertEquals(settings1, result1)
            assertEquals(settings2, result2)
            assertEquals(testUserId, result1.userId)
            assertEquals(testUserId2, result2.userId)
            assertEquals(testSalonId, result1.salonId)
            assertEquals(testSalonId, result2.salonId)
        }
    }

    @Nested
    @DisplayName("Update Notification Settings")
    inner class UpdateNotificationSettingsTests {

        @Test
        fun `should update existing notification settings for user and salon`() {
            // Given
            val salon = createTestSalon(testSalonId)
            val existingSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val updatedSettings = existingSettings.updateSettings(
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.SILENT
            )

            val command = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.SILENT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(existingSettings)
            whenever(notificationSettingsRepository.save(any())).thenReturn(updatedSettings)

            // When
            val result = useCase.updateNotificationSettings(command)

            // Then
            assertEquals(updatedSettings, result)
            verify(salonRepository).findById(testSalonId)
            verify(notificationSettingsRepository).findByUserIdAndSalonId(testUserId, testSalonId)
            verify(notificationSettingsRepository).save(argThat {
                userId == testUserId &&
                salonId == testSalonId &&
                !pushNotificationsEnabled &&
                soundPreference == SoundPreference.SILENT
            })
        }

        @Test
        fun `should create and update settings when none exist for user and salon`() {
            // Given
            val salon = createTestSalon(testSalonId)
            val defaultSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val updatedSettings = defaultSettings.updateSettings(
                pushNotificationsEnabled = false,
                vibrationEnabled = false
            )

            val command = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.DEFAULT,
                vibrationEnabled = false,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(null)
            whenever(notificationSettingsRepository.save(any())).thenReturn(updatedSettings)

            // When
            val result = useCase.updateNotificationSettings(command)

            // Then
            assertEquals(updatedSettings, result)
            verify(salonRepository).findById(testSalonId)
            verify(notificationSettingsRepository).findByUserIdAndSalonId(testUserId, testSalonId)
            verify(notificationSettingsRepository).save(argThat {
                userId == testUserId &&
                salonId == testSalonId &&
                !pushNotificationsEnabled &&
                !vibrationEnabled
            })
        }

        @Test
        fun `should throw exception when salon not found during update`() {
            // Given
            val command = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.DEFAULT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )

            whenever(salonRepository.findById(testSalonId)).thenReturn(null)

            // When & Then
            val exception = assertThrows(EntityNotFoundException::class.java) {
                useCase.updateNotificationSettings(command)
            }

            assertTrue(exception.message!!.contains("Salon not found"))
            verify(salonRepository).findById(testSalonId)
            verify(notificationSettingsRepository, never()).findByUserIdAndSalonId(any(), any())
            verify(notificationSettingsRepository, never()).save(any())
        }

        @Test
        fun `should handle complex do not disturb settings update`() {
            // Given
            val salon = createTestSalon(testSalonId)
            val existingSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val complexDnd = DoNotDisturbSettings(
                enabled = true,
                startTime = LocalTime.of(23, 30),
                endTime = LocalTime.of(7, 30),
                allowCritical = false
            )
            val complexRules = NotificationRules(
                newAppointments = false,
                appointmentCancellations = true,
                paymentConfirmations = false,
                teamMemberUpdates = true,
                systemMaintenanceAlerts = false,
                defaultPriority = NotificationPriority.HIGH
            )

            val command = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.CUSTOM2,
                vibrationEnabled = false,
                doNotDisturb = complexDnd,
                notificationRules = complexRules
            )

            val updatedSettings = existingSettings.updateSettings(
                pushNotificationsEnabled = command.pushNotificationsEnabled,
                soundPreference = command.soundPreference,
                vibrationEnabled = command.vibrationEnabled,
                doNotDisturb = command.doNotDisturb,
                notificationRules = command.notificationRules
            )

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(existingSettings)
            whenever(notificationSettingsRepository.save(any())).thenReturn(updatedSettings)

            // When
            val result = useCase.updateNotificationSettings(command)

            // Then
            assertEquals(updatedSettings, result)
            verify(notificationSettingsRepository).save(argThat {
                userId == testUserId &&
                salonId == testSalonId &&
                doNotDisturb.enabled &&
                doNotDisturb.startTime == LocalTime.of(23, 30) &&
                doNotDisturb.endTime == LocalTime.of(7, 30) &&
                !doNotDisturb.allowCritical &&
                !notificationRules.newAppointments &&
                notificationRules.appointmentCancellations &&
                !notificationRules.paymentConfirmations &&
                notificationRules.teamMemberUpdates &&
                !notificationRules.systemMaintenanceAlerts &&
                notificationRules.defaultPriority == NotificationPriority.HIGH
            })
        }
    }

    @Nested
    @DisplayName("User Isolation Tests")
    inner class UserIsolationTests {

        @Test
        fun `should maintain separate settings for same user across different salons`() {
            // Given
            val salon1 = createTestSalon(testSalonId)
            val salon2 = createTestSalon(testSalonId2)

            val command1 = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.DEFAULT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )

            val command2 = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId2,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.SILENT,
                vibrationEnabled = false,
                doNotDisturb = DoNotDisturbSettings(enabled = true),
                notificationRules = NotificationRules(newAppointments = false)
            )

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon1)
            whenever(salonRepository.findById(testSalonId2)).thenReturn(salon2)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(null)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId2))
                .thenReturn(null)
            whenever(notificationSettingsRepository.save(any())).thenAnswer { it.arguments[0] }

            // When
            val result1 = useCase.updateNotificationSettings(command1)
            val result2 = useCase.updateNotificationSettings(command2)

            // Then
            assertEquals(testUserId, result1.userId)
            assertEquals(testUserId, result2.userId)
            assertEquals(testSalonId, result1.salonId)
            assertEquals(testSalonId2, result2.salonId)
            assertTrue(result1.pushNotificationsEnabled)
            assertFalse(result2.pushNotificationsEnabled)
            assertEquals(SoundPreference.DEFAULT, result1.soundPreference)
            assertEquals(SoundPreference.SILENT, result2.soundPreference)
        }

        @Test
        fun `should maintain separate settings for different users in same salon`() {
            // Given
            val salon = createTestSalon(testSalonId)

            val command1 = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.DEFAULT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )

            val command2 = UpdateNotificationSettingsCommand(
                userId = testUserId2,
                salonId = testSalonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.CUSTOM1,
                vibrationEnabled = false,
                doNotDisturb = DoNotDisturbSettings(enabled = true),
                notificationRules = NotificationRules(teamMemberUpdates = false)
            )

            whenever(salonRepository.findById(testSalonId)).thenReturn(salon)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalonId))
                .thenReturn(null)
            whenever(notificationSettingsRepository.findByUserIdAndSalonId(testUserId2, testSalonId))
                .thenReturn(null)
            whenever(notificationSettingsRepository.save(any())).thenAnswer { it.arguments[0] }

            // When
            val result1 = useCase.updateNotificationSettings(command1)
            val result2 = useCase.updateNotificationSettings(command2)

            // Then
            assertEquals(testUserId, result1.userId)
            assertEquals(testUserId2, result2.userId)
            assertEquals(testSalonId, result1.salonId)
            assertEquals(testSalonId, result2.salonId)
            assertTrue(result1.pushNotificationsEnabled)
            assertFalse(result2.pushNotificationsEnabled)
            assertTrue(result1.notificationRules.teamMemberUpdates)
            assertFalse(result2.notificationRules.teamMemberUpdates)
        }
    }

    private fun createTestSalon(salonId: SalonId): Salon {
        return Salon(
            id = salonId,
            name = "Test Salon",
            address = "Test Address",
            phone = PhoneNumber.of("+40123456789"),
            ownerId = UserId.generate(),
            description = "Test Description",
            city = "Test City",
            email = Email.of("<EMAIL>"),
            isActive = true
        )
    }
}

// Additional integration test for entity mapping
@ExtendWith(MockitoExtension::class)
@DisplayName("Notification Settings Entity Mapper Tests")
class NotificationSettingsEntityMapperTest {

    private val mapper = NotificationSettingsEntityMapper()
    private val testUserId = UserId.generate()
    private val testSalonId = SalonId.generate()

    @Test
    fun `should map domain to entity with userId and salonId`() {
        // Given
        val domain = NotificationSettings.createDefault(testUserId, testSalonId)

        // When
        val entity = mapper.toEntity(domain)

        // Then
        assertEquals(testUserId.value, entity.userId)
        assertEquals(testSalonId.value, entity.salonId)
        assertEquals(domain.pushNotificationsEnabled, entity.pushNotificationsEnabled)
        assertEquals(domain.soundPreference, entity.soundPreference)
        assertEquals(domain.vibrationEnabled, entity.vibrationEnabled)
    }

    @Test
    fun `should map entity to domain with userId and salonId`() {
        // Given
        val entity = ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationSettings(
            userId = testUserId.value,
            salonId = testSalonId.value,
            pushNotificationsEnabled = false,
            soundPreference = SoundPreference.SILENT,
            vibrationEnabled = false,
            dndEnabled = true,
            dndStartTime = java.time.LocalTime.of(23, 0),
            dndEndTime = java.time.LocalTime.of(7, 0),
            dndAllowCritical = false,
            newAppointments = false,
            appointmentCancellations = true,
            paymentConfirmations = false,
            teamMemberUpdates = true,
            systemMaintenanceAlerts = false,
            defaultPriority = NotificationPriority.HIGH,
            createdAt = java.time.LocalDateTime.now(),
            updatedAt = java.time.LocalDateTime.now()
        )

        // When
        val domain = mapper.toDomain(entity)

        // Then
        assertEquals(testUserId, domain.userId)
        assertEquals(testSalonId, domain.salonId)
        assertFalse(domain.pushNotificationsEnabled)
        assertEquals(SoundPreference.SILENT, domain.soundPreference)
        assertFalse(domain.vibrationEnabled)
        assertTrue(domain.doNotDisturb.enabled)
        assertEquals(java.time.LocalTime.of(23, 0), domain.doNotDisturb.startTime)
        assertEquals(java.time.LocalTime.of(7, 0), domain.doNotDisturb.endTime)
        assertFalse(domain.doNotDisturb.allowCritical)
        assertFalse(domain.notificationRules.newAppointments)
        assertTrue(domain.notificationRules.appointmentCancellations)
        assertFalse(domain.notificationRules.paymentConfirmations)
        assertTrue(domain.notificationRules.teamMemberUpdates)
        assertFalse(domain.notificationRules.systemMaintenanceAlerts)
        assertEquals(NotificationPriority.HIGH, domain.notificationRules.defaultPriority)
    }
}
