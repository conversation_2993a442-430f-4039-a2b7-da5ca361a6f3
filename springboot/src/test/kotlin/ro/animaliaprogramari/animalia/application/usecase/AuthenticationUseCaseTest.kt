package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.application.port.outbound.FirebaseTokenValidator
import ro.animaliaprogramari.animalia.application.port.outbound.JwtTokenGenerator
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.query.GetUserByFirebaseUidQuery
import ro.animaliaprogramari.animalia.application.query.GetUserByIdQuery
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.service.AuthenticationService
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

/**
 * Unit tests for AuthenticationUseCaseImpl
 * Tests the authentication business logic without external dependencies
 */
@DisplayName("AuthenticationUseCase")
class AuthenticationUseCaseTest {
    // Mocked dependencies
    private val firebaseTokenValidator = mockk<FirebaseTokenValidator>()
    private val jwtTokenGenerator = mockk<JwtTokenGenerator>()
    private val userRepository = mockk<UserRepository>()
    private val staffRepository = mockk<StaffRepository>()
    private val authenticationService = mockk<AuthenticationService>()

    // System under test
    private lateinit var useCase: AuthenticationUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase =
            AuthenticationUseCaseImpl(
                firebaseTokenValidator = firebaseTokenValidator,
                jwtTokenGenerator = jwtTokenGenerator,
                userRepository = userRepository,
                staffRepository = staffRepository,
                authenticationService = authenticationService,
            )
    }

    @Nested
    @DisplayName("Get Current User")
    inner class GetCurrentUserTests {
        @Test
        fun `should return current user when user exists and is active`() {
            // Given
            val userId = UserId.generate()
            val query = GetUserByIdQuery(userId = userId)

            val user =
                TestDataBuilder.aUser()
                    .withId(userId)
                    .withActive(true)
                    .build()

            val staffMembers =
                listOf(
                    TestDataBuilder.aStaff()
                        .withUserId(userId)
                        .withActive(true)
                        .build(),
                )

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findActiveByUserId(userId) } returns staffMembers

            // When
            val result = useCase.getCurrentUser(query)

            // Then
            assertNotNull(result)
            assertEquals(userId, result!!.userId)
            assertEquals(user.name, result.name)
            assertEquals(user.email, result.email)
            assertEquals(user.role, result.role)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { staffRepository.findActiveByUserId(userId) }
        }

        @Test
        fun `should return null when user not found`() {
            // Given
            val userId = UserId.generate()
            val query = GetUserByIdQuery(userId = userId)

            every { userRepository.findById(userId) } returns null

            // When
            val result = useCase.getCurrentUser(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { staffRepository.findActiveByUserId(any()) }
        }

        @Test
        fun `should return null when user is inactive`() {
            // Given
            val userId = UserId.generate()
            val query = GetUserByIdQuery(userId = userId)

            val inactiveUser =
                TestDataBuilder.aUser()
                    .withId(userId)
                    .withActive(false)
                    .build()

            every { userRepository.findById(userId) } returns inactiveUser

            // When
            val result = useCase.getCurrentUser(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { staffRepository.findActiveByUserId(any()) }
        }
    }

    @Nested
    @DisplayName("Get User by Firebase UID")
    inner class GetUserByFirebaseUidTests {
        @Test
        fun `should return user when found by Firebase UID`() {
            // Given
            val firebaseUid = "firebase_uid_123"
            val query = GetUserByFirebaseUidQuery(firebaseUid = firebaseUid)

            val user =
                TestDataBuilder.aUser()
                    .withFirebaseUid(firebaseUid)
                    .build()

            every { userRepository.findByFirebaseUid(firebaseUid) } returns user

            // When
            val result = useCase.getUserByFirebaseUid(query)

            // Then
            assertNotNull(result)
            assertEquals(firebaseUid, result!!.firebaseUid)
            assertEquals(user.id, result.id)

            verify(exactly = 1) { userRepository.findByFirebaseUid(firebaseUid) }
        }

        @Test
        fun `should return null when user not found by Firebase UID`() {
            // Given
            val firebaseUid = "nonexistent_firebase_uid"
            val query = GetUserByFirebaseUidQuery(firebaseUid = firebaseUid)

            every { userRepository.findByFirebaseUid(firebaseUid) } returns null

            // When
            val result = useCase.getUserByFirebaseUid(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { userRepository.findByFirebaseUid(firebaseUid) }
        }
    }
}
