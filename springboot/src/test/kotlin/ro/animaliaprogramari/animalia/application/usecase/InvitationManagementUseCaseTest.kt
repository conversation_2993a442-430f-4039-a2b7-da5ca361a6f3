package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.AcceptSalonInvitationCommand
import ro.animaliaprogramari.animalia.application.command.CancelSalonInvitationCommand
import ro.animaliaprogramari.animalia.application.command.DeclineSalonInvitationCommand
import ro.animaliaprogramari.animalia.application.command.SendSalonInvitationCommand
import ro.animaliaprogramari.animalia.application.port.outbound.SalonInvitationRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.exception.UnauthorizedException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Unit tests for InvitationManagementUseCaseImpl
 * Tests all invitation lifecycle operations with comprehensive coverage
 */
@DisplayName("InvitationManagementUseCase")
class InvitationManagementUseCaseTest {
    // Mocked dependencies
    private val invitationRepository = mockk<SalonInvitationRepository>()
    private val userRepository = mockk<UserRepository>()
    private val salonRepository = mockk<SalonRepository>()
    private val staffRepository = mockk<StaffRepository>()

    // System under test
    private lateinit var useCase: InvitationManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase =
            InvitationManagementUseCaseImpl(
                invitationRepository = invitationRepository,
                userRepository = userRepository,
                salonRepository = salonRepository,
                staffRepository = staffRepository,
            )
    }

    @Nested
    @DisplayName("Send Invitation")
    inner class SendInvitationTests {
        @Test
        fun `should send invitation successfully when all validations pass`() {
            // Given
            val salonId = SalonId.generate()
            val inviterUserId = UserId.generate()
            val invitedPhone = "+***********"
            val proposedRole = StaffRole.GROOMER
            val proposedPermissions = StaffPermissions.defaultGroomerAccess()

            val command =
                SendSalonInvitationCommand(
                    salonId = salonId,
                    inviterUserId = inviterUserId,
                    invitedUserPhone = invitedPhone,
                    proposedRole = proposedRole,
                    proposedPermissions = proposedPermissions,
                    message = "Join our team!",
                )

            val salon = TestDataBuilder.aSalon().withId(salonId).build()
            val inviter = TestDataBuilder.aUser().withId(inviterUserId).withRole(UserRole.STAFF).build()
            val inviterStaff =
                TestDataBuilder.aStaff()
                    .withUserId(inviterUserId)
                    .withSalonId(salonId)
                    .withRole(StaffRole.CHIEF_GROOMER)
                    .withActive(true)
                    .build()

            val expectedInvitation =
                TestDataBuilder.aSalonInvitation()
                    .withSalonId(salonId)
                    .withInviterUserId(inviterUserId)
                    .withInvitedUserPhone(invitedPhone)
                    .withProposedRole(proposedRole)
                    .withProposedPermissions(proposedPermissions)
                    .withMessage("Join our team!")
                    .build()

            // Mock repository calls
            every { salonRepository.findById(salonId) } returns salon
            every { userRepository.findById(inviterUserId) } returns inviter
            every { staffRepository.findByUserIdAndSalonId(inviterUserId, salonId) } returns inviterStaff
            every { invitationRepository.existsPendingInvitation(salonId, invitedPhone) } returns false
            every { userRepository.findByPhoneNumber(PhoneNumber(invitedPhone)) } returns null
            every { invitationRepository.save(any()) } returns expectedInvitation

            // When
            val result = useCase.sendInvitation(command)

            // Then
            assertNotNull(result)
            assertEquals(salonId, result.salonId)
            assertEquals(inviterUserId, result.inviterUserId)
            assertEquals(invitedPhone, result.invitedUserPhone)
            assertEquals(proposedRole, result.proposedRole)
            assertEquals(InvitationStatus.PENDING, result.status)

            // Verify interactions
            verify(exactly = 1) { salonRepository.findById(salonId) }
            verify(exactly = 1) { userRepository.findById(inviterUserId) }
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(inviterUserId, salonId) }
            verify(exactly = 1) { invitationRepository.existsPendingInvitation(salonId, invitedPhone) }
            verify(exactly = 1) { userRepository.findByPhoneNumber(PhoneNumber(invitedPhone)) }

            verify(exactly = 1) { invitationRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when salon not found`() {
            // Given
            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.generate(),
                    inviterUserId = UserId.generate(),
                    invitedUserPhone = "+***********",
                    proposedRole = StaffRole.GROOMER,
                    proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                )

            every { salonRepository.findById(command.salonId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.sendInvitation(command)
                }

            assertTrue(exception.message!!.contains("Salon not found"))
            verify(exactly = 1) { salonRepository.findById(command.salonId) }
        }

        @Test
        fun `should throw EntityNotFoundException when inviter not found`() {
            // Given
            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.generate(),
                    inviterUserId = UserId.generate(),
                    invitedUserPhone = "+***********",
                    proposedRole = StaffRole.GROOMER,
                    proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()

            every { salonRepository.findById(command.salonId) } returns salon
            every { userRepository.findById(command.inviterUserId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.sendInvitation(command)
                }

            assertTrue(exception.message!!.contains("Inviter user not found"))
            verify(exactly = 1) { userRepository.findById(command.inviterUserId) }
        }

        @Test
        fun `should throw UnauthorizedException when inviter not associated with salon`() {
            // Given
            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.generate(),
                    inviterUserId = UserId.generate(),
                    invitedUserPhone = "+***********",
                    proposedRole = StaffRole.GROOMER,
                    proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
            val inviter = TestDataBuilder.aUser().withId(command.inviterUserId).build()

            every { salonRepository.findById(command.salonId) } returns salon
            every { userRepository.findById(command.inviterUserId) } returns inviter
            every { staffRepository.findByUserIdAndSalonId(command.inviterUserId, command.salonId) } returns null

            // When & Then
            val exception =
                assertThrows<UnauthorizedException> {
                    useCase.sendInvitation(command)
                }

            assertTrue(exception.message!!.contains("User is not associated with this salon"))
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(command.inviterUserId, command.salonId) }
        }

        @Test
        fun `should throw UnauthorizedException when inviter is not chief groomer or admin`() {
            // Given
            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.generate(),
                    inviterUserId = UserId.generate(),
                    invitedUserPhone = "+***********",
                    proposedRole = StaffRole.GROOMER,
                    proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
            val inviter = TestDataBuilder.aUser().withId(command.inviterUserId).withRole(UserRole.STAFF).build()
            val inviterStaff =
                TestDataBuilder.aStaff()
                    .withUserId(command.inviterUserId)
                    .withSalonId(command.salonId)
                    .withRole(StaffRole.GROOMER) // Not chief groomer
                    .withActive(true)
                    .build()

            every { salonRepository.findById(command.salonId) } returns salon
            every { userRepository.findById(command.inviterUserId) } returns inviter
            every { staffRepository.findByUserIdAndSalonId(command.inviterUserId, command.salonId) } returns inviterStaff

            // When & Then
            val exception =
                assertThrows<UnauthorizedException> {
                    useCase.sendInvitation(command)
                }

            assertTrue(exception.message!!.contains("Only chief groomers and admins can send invitations"))
        }

        @Test
        fun `should throw BusinessRuleViolationException when pending invitation already exists`() {
            // Given
            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.generate(),
                    inviterUserId = UserId.generate(),
                    invitedUserPhone = "+***********",
                    proposedRole = StaffRole.GROOMER,
                    proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
            val inviter = TestDataBuilder.aUser().withId(command.inviterUserId).withRole(UserRole.STAFF).build()
            val inviterStaff =
                TestDataBuilder.aStaff()
                    .withUserId(command.inviterUserId)
                    .withSalonId(command.salonId)
                    .withRole(StaffRole.CHIEF_GROOMER)
                    .withActive(true)
                    .build()

            every { salonRepository.findById(command.salonId) } returns salon
            every { userRepository.findById(command.inviterUserId) } returns inviter
            every { staffRepository.findByUserIdAndSalonId(command.inviterUserId, command.salonId) } returns inviterStaff
            every { invitationRepository.existsPendingInvitation(command.salonId, command.invitedUserPhone) } returns true

            // When & Then
            val exception =
                assertThrows<BusinessRuleViolationException> {
                    useCase.sendInvitation(command)
                }

            assertTrue(exception.message!!.contains("There is already a pending invitation"))
            verify(
                exactly = 1,
            ) { invitationRepository.existsPendingInvitation(command.salonId, command.invitedUserPhone) }
        }

        @Test
        fun `should throw BusinessRuleViolationException when user already associated with salon`() {
            // Given
            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.generate(),
                    inviterUserId = UserId.generate(),
                    invitedUserPhone = "+***********",
                    proposedRole = StaffRole.GROOMER,
                    proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
            val inviter = TestDataBuilder.aUser().withId(command.inviterUserId).withRole(UserRole.STAFF).build()
            val inviterStaff =
                TestDataBuilder.aStaff()
                    .withUserId(command.inviterUserId)
                    .withSalonId(command.salonId)
                    .withRole(StaffRole.CHIEF_GROOMER)
                    .withActive(true)
                    .build()

            val existingUser = TestDataBuilder.aUser().withPhoneNumber(command.invitedUserPhone).build()
            val existingStaff =
                TestDataBuilder.aStaff()
                    .withUserId(existingUser.id)
                    .withSalonId(command.salonId)
                    .withActive(true)
                    .build()

            every { salonRepository.findById(command.salonId) } returns salon
            every { userRepository.findById(command.inviterUserId) } returns inviter
            every { staffRepository.findByUserIdAndSalonId(command.inviterUserId, command.salonId) } returns inviterStaff
            every { invitationRepository.existsPendingInvitation(command.salonId, command.invitedUserPhone) } returns false
            every { userRepository.findByPhoneNumber(PhoneNumber(command.invitedUserPhone)) } returns existingUser

            every { staffRepository.findByUserIdAndSalonId(existingUser.id, command.salonId) } returns existingStaff

            // When & Then
            val exception =
                assertThrows<BusinessRuleViolationException> {
                    useCase.sendInvitation(command)
                }

            assertTrue(exception.message!!.contains("User is already associated with this salon"))
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(existingUser.id, command.salonId) }
        }

        @Test
        fun `should throw UnauthorizedException when admin has no salon association`() {
            // Given - Based on actual implementation, admin still needs salon association
            val command =
                SendSalonInvitationCommand(
                    salonId = SalonId.generate(),
                    inviterUserId = UserId.generate(),
                    invitedUserPhone = "+***********",
                    proposedRole = StaffRole.GROOMER,
                    proposedPermissions = StaffPermissions.defaultGroomerAccess(),
                )

            val salon = TestDataBuilder.aSalon().withId(command.salonId).build()
            val adminInviter = TestDataBuilder.aUser().withId(command.inviterUserId).withRole(UserRole.ADMIN).build()

            every { salonRepository.findById(command.salonId) } returns salon
            every { userRepository.findById(command.inviterUserId) } returns adminInviter
            every { staffRepository.findByUserIdAndSalonId(command.inviterUserId, command.salonId) } returns null

            // When & Then
            val exception =
                assertThrows<UnauthorizedException> {
                    useCase.sendInvitation(command)
                }

            assertTrue(exception.message!!.contains("User is not associated with this salon"))
        }
    }

    @Nested
    @DisplayName("Accept Invitation")
    inner class AcceptInvitationTests {
        @Test
        fun `should accept invitation successfully and create staff member`() {
            // Given
            val invitationId = InvitationId.generate()
            val acceptingUserId = UserId.generate()
            val salonId = SalonId.generate()

            val command =
                AcceptSalonInvitationCommand(
                    invitationId = invitationId,
                    acceptingUserId = acceptingUserId,
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(invitationId)
                    .withSalonId(salonId)
                    .withStatus(InvitationStatus.PENDING)
                    .withProposedRole(StaffRole.GROOMER)
                    .withProposedPermissions(StaffPermissions.defaultGroomerAccess())
                    .withExpiresAt(LocalDateTime.now().plusDays(1)) // Not expired
                    .build()

            val acceptingUser =
                TestDataBuilder.aUser()
                    .withId(acceptingUserId)
                    .withPhoneNumber(invitation.invitedUserPhone)
                    .build()

            val acceptedInvitation =
                invitation.copy(
                    status = InvitationStatus.ACCEPTED,
                    respondedAt = LocalDateTime.now(),
                )

            val expectedStaff =
                TestDataBuilder.aStaff()
                    .withUserId(acceptingUserId)
                    .withSalonId(salonId)
                    .withRole(StaffRole.GROOMER)
                    .withPermissions(StaffPermissions.defaultGroomerAccess())
                    .build()

            // Mock repository calls
            every { invitationRepository.findById(invitationId) } returns invitation
            every { userRepository.findById(acceptingUserId) } returns acceptingUser
            every { staffRepository.findByUserIdAndSalonId(acceptingUserId, salonId) } returns null
            every { invitationRepository.save(any()) } returns acceptedInvitation
            every { staffRepository.save(any()) } returns expectedStaff

            // When
            val result = useCase.acceptInvitation(command)

            // Then
            assertNotNull(result)
            assertEquals(acceptingUserId, result.userId)
            assertEquals(salonId, result.salonId)
            assertEquals(StaffRole.GROOMER, result.role)
            assertTrue(result.isActive)

            // Verify interactions
            verify(exactly = 1) { invitationRepository.findById(invitationId) }
            verify(exactly = 1) { userRepository.findById(acceptingUserId) }
            verify(exactly = 1) { staffRepository.findByUserIdAndSalonId(acceptingUserId, salonId) }
            verify(exactly = 1) { invitationRepository.save(any()) }
            verify(exactly = 1) { staffRepository.save(any()) }
        }

        @Test
        fun `should create chief groomer when accepting chief groomer invitation`() {
            // Given
            val invitationId = InvitationId.generate()
            val acceptingUserId = UserId.generate()
            val salonId = SalonId.generate()

            val command =
                AcceptSalonInvitationCommand(
                    invitationId = invitationId,
                    acceptingUserId = acceptingUserId,
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(invitationId)
                    .withSalonId(salonId)
                    .withStatus(InvitationStatus.PENDING)
                    .withProposedRole(StaffRole.CHIEF_GROOMER)
                    .withProposedPermissions(StaffPermissions.fullAccess())
                    .withExpiresAt(LocalDateTime.now().plusDays(1))
                    .build()

            val acceptingUser =
                TestDataBuilder.aUser()
                    .withId(acceptingUserId)
                    .withPhoneNumber(invitation.invitedUserPhone)
                    .build()

            val expectedStaff =
                TestDataBuilder.aStaff()
                    .withUserId(acceptingUserId)
                    .withSalonId(salonId)
                    .withRole(StaffRole.CHIEF_GROOMER)
                    .withPermissions(StaffPermissions.fullAccess())
                    .build()

            every { invitationRepository.findById(invitationId) } returns invitation
            every { userRepository.findById(acceptingUserId) } returns acceptingUser
            every { staffRepository.findByUserIdAndSalonId(acceptingUserId, salonId) } returns null
            every { invitationRepository.save(any()) } returns invitation.copy(status = InvitationStatus.ACCEPTED)
            every { staffRepository.save(any()) } returns expectedStaff

            // When
            val result = useCase.acceptInvitation(command)

            // Then
            assertEquals(StaffRole.CHIEF_GROOMER, result.role)
            assertEquals(StaffPermissions.fullAccess(), result.permissions)
        }

        @Test
        fun `should throw EntityNotFoundException when invitation not found`() {
            // Given
            val command =
                AcceptSalonInvitationCommand(
                    invitationId = InvitationId.generate(),
                    acceptingUserId = UserId.generate(),
                )

            every { invitationRepository.findById(command.invitationId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.acceptInvitation(command)
                }

            assertTrue(exception.message!!.contains("Invitation not found"))
            verify(exactly = 1) { invitationRepository.findById(command.invitationId) }
        }

        @Test
        fun `should throw BusinessRuleViolationException when invitation cannot be responded to`() {
            // Given
            val command =
                AcceptSalonInvitationCommand(
                    invitationId = InvitationId.generate(),
                    acceptingUserId = UserId.generate(),
                )

            val expiredInvitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(command.invitationId)
                    .withStatus(InvitationStatus.PENDING)
                    .withInvitedAt(LocalDateTime.now().minusDays(8)) // Invited 8 days ago
                    .withExpiresAt(LocalDateTime.now().minusDays(1)) // Expired 1 day ago
                    .build()

            every { invitationRepository.findById(command.invitationId) } returns expiredInvitation

            // When & Then
            val exception =
                assertThrows<BusinessRuleViolationException> {
                    useCase.acceptInvitation(command)
                }

            assertTrue(exception.message!!.contains("Invitation cannot be accepted"))
        }

        @Test
        fun `should throw EntityNotFoundException when accepting user not found`() {
            // Given
            val command =
                AcceptSalonInvitationCommand(
                    invitationId = InvitationId.generate(),
                    acceptingUserId = UserId.generate(),
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(command.invitationId)
                    .withStatus(InvitationStatus.PENDING)
                    .withExpiresAt(LocalDateTime.now().plusDays(1))
                    .build()

            every { invitationRepository.findById(command.invitationId) } returns invitation
            every { userRepository.findById(command.acceptingUserId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.acceptInvitation(command)
                }

            assertTrue(exception.message!!.contains("User not found"))
            verify(exactly = 1) { userRepository.findById(command.acceptingUserId) }
        }

        @Test
        fun `should throw BusinessRuleViolationException when phone numbers do not match`() {
            // Given
            val command =
                AcceptSalonInvitationCommand(
                    invitationId = InvitationId.generate(),
                    acceptingUserId = UserId.generate(),
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(command.invitationId)
                    .withInvitedUserPhone("+***********")
                    .withStatus(InvitationStatus.PENDING)
                    .withExpiresAt(LocalDateTime.now().plusDays(1))
                    .build()

            val acceptingUser =
                TestDataBuilder.aUser()
                    .withId(command.acceptingUserId)
                    .withPhoneNumber("+***********") // Different phone
                    .build()

            every { invitationRepository.findById(command.invitationId) } returns invitation
            every { userRepository.findById(command.acceptingUserId) } returns acceptingUser

            // When & Then
            val exception =
                assertThrows<UnauthorizedException> {
                    useCase.acceptInvitation(command)
                }

            assertTrue(exception.message!!.contains("phone number does not match"))
        }

        @Test
        fun `should throw BusinessRuleViolationException when user already associated with salon`() {
            // Given
            val command =
                AcceptSalonInvitationCommand(
                    invitationId = InvitationId.generate(),
                    acceptingUserId = UserId.generate(),
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(command.invitationId)
                    .withSalonId(SalonId.generate())
                    .withInvitedUserPhone("+***********")
                    .withStatus(InvitationStatus.PENDING)
                    .withExpiresAt(LocalDateTime.now().plusDays(1))
                    .build()

            val acceptingUser =
                TestDataBuilder.aUser()
                    .withId(command.acceptingUserId)
                    .withPhoneNumber("+***********")
                    .build()

            val existingStaff =
                TestDataBuilder.aStaff()
                    .withUserId(command.acceptingUserId)
                    .withSalonId(invitation.salonId)
                    .withActive(true)
                    .build()

            every { invitationRepository.findById(command.invitationId) } returns invitation
            every { userRepository.findById(command.acceptingUserId) } returns acceptingUser
            every { staffRepository.findByUserIdAndSalonId(command.acceptingUserId, invitation.salonId) } returns existingStaff

            // When & Then
            val exception =
                assertThrows<BusinessRuleViolationException> {
                    useCase.acceptInvitation(command)
                }

            assertTrue(exception.message!!.contains("User is already associated with this salon"))
        }
    }

    @Nested
    @DisplayName("Decline Invitation")
    inner class DeclineInvitationTests {
        @Test
        fun `should decline invitation successfully`() {
            // Given
            val invitationId = InvitationId.generate()
            val decliningUserId = UserId.generate()

            val command =
                DeclineSalonInvitationCommand(
                    invitationId = invitationId,
                    decliningUserId = decliningUserId,
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(invitationId)
                    .withStatus(InvitationStatus.PENDING)
                    .withExpiresAt(LocalDateTime.now().plusDays(1))
                    .build()

            val decliningUser =
                TestDataBuilder.aUser()
                    .withId(decliningUserId)
                    .withPhoneNumber(invitation.invitedUserPhone)
                    .build()

            val declinedInvitation =
                invitation.copy(
                    status = InvitationStatus.DECLINED,
                    respondedAt = LocalDateTime.now(),
                )

            every { invitationRepository.findById(invitationId) } returns invitation
            every { userRepository.findById(decliningUserId) } returns decliningUser
            every { invitationRepository.save(any()) } returns declinedInvitation

            // When
            val result = useCase.declineInvitation(command)

            // Then
            assertNotNull(result)
            assertEquals(InvitationStatus.DECLINED, result.status)
            assertNotNull(result.respondedAt)

            verify(exactly = 1) { invitationRepository.findById(invitationId) }
            verify(exactly = 1) { userRepository.findById(decliningUserId) }
            verify(exactly = 1) { invitationRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when invitation not found`() {
            // Given
            val command =
                DeclineSalonInvitationCommand(
                    invitationId = InvitationId.generate(),
                    decliningUserId = UserId.generate(),
                )

            every { invitationRepository.findById(command.invitationId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.declineInvitation(command)
                }

            assertTrue(exception.message!!.contains("Invitation not found"))
        }

        @Test
        fun `should throw BusinessRuleViolationException when invitation already responded to`() {
            // Given
            val command =
                DeclineSalonInvitationCommand(
                    invitationId = InvitationId.generate(),
                    decliningUserId = UserId.generate(),
                )

            val acceptedInvitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(command.invitationId)
                    .withStatus(InvitationStatus.ACCEPTED) // Already responded
                    .build()

            every { invitationRepository.findById(command.invitationId) } returns acceptedInvitation

            // When & Then
            val exception =
                assertThrows<BusinessRuleViolationException> {
                    useCase.declineInvitation(command)
                }

            assertTrue(exception.message!!.contains("Invitation cannot be declined"))
        }
    }

    @Nested
    @DisplayName("Cancel Invitation")
    inner class CancelInvitationTests {
        @Test
        fun `should cancel invitation successfully when user has permission`() {
            // Given
            val invitationId = InvitationId.generate()
            val cancellingUserId = UserId.generate()
            val salonId = SalonId.generate()

            val command =
                CancelSalonInvitationCommand(
                    invitationId = invitationId,
                    cancellingUserId = cancellingUserId,
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(invitationId)
                    .withSalonId(salonId)
                    .withInviterUserId(cancellingUserId) // Same user who sent it
                    .withStatus(InvitationStatus.PENDING)
                    .build()

            val cancellingUser =
                TestDataBuilder.aUser()
                    .withId(cancellingUserId)
                    .withRole(UserRole.STAFF)
                    .build()

            val cancellerStaff =
                TestDataBuilder.aStaff()
                    .withUserId(cancellingUserId)
                    .withSalonId(salonId)
                    .withRole(StaffRole.CHIEF_GROOMER)
                    .withActive(true)
                    .build()

            val cancelledInvitation =
                invitation.copy(
                    status = InvitationStatus.CANCELLED,
                    respondedAt = LocalDateTime.now(),
                )

            every { invitationRepository.findById(invitationId) } returns invitation
            every { userRepository.findById(cancellingUserId) } returns cancellingUser
            every { staffRepository.findByUserIdAndSalonId(cancellingUserId, salonId) } returns cancellerStaff
            every { invitationRepository.save(any()) } returns cancelledInvitation

            // When
            val result = useCase.cancelInvitation(command)

            // Then
            assertNotNull(result)
            assertEquals(InvitationStatus.CANCELLED, result.status)

            verify(exactly = 1) { invitationRepository.findById(invitationId) }
            verify(exactly = 1) { userRepository.findById(cancellingUserId) }
            verify(exactly = 1) { invitationRepository.save(any()) }
        }

        @Test
        fun `should allow admin to cancel any invitation`() {
            // Given
            val invitationId = InvitationId.generate()
            val adminUserId = UserId.generate()
            val originalInviterUserId = UserId.generate()

            val command =
                CancelSalonInvitationCommand(
                    invitationId = invitationId,
                    cancellingUserId = adminUserId,
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(invitationId)
                    .withInviterUserId(originalInviterUserId) // Different user
                    .withStatus(InvitationStatus.PENDING)
                    .build()

            val adminUser =
                TestDataBuilder.aUser()
                    .withId(adminUserId)
                    .withRole(UserRole.ADMIN) // Admin can cancel any invitation
                    .build()

            val cancelledInvitation = invitation.copy(status = InvitationStatus.CANCELLED)

            every { invitationRepository.findById(invitationId) } returns invitation
            every { userRepository.findById(adminUserId) } returns adminUser
            every { invitationRepository.save(any()) } returns cancelledInvitation

            // When
            val result = useCase.cancelInvitation(command)

            // Then
            assertEquals(InvitationStatus.CANCELLED, result.status)
        }

        @Test
        fun `should throw UnauthorizedException when user cannot cancel invitation`() {
            // Given
            val invitationId = InvitationId.generate()
            val cancellingUserId = UserId.generate()
            val originalInviterUserId = UserId.generate()
            val salonId = SalonId.generate()

            val command =
                CancelSalonInvitationCommand(
                    invitationId = invitationId,
                    cancellingUserId = cancellingUserId,
                )

            val invitation =
                TestDataBuilder.aSalonInvitation()
                    .withId(invitationId)
                    .withSalonId(salonId)
                    .withInviterUserId(originalInviterUserId) // Different user
                    .withStatus(InvitationStatus.PENDING)
                    .build()

            val cancellingUser =
                TestDataBuilder.aUser()
                    .withId(cancellingUserId)
                    .withRole(UserRole.STAFF) // Not admin
                    .build()

            val cancellerStaff =
                TestDataBuilder.aStaff()
                    .withUserId(cancellingUserId)
                    .withSalonId(salonId)
                    .withRole(StaffRole.GROOMER) // Not chief groomer
                    .withActive(true)
                    .build()

            every { invitationRepository.findById(invitationId) } returns invitation
            every { userRepository.findById(cancellingUserId) } returns cancellingUser
            every { staffRepository.findByUserIdAndSalonId(cancellingUserId, salonId) } returns cancellerStaff

            // When & Then
            val exception =
                assertThrows<UnauthorizedException> {
                    useCase.cancelInvitation(command)
                }

            assertTrue(exception.message!!.contains("does not have permission to cancel"))
        }
    }
}
