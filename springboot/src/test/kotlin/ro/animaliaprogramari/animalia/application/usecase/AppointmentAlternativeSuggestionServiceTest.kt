package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.application.command.ScheduleAppointmentCommand
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SchedulingConflictService
import java.time.LocalDate
import java.time.LocalTime

class AppointmentAlternativeSuggestionServiceTest {
    private val workingHoursRepository = mockk<WorkingHoursRepository>()
    private val staffWorkingHoursRepository = mockk<StaffWorkingHoursRepository>()
    private val appointmentRepository = mockk<AppointmentRepository>()
    private val blockTimeRepository = mockk<BlockTimeRepository>()
    private val staffRepository = mockk<StaffRepository>()
    private val schedulingConflictService = mockk<SchedulingConflictService>()

    private lateinit var service: AppointmentAlternativeSuggestionService

    @BeforeEach
    fun setUp() {
        service =
            AppointmentAlternativeSuggestionService(
                workingHoursRepository,
                staffWorkingHoursRepository,
                appointmentRepository,
                blockTimeRepository,
                staffRepository,
                schedulingConflictService,
            )
    }

    @Nested
    inner class FindAlternativeStaff {
        @Test
        fun `should find alternative staff when available at same time`() {
            // Given
            val salonId = SalonId.generate()
            val excludeStaffId = StaffId.generate()
            val alternativeStaffId = StaffId.generate()
            val date = LocalDate.now().plusDays(1)
            val startTime = LocalTime.of(10, 0)
            val endTime = LocalTime.of(11, 0)

            val alternativeStaff =
                Staff.createChiefGroomer(
                    userId = UserId.generate(),
                    salonId = salonId,
                ).copy(
                    id = alternativeStaffId,
                    nickname = "Alternative Staff",
                )

            every { staffRepository.findBySalonId(salonId) } returns listOf(alternativeStaff)
            every { workingHoursRepository.findBySalonId(salonId) } returns WorkingHoursSettings.createDefault(salonId)
            every {
                staffWorkingHoursRepository.findByStaffIdAndSalonId(alternativeStaffId, salonId)
            } returns StaffWorkingHoursSettings.createDefault(alternativeStaffId, salonId)
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, alternativeStaffId, date) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { schedulingConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returns true

            // When
            val result = service.findAlternativeStaff(date, startTime, endTime, salonId, excludeStaffId)

            // Then
            assertEquals(1, result.size)
            assertEquals(alternativeStaffId, result[0].id)
            assertEquals("Alternative Staff", result[0].nickname)
        }

        @Test
        fun `should return empty list when no staff available`() {
            // Given
            val salonId = SalonId.generate()
            val excludeStaffId = StaffId.generate()
            val date = LocalDate.now().plusDays(1)
            val startTime = LocalTime.of(10, 0)
            val endTime = LocalTime.of(11, 0)

            every { staffRepository.findBySalonId(salonId) } returns emptyList()
            every { workingHoursRepository.findBySalonId(salonId) } returns WorkingHoursSettings.createDefault(salonId)

            // When
            val result = service.findAlternativeStaff(date, startTime, endTime, salonId, excludeStaffId)

            // Then
            assertTrue(result.isEmpty())
        }
    }

    @Nested
    inner class SuggestAlternatives {
        @Test
        fun `should prioritize alternative staff over time adjustments`() {
            // Given
            val command =
                ScheduleAppointmentCommand(
                    salonId = SalonId.generate(),
                    staffId = StaffId.generate(),
                    clientId = ClientId.generate(),
                    petId = PetId.generate(),
                    appointmentDate = LocalDate.now().plusDays(1),
                    startTime = LocalTime.of(10, 0),
                    endTime = LocalTime.of(11, 0),
                    serviceIds = listOf(ServiceId.generate()),
                    notes = null,
                    repetitionFrequency = null,
                    clientName = "Test Client",
                    clientPhone = PhoneNumber.of("+40123456789"),
                    isNewClient = false,
                    petName = "Test Pet",
                    petSpecies = "Dog",
                    isNewPet = false,
                    petBreed = "Labrador",
                    petSize = "Large",
                )

            val alternativeStaff =
                Staff.createChiefGroomer(
                    userId = UserId.generate(),
                    salonId = command.salonId,
                ).copy(nickname = "Alternative Staff")

            // Mock alternative staff finding
            every { staffRepository.findBySalonId(command.salonId) } returns listOf(alternativeStaff)
            every {
                workingHoursRepository.findBySalonId(command.salonId)
            } returns WorkingHoursSettings.createDefault(command.salonId)
            every {
                staffWorkingHoursRepository.findByStaffIdAndSalonId(any(), command.salonId)
            } returns StaffWorkingHoursSettings.createDefault(alternativeStaff.id, command.salonId)
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { schedulingConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returns true

            // Mock original staff for time slot alternatives
            every { staffRepository.findById(command.staffId) } returns
                Staff.createChiefGroomer(
                    userId = UserId.generate(),
                    salonId = command.salonId,
                ).copy(nickname = "Original Staff")

            // When
            val result = service.suggestAlternatives(command, emptyList())

            // Then
            assertTrue(result.isNotEmpty())
            // First suggestion should be alternative staff (priority 1)
            assertEquals(AlternativeType.STAFF_ALTERNATIVE, result[0].type)
            assertEquals(1, result[0].priority)
            assertTrue(result[0].reason.contains("Personal alternativ disponibil"))
        }
    }
}
