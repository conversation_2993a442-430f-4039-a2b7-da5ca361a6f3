package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.application.command.DeleteUserAccountCommand
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import java.time.LocalDate

@DisplayName("Delete User Account Use Case Tests")
class DeleteUserAccountUseCaseTest {
    private lateinit var userRepository: UserRepository
    private lateinit var staffRepository: StaffRepository
    private lateinit var clientRepository: ClientRepository
    private lateinit var appointmentRepository: AppointmentRepository
    private lateinit var useCase: UserManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        userRepository = mockk()
        staffRepository = mockk()
        clientRepository = mockk()
        appointmentRepository = mockk()

        useCase =
            UserManagementUseCaseImpl(
                userRepository = userRepository,
                staffRepository = staffRepository,
                clientRepository = clientRepository,
                appointmentRepository = appointmentRepository,
            )
    }

    @Nested
    @DisplayName("Delete User Account")
    inner class DeleteUserAccountTests {
        @Test
        fun `should delete user account successfully when no staff associations exist`() {
            // Given
            val userId = UserId.generate()
            val user =
                TestDataBuilder.aUser()
                    .withId(userId)
                    .withName("Test User")
                    .build()

            val command =
                DeleteUserAccountCommand(
                    userId = userId,
                    confirmationText = "confirm",
                )

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findActiveByUserId(userId) } returns emptyList()
            every { clientRepository.findAll(userId = userId) } returns emptyList()
            every { userRepository.deleteById(userId) } returns true

            // When
            val result = useCase.deleteUserAccount(command)

            // Then
            assertTrue(result)
            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { staffRepository.findActiveByUserId(userId) }
            verify(exactly = 1) { clientRepository.findAll(userId = userId) }
            verify(exactly = 1) { userRepository.deleteById(userId) }
            // No appointments to delete since no staff associations
            verify(exactly = 0) { appointmentRepository.deleteById(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when user does not exist`() {
            // Given
            val userId = UserId.generate()
            val command =
                DeleteUserAccountCommand(
                    userId = userId,
                    confirmationText = "confirm",
                )

            every { userRepository.findById(userId) } returns null

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.deleteUserAccount(command)
            }

            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 0) { userRepository.deleteById(any()) }
        }

        @Test
        fun `should delete user account and all appointments when user has active appointments`() {
            // Given
            val userId = UserId.generate()
            val staffId = StaffId.generate()
            val appointmentId = AppointmentId.generate()

            val user =
                TestDataBuilder.aUser()
                    .withId(userId)
                    .build()

            val staff =
                TestDataBuilder.aStaff()
                    .withId(staffId.value)
                    .withUserId(userId)
                    .build()

            val futureAppointment =
                TestDataBuilder.anAppointment()
                    .withId(appointmentId)
                    .withStaffId(staffId)
                    .withDate(LocalDate.now().plusDays(1))
                    .withStatus(AppointmentStatus.SCHEDULED)
                    .build()

            val command =
                DeleteUserAccountCommand(
                    userId = userId,
                    confirmationText = "confirm",
                )

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findActiveByUserId(userId) } returns listOf(staff)
            every { appointmentRepository.findByStaffId(staffId, any(), any(), any()) } returns listOf(futureAppointment)
            every { appointmentRepository.deleteById(appointmentId) } returns true
            every { staffRepository.save(any()) } returns staff.deactivate()
            every { clientRepository.findAll(userId = userId) } returns emptyList()
            every { userRepository.deleteById(userId) } returns true

            // When
            val result = useCase.deleteUserAccount(command)

            // Then
            assertTrue(result)
            verify(exactly = 1) { userRepository.findById(userId) }
            verify(exactly = 1) { staffRepository.findActiveByUserId(userId) }
            verify(exactly = 1) { appointmentRepository.findByStaffId(staffId, any(), any(), any()) }
            verify(exactly = 1) { appointmentRepository.deleteById(appointmentId) }
            verify(exactly = 1) { staffRepository.save(any()) }
            verify(exactly = 1) { userRepository.deleteById(userId) }
        }

        @Test
        fun `should delete appointments and deactivate staff associations and client records before deleting user`() {
            // Given
            val userId = UserId.generate()
            val staffId = StaffId.generate()
            val clientId = ClientId.generate()
            val appointmentId = AppointmentId.generate()

            val user =
                TestDataBuilder.aUser()
                    .withId(userId)
                    .build()

            val staff =
                TestDataBuilder.aStaff()
                    .withId(staffId.value)
                    .withUserId(userId)
                    .build()

            val client =
                TestDataBuilder.aClient()
                    .withId(clientId)
                    .build()

            val appointment =
                TestDataBuilder.anAppointment()
                    .withId(appointmentId)
                    .withStaffId(staffId)
                    .build()

            val command =
                DeleteUserAccountCommand(
                    userId = userId,
                    confirmationText = "confirm",
                )

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findActiveByUserId(userId) } returns listOf(staff)
            every { appointmentRepository.findByStaffId(staffId, any(), any(), any()) } returns listOf(appointment)
            every { appointmentRepository.deleteById(appointmentId) } returns true
            every { clientRepository.findAll(userId = userId) } returns listOf(client)
            every { staffRepository.save(any()) } returns staff.deactivate()
            every { clientRepository.save(any()) } returns client.deactivate()
            every { userRepository.deleteById(userId) } returns true

            // When
            val result = useCase.deleteUserAccount(command)

            // Then
            assertTrue(result)
            verify(exactly = 1) { appointmentRepository.deleteById(appointmentId) }
            verify(exactly = 1) { staffRepository.save(any()) }
            verify(exactly = 1) { clientRepository.save(any()) }
            verify(exactly = 1) { userRepository.deleteById(userId) }
        }

        @Test
        fun `should return false when user deletion fails`() {
            // Given
            val userId = UserId.generate()
            val user =
                TestDataBuilder.aUser()
                    .withId(userId)
                    .build()

            val command =
                DeleteUserAccountCommand(
                    userId = userId,
                    confirmationText = "confirm",
                )

            every { userRepository.findById(userId) } returns user
            every { staffRepository.findActiveByUserId(userId) } returns emptyList()
            every { clientRepository.findAll(userId = userId) } returns emptyList()
            every { userRepository.deleteById(userId) } returns false

            // When
            val result = useCase.deleteUserAccount(command)

            // Then
            assertFalse(result)
            verify(exactly = 1) { userRepository.deleteById(userId) }
        }
    }
}
