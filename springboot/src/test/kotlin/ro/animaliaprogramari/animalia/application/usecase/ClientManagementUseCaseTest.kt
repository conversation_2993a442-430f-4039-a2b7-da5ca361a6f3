package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.command.ActivateClientCommand
import ro.animaliaprogramari.animalia.application.command.DeactivateClientCommand
import ro.animaliaprogramari.animalia.application.command.RegisterClientCommand
import ro.animaliaprogramari.animalia.application.command.UpdateClientCommand
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.DomainEventPublisher
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.query.GetClientByEmailQuery
import ro.animaliaprogramari.animalia.application.query.GetClientByIdQuery
import ro.animaliaprogramari.animalia.application.query.GetClientsByGroomerQuery
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * Unit tests for ClientManagementUseCaseImpl
 * Tests the client management business logic without external dependencies
 */
@DisplayName("ClientManagementUseCase")
class ClientManagementUseCaseTest {
    // Mocked dependencies
    private val clientRepository = mockk<ClientRepository>()
    private val appointmentRepository = mockk<AppointmentRepository>()
    private val petRepository = mockk<PetRepository>()
    private val salonRepository = mockk<SalonRepository>()
    private val domainEventPublisher = mockk<DomainEventPublisher>()

    // System under test
    private lateinit var useCase: ClientManagementUseCaseImpl

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase =
            ClientManagementUseCaseImpl(
                clientRepository = clientRepository,
                appointmentRepository = appointmentRepository,
                petRepository = petRepository,
                salonRepository = salonRepository,
                domainEventPublisher = domainEventPublisher,
            )
    }

    @Nested
    @DisplayName("Register Client")
    inner class RegisterClientTests {
        @Test
        fun `should register client successfully with valid data`() {
            // Given
            val command =
                RegisterClientCommand(
                    name = "John Doe",
                    phone = PhoneNumber.of("+40731446895"),
                    email = Email.of("<EMAIL>"),
                    address = "123 Main St, Bucharest",
                    notes = "VIP client",
                )

            val expectedClient =
                TestDataBuilder.aClient()
                    .withName(command.name)
                    .withPhone(command.phone)
                    .withEmail(command.email)
                    .withAddress(command.address)
                    .withNotes(command.notes)
                    .build()

            every { clientRepository.save(any()) } returns expectedClient
            every { domainEventPublisher.publish(any()) } just Runs

            // When
            val result = useCase.registerClient(command)

            // Then
            assertNotNull(result)
            assertEquals(command.name, result.name)
            assertEquals(command.phone, result.phone)
            assertEquals(command.email, result.email)
            assertEquals(command.address, result.address)
            assertEquals(command.notes, result.notes)
            assertTrue(result.isActive)

            verify(exactly = 1) { clientRepository.save(any()) }
            verify(exactly = 1) { domainEventPublisher.publish(any()) }
        }

        @Test
        fun `should register client with minimal data`() {
            // Given
            val command =
                RegisterClientCommand(
                    name = "Jane Smith",
                    phone = PhoneNumber.of("+40712345679"),
                    email = null,
                    address = null,
                    notes = null,
                )

            val expectedClient =
                TestDataBuilder.aClient()
                    .withName(command.name)
                    .withPhone(command.phone)
                    .withEmail(null)
                    .withAddress(null)
                    .withNotes(null)
                    .build()

            every { clientRepository.save(any()) } returns expectedClient
            every { domainEventPublisher.publish(any()) } just Runs

            // When
            val result = useCase.registerClient(command)

            // Then
            assertNotNull(result)
            assertEquals(command.name, result.name)
            assertEquals(command.phone, result.phone)
            assertNull(result.email)
            assertNull(result.address)
            assertNull(result.notes)

            verify(exactly = 1) { clientRepository.save(any()) }
            verify(exactly = 1) { domainEventPublisher.publish(any()) }
        }
    }

    @Nested
    @DisplayName("Update Client")
    inner class UpdateClientTests {
        @Test
        fun `should update client successfully when client exists`() {
            // Given
            val clientId = ClientId.generate()
            val command =
                UpdateClientCommand(
                    clientId = clientId,
                    name = "Updated Name",
                    phone = PhoneNumber.of("+40731446895"),
                    email = Email.of("<EMAIL>"),
                    address = "Updated Address",
                    notes = "Updated notes",
                )

            val existingClient =
                TestDataBuilder.aClient()
                    .withId(clientId)
                    .withName("Original Name")
                    .build()

            val updatedClient =
                existingClient.update(
                    name = command.name,
                    phone = command.phone,
                    email = command.email,
                    address = command.address,
                    notes = command.notes,
                )

            every { clientRepository.findById(clientId) } returns existingClient
            every { clientRepository.save(any()) } returns updatedClient

            // When
            val result = useCase.updateClient(command)

            // Then
            assertNotNull(result)
            assertEquals(command.name, result.name)
            assertEquals(command.phone, result.phone)
            assertEquals(command.email, result.email)
            assertEquals(command.address, result.address)
            assertEquals(command.notes, result.notes)

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 1) { clientRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when client not found for update`() {
            // Given
            val clientId = ClientId.generate()
            val command =
                UpdateClientCommand(
                    clientId = clientId,
                    name = "Updated Name",
                    phone = PhoneNumber.of("+40731446895"),
                    email = Email.of("<EMAIL>"),
                    address = "Updated Address",
                    notes = "Updated notes",
                )

            every { clientRepository.findById(clientId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.updateClient(command)
                }

            assertTrue(exception.message!!.contains("Client"))
            assertTrue(exception.message!!.contains(clientId.value))

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 0) { clientRepository.save(any()) }
        }

        @Test
        fun `should update client with partial data`() {
            // Given
            val clientId = ClientId.generate()
            val command =
                UpdateClientCommand(
                    clientId = clientId,
                    name = "New Name Only",
                    phone = null,
                    email = null,
                    address = null,
                    notes = null,
                )

            val existingClient =
                TestDataBuilder.aClient()
                    .withId(clientId)
                    .withName("Original Name")
                    .withPhone(PhoneNumber.of("+40731446895"))
                    .build()

            val updatedClient =
                existingClient.update(
                    name = command.name,
                    phone = command.phone,
                    email = command.email,
                    address = command.address,
                    notes = command.notes,
                )

            every { clientRepository.findById(clientId) } returns existingClient
            every { clientRepository.save(any()) } returns updatedClient

            // When
            val result = useCase.updateClient(command)

            // Then
            assertNotNull(result)
            assertEquals(command.name, result.name)

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 1) { clientRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Deactivate Client")
    inner class DeactivateClientTests {
        @Test
        fun `should deactivate client successfully when client exists`() {
            // Given
            val clientId = ClientId.generate()
            val command = DeactivateClientCommand(clientId = clientId)

            val activeClient =
                TestDataBuilder.aClient()
                    .withId(clientId)
                    .withActive(true)
                    .build()

            val deactivatedClient = activeClient.deactivate()

            every { clientRepository.findById(clientId) } returns activeClient
            every { clientRepository.save(any()) } returns deactivatedClient

            // When
            val result = useCase.deactivateClient(command)

            // Then
            assertNotNull(result)
            assertEquals(clientId, result.id)
            assertEquals(false, result.isActive)

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 1) { clientRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when client not found for deactivation`() {
            // Given
            val clientId = ClientId.generate()
            val command = DeactivateClientCommand(clientId = clientId)

            every { clientRepository.findById(clientId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.deactivateClient(command)
                }

            assertTrue(exception.message!!.contains("Client"))
            assertTrue(exception.message!!.contains(clientId.value))

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 0) { clientRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Activate Client")
    inner class ActivateClientTests {
        @Test
        fun `should activate client successfully when client exists`() {
            // Given
            val clientId = ClientId.generate()
            val command = ActivateClientCommand(clientId = clientId)

            val inactiveClient =
                TestDataBuilder.aClient()
                    .withId(clientId)
                    .withActive(false)
                    .build()

            val activatedClient = inactiveClient.activate()

            every { clientRepository.findById(clientId) } returns inactiveClient
            every { clientRepository.save(any()) } returns activatedClient

            // When
            val result = useCase.activateClient(command)

            // Then
            assertNotNull(result)
            assertEquals(clientId, result.id)
            assertEquals(true, result.isActive)

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 1) { clientRepository.save(any()) }
        }

        @Test
        fun `should throw EntityNotFoundException when client not found for activation`() {
            // Given
            val clientId = ClientId.generate()
            val command = ActivateClientCommand(clientId = clientId)

            every { clientRepository.findById(clientId) } returns null

            // When & Then
            val exception =
                assertThrows<EntityNotFoundException> {
                    useCase.activateClient(command)
                }

            assertTrue(exception.message!!.contains("Client"))
            assertTrue(exception.message!!.contains(clientId.value))

            verify(exactly = 1) { clientRepository.findById(clientId) }
            verify(exactly = 0) { clientRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("Get Client by ID")
    inner class GetClientByIdTests {
        @Test
        fun `should return client when found by ID`() {
            // Given
            val clientId = ClientId.generate()
            val query = GetClientByIdQuery(clientId = clientId)

            val client =
                TestDataBuilder.aClient()
                    .withId(clientId)
                    .build()

            every { clientRepository.findById(clientId) } returns client

            // When
            val result = useCase.getClientById(query)

            // Then
            assertNotNull(result)
            assertEquals(clientId, result!!.id)
            assertEquals(client.name, result.name)

            verify(exactly = 1) { clientRepository.findById(clientId) }
        }

        @Test
        fun `should return null when client not found by ID`() {
            // Given
            val clientId = ClientId.generate()
            val query = GetClientByIdQuery(clientId = clientId)

            every { clientRepository.findById(clientId) } returns null

            // When
            val result = useCase.getClientById(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { clientRepository.findById(clientId) }
        }
    }

    @Nested
    @DisplayName("Get Client by Email")
    inner class GetClientByEmailTests {
        @Test
        fun `should return client when found by email`() {
            // Given
            val email = Email.of("<EMAIL>")
            val query = GetClientByEmailQuery(email = email)

            val client =
                TestDataBuilder.aClient()
                    .withEmail(email)
                    .build()

            every { clientRepository.findByEmail(email) } returns client

            // When
            val result = useCase.getClientByEmail(query)

            // Then
            assertNotNull(result)
            assertEquals(email, result!!.email)
            assertEquals(client.id, result.id)

            verify(exactly = 1) { clientRepository.findByEmail(email) }
        }

        @Test
        fun `should return null when client not found by email`() {
            // Given
            val email = Email.of("<EMAIL>")
            val query = GetClientByEmailQuery(email = email)

            every { clientRepository.findByEmail(email) } returns null

            // When
            val result = useCase.getClientByEmail(query)

            // Then
            assertNull(result)

            verify(exactly = 1) { clientRepository.findByEmail(email) }
        }
    }

    @Nested
    @DisplayName("Get Clients by Groomer")
    inner class GetClientsByGroomerTests {
        @Test
        fun `should return clients associated with groomer`() {
            // Given
            val userId = UserId.generate()
            val query =
                GetClientsByGroomerQuery(
                    userId = userId,
                    isActive = true,
                    limit = 15,
                    offset = 5,
                )

            val clients =
                listOf(
                    TestDataBuilder.aClient().withName("Groomer Client 1").withActive(true).build(),
                    TestDataBuilder.aClient().withName("Groomer Client 2").withActive(true).build(),
                )

            every {
                clientRepository.findByGroomer(
                    userId = userId,
                    isActive = true,
                    limit = 15,
                    offset = 5,
                )
            } returns clients

            // When
            val result = useCase.getClientsByGroomer(query)

            // Then
            assertEquals(2, result.size)
            assertTrue(result.all { it.isActive })

            verify(exactly = 1) { clientRepository.findByGroomer(userId, true, 15, 5) }
        }

        @Test
        fun `should return empty list when groomer has no clients`() {
            // Given
            val userId = UserId.generate()
            val query =
                GetClientsByGroomerQuery(
                    userId = userId,
                    isActive = true,
                    limit = 10,
                    offset = 0,
                )

            every {
                clientRepository.findByGroomer(
                    userId = userId,
                    isActive = true,
                    limit = 10,
                    offset = 0,
                )
            } returns emptyList()

            // When
            val result = useCase.getClientsByGroomer(query)

            // Then
            assertTrue(result.isEmpty())

            verify(exactly = 1) { clientRepository.findByGroomer(userId, true, 10, 0) }
        }
    }
}
