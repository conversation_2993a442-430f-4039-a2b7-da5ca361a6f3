package ro.animaliaprogramari.animalia.infrastructure.scheduler

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AppointmentCompletionSchedulerTest {

    private lateinit var appointmentRepository: AppointmentRepository
    private lateinit var scheduler: AppointmentCompletionScheduler

    @BeforeEach
    fun setUp() {
        appointmentRepository = mockk()
        scheduler = AppointmentCompletionScheduler(appointmentRepository)
    }

    @Test
    fun `should complete past appointments with eligible statuses`() {
        // Given
        val pastDate = LocalDate.now().minusDays(1)
        val pastTime = LocalTime.of(10, 0)
        val endTime = LocalTime.of(11, 0)

        val pastScheduledAppointment = createTestAppointment(
            status = AppointmentStatus.SCHEDULED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        val pastConfirmedAppointment = createTestAppointment(
            status = AppointmentStatus.CONFIRMED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        val pastRescheduledAppointment = createTestAppointment(
            status = AppointmentStatus.RESCHEDULED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        val pastInProgressAppointment = createTestAppointment(
            status = AppointmentStatus.IN_PROGRESS,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        val appointments = listOf(
            pastScheduledAppointment,
            pastConfirmedAppointment,
            pastRescheduledAppointment,
            pastInProgressAppointment
        )

        every { appointmentRepository.findByStatuses(any()) } returns appointments

        val savedAppointmentSlot = slot<Appointment>()
        every { appointmentRepository.save(capture(savedAppointmentSlot)) } returnsArgument 0

        // When
        scheduler.completePastAppointments()

        // Then
        verify(exactly = 1) { 
            appointmentRepository.findByStatuses(
                listOf(
                    AppointmentStatus.SCHEDULED,
                    AppointmentStatus.CONFIRMED,
                    AppointmentStatus.IN_PROGRESS,
                    AppointmentStatus.RESCHEDULED
                )
            )
        }

        verify(exactly = 4) { appointmentRepository.save(any()) }

        // Verify all saved appointments have COMPLETED status
        val savedAppointments = savedAppointmentSlot.captured
        assertEquals(AppointmentStatus.COMPLETED, savedAppointments.status)
    }

    @Test
    fun `should not complete future appointments`() {
        // Given
        val futureDate = LocalDate.now().plusDays(1)
        val futureTime = LocalTime.of(10, 0)
        val endTime = LocalTime.of(11, 0)

        val futureAppointment = createTestAppointment(
            status = AppointmentStatus.SCHEDULED,
            date = futureDate,
            startTime = futureTime,
            endTime = endTime
        )

        every { appointmentRepository.findByStatuses(any()) } returns listOf(futureAppointment)

        // When
        scheduler.completePastAppointments()

        // Then
        verify(exactly = 0) { appointmentRepository.save(any()) }
    }

    @Test
    fun `should not complete appointments with ineligible statuses`() {
        // Given
        val pastDate = LocalDate.now().minusDays(1)
        val pastTime = LocalTime.of(10, 0)
        val endTime = LocalTime.of(11, 0)

        val cancelledAppointment = createTestAppointment(
            status = AppointmentStatus.CANCELLED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        val completedAppointment = createTestAppointment(
            status = AppointmentStatus.COMPLETED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        val noShowAppointment = createTestAppointment(
            status = AppointmentStatus.NO_SHOW,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        // These appointments should not be returned by findByStatuses since they're not in the eligible list
        every { appointmentRepository.findByStatuses(any()) } returns emptyList()

        // When
        scheduler.completePastAppointments()

        // Then
        verify(exactly = 0) { appointmentRepository.save(any()) }
    }

    @Test
    fun `should handle appointments that are exactly at current time`() {
        // Given
        val now = LocalDateTime.now()
        val currentDate = now.toLocalDate()
        val currentTime = now.toLocalTime()
        val endTime = currentTime.minusMinutes(1) // Appointment ended 1 minute ago

        val justFinishedAppointment = createTestAppointment(
            status = AppointmentStatus.SCHEDULED,
            date = currentDate,
            startTime = currentTime.minusHours(1),
            endTime = endTime
        )

        every { appointmentRepository.findByStatuses(any()) } returns listOf(justFinishedAppointment)
        every { appointmentRepository.save(any()) } returnsArgument 0

        // When
        scheduler.completePastAppointments()

        // Then
        verify(exactly = 1) { appointmentRepository.save(any()) }
    }

    @Test
    fun `should handle repository exceptions gracefully`() {
        // Given
        every { appointmentRepository.findByStatuses(any()) } throws RuntimeException("Database error")

        // When & Then - Should not throw exception
        scheduler.completePastAppointments()

        // Verify that save was never called due to the exception
        verify(exactly = 0) { appointmentRepository.save(any()) }
    }

    @Test
    fun `should handle save exceptions gracefully and continue processing other appointments`() {
        // Given
        val pastDate = LocalDate.now().minusDays(1)
        val pastTime = LocalTime.of(10, 0)
        val endTime = LocalTime.of(11, 0)

        val appointment1 = createTestAppointment(
            id = "appointment-1",
            status = AppointmentStatus.SCHEDULED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        val appointment2 = createTestAppointment(
            id = "appointment-2",
            status = AppointmentStatus.CONFIRMED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime
        )

        every { appointmentRepository.findByStatuses(any()) } returns listOf(appointment1, appointment2)

        // First save fails, second succeeds
        every { appointmentRepository.save(match { it.id.value == "appointment-1" }) } throws RuntimeException("Save failed")
        every { appointmentRepository.save(match { it.id.value == "appointment-2" }) } returnsArgument 0

        // When
        scheduler.completePastAppointments()

        // Then
        verify(exactly = 2) { appointmentRepository.save(any()) }
    }

    @Test
    fun `should verify correct status list is passed to repository`() {
        // Given
        val statusSlot = slot<List<AppointmentStatus>>()
        every { appointmentRepository.findByStatuses(capture(statusSlot)) } returns emptyList()

        // When
        scheduler.completePastAppointments()

        // Then
        val capturedStatuses = statusSlot.captured
        assertEquals(4, capturedStatuses.size)
        assertTrue(capturedStatuses.contains(AppointmentStatus.SCHEDULED))
        assertTrue(capturedStatuses.contains(AppointmentStatus.CONFIRMED))
        assertTrue(capturedStatuses.contains(AppointmentStatus.IN_PROGRESS))
        assertTrue(capturedStatuses.contains(AppointmentStatus.RESCHEDULED))
    }

    @Test
    fun `should complete appointments that ended exactly now`() {
        // Given
        val now = LocalDateTime.now()
        val currentDate = now.toLocalDate()
        val currentTime = now.toLocalTime()

        val appointmentEndingNow = createTestAppointment(
            status = AppointmentStatus.SCHEDULED,
            date = currentDate,
            startTime = currentTime.minusHours(1),
            endTime = currentTime // Ends exactly now
        )

        every { appointmentRepository.findByStatuses(any()) } returns listOf(appointmentEndingNow)
        every { appointmentRepository.save(any()) } returnsArgument 0

        // When
        scheduler.completePastAppointments()

        // Then
        verify(exactly = 1) { appointmentRepository.save(any()) }
    }

    @Test
    fun `should add completion notes when completing appointments`() {
        // Given
        val pastDate = LocalDate.now().minusDays(1)
        val pastTime = LocalTime.of(10, 0)
        val endTime = LocalTime.of(11, 0)

        val appointment = createTestAppointment(
            status = AppointmentStatus.SCHEDULED,
            date = pastDate,
            startTime = pastTime,
            endTime = endTime,
            notes = "Original notes"
        )

        every { appointmentRepository.findByStatuses(any()) } returns listOf(appointment)

        val savedAppointmentSlot = slot<Appointment>()
        every { appointmentRepository.save(capture(savedAppointmentSlot)) } returnsArgument 0

        // When
        scheduler.completePastAppointments()

        // Then
        val savedAppointment = savedAppointmentSlot.captured
        assertEquals(AppointmentStatus.COMPLETED, savedAppointment.status)
        assertTrue(savedAppointment.notes?.contains("Auto-completed by scheduler") == true)
    }

    private fun createTestAppointment(
        id: String = "test-appointment-id",
        status: AppointmentStatus,
        date: LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        notes: String? = "Test appointment"
    ): Appointment {
        return Appointment(
            id = AppointmentId.of(id),
            salonId = SalonId.of("test-salon"),
            clientId = ClientId.of("test-client"),
            petId = PetId.of("test-pet"),
            staffId = StaffId.of("test-staff"),
            appointmentDate = date,
            startTime = startTime,
            endTime = endTime,
            status = status,
            serviceIds = listOf(ServiceId.of("test-service")),
            totalPrice = Money.of(50.0),
            totalDuration = Duration.ofMinutes(60),
            notes = notes,
            repetitionFrequency = null,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            version = 0
        )
    }
}
