package ro.animaliaprogramari.animalia.test

import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SpecializationEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffRoleEntity
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

/**
 * Test data builders for creating domain objects in tests
 * Provides fluent API for test data creation with sensible defaults
 */
object TestDataBuilder {
    // ===== APPOINTMENT BUILDERS =====

    fun anAppointment(): AppointmentBuilder = AppointmentBuilder()

    class AppointmentBuilder {
        private var id: AppointmentId = AppointmentId.generate()
        private var salonId: SalonId = SalonId.generate()
        private var clientId: ClientId = ClientId.generate()
        private var petId: PetId = PetId.generate()
        private var staffId: StaffId = StaffId.generate()
        private var appointmentDate: LocalDate = LocalDate.now().plusDays(1)
        private var startTime: LocalTime = LocalTime.of(10, 0)
        private var endTime: LocalTime = LocalTime.of(11, 0)
        private var status: AppointmentStatus = AppointmentStatus.SCHEDULED
        private var serviceIds: List<ServiceId> = listOf(ServiceId.generate())
        private var totalPrice: Money = Money.of(BigDecimal("50.00"))
        private var totalDuration: Duration = Duration.ofMinutes(60)
        private var notes: String? = null
        private var repetitionFrequency: RepetitionFrequency? = null
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: AppointmentId) = apply { this.id = id }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withClientId(clientId: ClientId) = apply { this.clientId = clientId }

        fun withPetId(petId: PetId) = apply { this.petId = petId }

        fun withStaffId(staffId: StaffId) = apply { this.staffId = staffId }

        fun withDate(date: LocalDate) = apply { this.appointmentDate = date }

        fun withStartTime(time: LocalTime) = apply { this.startTime = time }

        fun withEndTime(time: LocalTime) = apply { this.endTime = time }

        fun withTimeSlot(
            start: LocalTime,
            end: LocalTime,
        ) = apply {
            this.startTime = start
            this.endTime = end
        }

        fun withStatus(status: AppointmentStatus) = apply { this.status = status }

        fun withServiceIds(serviceIds: List<ServiceId>) = apply { this.serviceIds = serviceIds }

        fun withPrice(price: Money) = apply { this.totalPrice = price }

        fun withDuration(duration: Duration) = apply { this.totalDuration = duration }

        fun withNotes(notes: String?) = apply { this.notes = notes }

        fun withRepetition(frequency: RepetitionFrequency?) = apply { this.repetitionFrequency = frequency }

        fun build(): Appointment =
            Appointment(
                id = id,
                salonId = salonId,
                clientId = clientId,
                petId = petId,
                staffId = staffId,
                appointmentDate = appointmentDate,
                startTime = startTime,
                endTime = endTime,
                status = status,
                serviceIds = serviceIds,
                totalPrice = totalPrice,
                totalDuration = totalDuration,
                notes = notes,
                repetitionFrequency = repetitionFrequency,
                createdAt = createdAt,
                updatedAt = updatedAt,
                version = 0,
            )
    }

    // ===== CLIENT BUILDERS =====

    fun aClient(): ClientBuilder = ClientBuilder()

    class ClientBuilder {
        private var id: ClientId = ClientId.generate()
        private var name: String = "John Doe"
        private var phone: PhoneNumber? = PhoneNumber.of("+40731446895")
        private var email: Email? = Email.of("<EMAIL>")
        private var address: String? = "123 Main St, Bucharest"
        private var notes: String? = null
        private var isActive: Boolean = true
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: ClientId) = apply { this.id = id }

        fun withName(name: String) = apply { this.name = name }

        fun withPhone(phone: PhoneNumber?) = apply { this.phone = phone }

        fun withEmail(email: Email?) = apply { this.email = email }

        fun withAddress(address: String?) = apply { this.address = address }

        fun withNotes(notes: String?) = apply { this.notes = notes }

        fun withActive(active: Boolean) = apply { this.isActive = active }

        fun build(): Client =
            Client(
                id = id,
                salonId = null,
                name = name,
                phone = phone,
                email = email,
                address = address,
                notes = notes,
                isActive = isActive,
                createdAt = createdAt,
                updatedAt = updatedAt,
                )
    }

    // ===== PET BUILDERS =====

    fun aPet(): PetBuilder = PetBuilder()

    class PetBuilder {
        private var id: PetId = PetId.generate()
        private var clientId: ClientId = ClientId.generate()
        private var name: String = "Buddy"
        private var breed: String? = "Golden Retriever"
        private var age: Int? = 3
        private var weight: BigDecimal? = BigDecimal("25.5")
        private var color: String? = "Golden"
        private var gender: Gender? = Gender.MALE
        private var notes: String? = null
        private var medicalConditions: String? = null
        private var size: String = "M"
        private var species: String = "Dog"
        private var isActive: Boolean = true
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()
        private var petSize: String? = "M"
        private var petSpecies: String? = "Dog"

        fun withId(id: PetId) = apply { this.id = id }

        fun withClientId(clientId: ClientId) = apply { this.clientId = clientId }

        fun withName(name: String) = apply { this.name = name }

        fun withBreed(breed: String?) = apply { this.breed = breed }

        fun withAge(age: Int?) = apply { this.age = age }

        fun withWeight(weight: BigDecimal?) = apply { this.weight = weight }

        fun withColor(color: String?) = apply { this.color = color }

        fun withGender(gender: Gender?) = apply { this.gender = gender }

        fun withNotes(notes: String?) = apply { this.notes = notes }

        fun withMedicalConditions(conditions: String?) = apply { this.medicalConditions = conditions }

        fun withSize(size: String) = apply { this.size = size }

        fun withSpecies(species: String) = apply { this.species = species }

        fun withActive(active: Boolean) = apply { this.isActive = active }

        fun withPetSize(size: String?) = apply { this.petSize = size }

        fun withPetSpecies(species: String?) = apply { this.petSpecies = species }

        fun build(): Pet =
            Pet(
                id = id,
                clientId = clientId,
                name = name,
                breed = breed,
                age = age,
                weight = weight,
                color = color,
                gender = gender,
                notes = notes,
                medicalConditions = medicalConditions,
                isActive = isActive,
                createdAt = createdAt,
                updatedAt = updatedAt,
                size = size,
                species = species,
                photoUrl = null,
            )
    }

    // ===== USER BUILDERS =====

    fun aUser(): UserBuilder = UserBuilder()

    class UserBuilder {
        private var id: UserId = UserId.generate()
        private var firebaseUid: String = "firebase_uid_123"
        private var email: Email? = Email.of("<EMAIL>")
        private var phoneNumber: String? = "+40731446895"
        private var name: String = "John Staff"
        private var role: UserRole = UserRole.STAFF
        private var currentSalonId: SalonId? = null
        private var isActive: Boolean = true
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: UserId) = apply { this.id = id }

        fun withFirebaseUid(uid: String) = apply { this.firebaseUid = uid }

        fun withEmail(email: Email?) = apply { this.email = email }

        fun withPhone(phone: String?) = apply { this.phoneNumber = phone }

        fun withName(name: String) = apply { this.name = name }

        fun withRole(role: UserRole) = apply { this.role = role }

        fun withPhoneNumber(phoneNumber: String) = apply { this.phoneNumber = phoneNumber }

        fun withCurrentSalonId(salonId: SalonId?) = apply { this.currentSalonId = salonId }

        fun withActive(active: Boolean) = apply { this.isActive = active }

        fun build(): User =
            User(
                id = id,
                firebaseUid = firebaseUid,
                email = email,
                phoneNumber = phoneNumber,
                name = name,
                role = role,
                currentSalonId = currentSalonId,
                isActive = isActive,
                createdAt = createdAt,
                updatedAt = updatedAt,
            )
    }

    // ===== SALON BUILDERS =====

    fun aSalon(): SalonBuilder = SalonBuilder()

    class SalonBuilder {
        private var id: SalonId = SalonId.generate()
        private var name: String = "Pet Paradise Salon"
        private var address: String? = "456 Pet Street, Bucharest"
        private var city: String? = "Bucharest"
        private var phone: PhoneNumber? = PhoneNumber.of("+40212345678")
        private var email: Email? = Email.of("<EMAIL>")
        private var ownerId: UserId = UserId.generate()
        private var clientIds: Set<ClientId> = emptySet()
        private var description: String? = "A premium pet grooming salon"
        private var isActive: Boolean = true
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: SalonId) = apply { this.id = id }

        fun withName(name: String) = apply { this.name = name }

        fun withAddress(address: String?) = apply { this.address = address }

        fun withCity(city: String?) = apply { this.city = city }

        fun withPhone(phone: PhoneNumber?) = apply { this.phone = phone }

        fun withEmail(email: Email?) = apply { this.email = email }

        fun withOwnerId(ownerId: UserId) = apply { this.ownerId = ownerId }

        fun withClientIds(clientIds: Set<ClientId>) = apply { this.clientIds = clientIds }

        fun withDescription(description: String?) = apply { this.description = description }

        fun withActive(active: Boolean) = apply { this.isActive = active }

        fun build(): Salon =
            Salon(
                id = id,
                name = name,
                address = address,
                city = city,
                phone = phone,
                email = email,
                ownerId = ownerId,
                isActive = isActive,
                createdAt = createdAt,
                updatedAt = updatedAt,
                description = description,
            )
    }

    // ===== SALON SERVICE BUILDERS =====

    fun aSalonService(): SalonServiceBuilder = SalonServiceBuilder()

    class SalonServiceBuilder {
        private var id: ServiceId = ServiceId.generate()
        private var salonId: SalonId = SalonId.generate()
        private var name: String = "Basic Grooming"
        private var description: String? = "Basic grooming service"
        private var basePrice: Money = Money.of(BigDecimal("50.00"))
        private var duration: Duration = Duration.ofMinutes(60)
        private var category: ServiceCategory = ServiceCategory.GROOMING
        private var isActive: Boolean = true
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: ServiceId) = apply { this.id = id }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withName(name: String) = apply { this.name = name }

        fun withDescription(description: String?) = apply { this.description = description }

        fun withPrice(price: Money) = apply { this.basePrice = price }

        fun withDuration(duration: Duration) =
            apply {
                require(duration.minutes > 0) { "Duration must be positive" }
                this.duration = duration
            }

        fun withCategory(category: ServiceCategory) = apply { this.category = category }

        fun withActive(active: Boolean) = apply { this.isActive = active }

        fun build(): SalonService =
            SalonService(
                id = id,
                salonId = salonId,
                name = name,
                description = description,
                basePrice = basePrice,
                duration = duration,
                category = category,
                isActive = isActive,
                createdAt = createdAt,
                updatedAt = updatedAt,
            )
    }

    // ===== STAFF BUILDERS =====

    fun aStaff(): StaffBuilder = StaffBuilder()

    class StaffBuilder {
        private var id: String? = null
        private var userId: UserId = UserId.generate()
        private var salonId: SalonId = SalonId.generate()
        private var role: StaffRole = StaffRole.GROOMER
        private var permissions: StaffPermissions = StaffPermissions.defaultGroomerAccess()
        private var isActive: Boolean = true
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: String?) = apply { this.id = id }

        fun withUserId(userId: UserId) = apply { this.userId = userId }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withRole(role: StaffRole) = apply { this.role = role }

        fun withPermissions(permissions: StaffPermissions) = apply { this.permissions = permissions }

        fun withActive(active: Boolean) = apply { this.isActive = active }

        fun build(): Staff =
            Staff(
                id = id?.let { StaffId.of(it) } ?: StaffId.generate(),
                userId = userId,
                salonId = salonId,
                role = role,
                permissions = permissions,
                isActive = isActive,
                specializations = emptySet(),
                createdAt = createdAt,
                updatedAt = updatedAt,
                nickname = null,
            )
    }

    // ===== STAFF ENTITY BUILDERS =====

    fun aStaffEntity(): StaffEntityBuilder = StaffEntityBuilder()

    class StaffEntityBuilder {
        private var id: String = UUID.randomUUID().toString()
        private var userId: String = UUID.randomUUID().toString()
        private var salonId: String = UUID.randomUUID().toString()
        private var role: StaffRoleEntity = StaffRoleEntity.GROOMER
        private var workingHours: String = "{}"
        private var hireDate: LocalDate = LocalDate.now()
        private var specializations: Set<SpecializationEntity> = emptySet()
        private var isActive: Boolean = true
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: StaffId) = apply { this.id = id.value }

        fun withUserId(userId: UserId) = apply { this.userId = userId.value }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId.value }

        fun withRole(role: StaffRoleEntity) = apply { this.role = role }

        fun withWorkingHours(workingHours: String) = apply { this.workingHours = workingHours }

        fun withHireDate(hireDate: LocalDate) = apply { this.hireDate = hireDate }

        fun withSpecializations(specializations: Set<SpecializationEntity>) =
            apply { this.specializations = specializations }

        fun withActive(active: Boolean) = apply { this.isActive = active }

        fun withCreatedAt(createdAt: LocalDateTime) = apply { this.createdAt = createdAt }

        fun withUpdatedAt(updatedAt: LocalDateTime) = apply { this.updatedAt = updatedAt }

        fun build(): StaffEntity =
            StaffEntity(
                id = id,
                userId = userId,
                salonId = salonId,
                role = role,
                workingHours = workingHours,
                hireDate = hireDate,
                specializations = specializations,
                isActive = isActive,
                createdAt = createdAt,
                updatedAt = updatedAt,
            )
    }

    // ===== SALON INVITATION BUILDERS =====

    fun aSalonInvitation(): SalonInvitationBuilder = SalonInvitationBuilder()

    class SalonInvitationBuilder {
        private var id: InvitationId? = null
        private var salonId: SalonId = SalonId.generate()
        private var inviterUserId: UserId = UserId.generate()
        private var invitedUserPhone: String = "+40731446895"
        private var proposedRole: StaffRole = StaffRole.GROOMER
        private var proposedPermissions: StaffPermissions = StaffPermissions.defaultGroomerAccess()
        private var message: String? = "Join our salon team!"
        private var status: InvitationStatus = InvitationStatus.PENDING
        private var invitedAt: LocalDateTime = LocalDateTime.now()
        private var respondedAt: LocalDateTime? = null
        private var expiresAt: LocalDateTime = LocalDateTime.now().plusDays(7)
        private var resendCount: Int = 0
        private var lastResendAt: LocalDateTime? = null
        private var cancelledAt: LocalDateTime? = null
        private var cancelledBy: UserId? = null
        private var createdAt: LocalDateTime = LocalDateTime.now()
        private var updatedAt: LocalDateTime = LocalDateTime.now()

        fun withId(id: InvitationId) = apply { this.id = id }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withInviterUserId(inviterUserId: UserId) = apply { this.inviterUserId = inviterUserId }

        fun withInvitedUserPhone(phone: String) = apply { this.invitedUserPhone = phone }

        fun withProposedRole(role: StaffRole) = apply { this.proposedRole = role }

        fun withProposedPermissions(permissions: StaffPermissions) = apply { this.proposedPermissions = permissions }

        fun withMessage(message: String?) = apply { this.message = message }

        fun withStatus(status: InvitationStatus) = apply { this.status = status }

        fun withInvitedAt(invitedAt: LocalDateTime) = apply { this.invitedAt = invitedAt }

        fun withRespondedAt(respondedAt: LocalDateTime?) = apply { this.respondedAt = respondedAt }

        fun withExpiresAt(expiresAt: LocalDateTime) = apply { this.expiresAt = expiresAt }

        fun withResendCount(count: Int) = apply { this.resendCount = count }

        fun withLastResendAt(lastResendAt: LocalDateTime?) = apply { this.lastResendAt = lastResendAt }

        fun withCancelledAt(cancelledAt: LocalDateTime?) = apply { this.cancelledAt = cancelledAt }

        fun withCancelledBy(cancelledBy: UserId?) = apply { this.cancelledBy = cancelledBy }

        fun build(): SalonInvitation =
            SalonInvitation(
                id = id ?: InvitationId.generate(),
                salonId = salonId,
                inviterUserId = inviterUserId,
                invitedUserPhone = invitedUserPhone,
                proposedRole = proposedRole,
                proposedPermissions = proposedPermissions,
                message = message,
                status = status,
                invitedAt = invitedAt,
                respondedAt = respondedAt,
                expiresAt = expiresAt,
                resendCount = resendCount,
                lastResendAt = lastResendAt,
                cancelledAt = cancelledAt,
                cancelledBy = cancelledBy,
                createdAt = createdAt,
                updatedAt = updatedAt,
            )
    }
}
