package ro.animaliaprogramari.animalia.test

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime

/**
 * Test builders for creating command and query objects in tests
 * Provides fluent API for test command/query creation with sensible defaults
 */
object TestCommandBuilder {
    // ===== SCHEDULE APPOINTMENT COMMAND BUILDERS =====

    fun aScheduleAppointmentCommand(): ScheduleAppointmentCommandBuilder = ScheduleAppointmentCommandBuilder()

    class ScheduleAppointmentCommandBuilder {
        private var salonId: SalonId = SalonId.generate()
        private var clientId: ClientId = ClientId.generate()
        private var clientName: String? = null
        private var clientPhone: PhoneNumber? = null
        private var petId: PetId? = PetId.generate() // Made optional - default to generated ID
        private var petName: String? = null
        private var petSpecies: String? = null
        private var petBreed: String? = null
        private var petSize: String? = null
        private var staffId: StaffId = StaffId.generate()
        private var appointmentDate: LocalDate = LocalDate.now().plusDays(1)
        private var startTime: LocalTime = LocalTime.of(10, 0)
        private var endTime: LocalTime = LocalTime.of(11, 0)
        private var serviceIds: List<ServiceId> = listOf(ServiceId.generate())
        private var notes: String? = null
        private var repetitionFrequency: RepetitionFrequency? = null
        private var isNewClient: Boolean = false
        private var isNewPet: Boolean = false

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withClientId(clientId: ClientId) = apply { this.clientId = clientId }

        fun withNewClient(
            name: String,
            phone: PhoneNumber,
        ) = apply {
            this.clientName = name
            this.clientPhone = phone
            this.isNewClient = true
        }

        fun withPetId(petId: PetId?) = apply { this.petId = petId }

        fun withNoPetId() = apply { this.petId = null }

        fun withNewPet(
            name: String,
            species: String,
            breed: String? = null,
            size: String? = null,
        ) = apply {
            this.petName = name
            this.petSpecies = species
            this.petBreed = breed
            this.petSize = size
            this.petId = null // Clear petId when creating new pet
            this.isNewPet = true
        }

        fun withStaffId(staffId: StaffId) = apply { this.staffId = staffId }

        fun withDate(date: LocalDate) = apply { this.appointmentDate = date }

        fun withTimeSlot(
            start: LocalTime,
            end: LocalTime,
        ) = apply {
            this.startTime = start
            this.endTime = end
        }

        fun withServiceIds(serviceIds: List<ServiceId>) = apply { this.serviceIds = serviceIds }

        fun withNotes(notes: String?) = apply { this.notes = notes }

        fun withRepetition(frequency: RepetitionFrequency?) = apply { this.repetitionFrequency = frequency }

        fun build(): ScheduleAppointmentCommand =
            ScheduleAppointmentCommand(
                salonId = salonId,
                clientId = clientId,
                clientName = clientName,
                clientPhone = clientPhone,
                petId = petId,
                petName = petName,
                petSpecies = petSpecies,
                petBreed = petBreed,
                petSize = petSize,
                staffId = staffId,
                appointmentDate = appointmentDate,
                startTime = startTime,
                endTime = endTime,
                serviceIds = serviceIds,
                notes = notes,
                repetitionFrequency = repetitionFrequency,
                isNewClient = isNewClient,
                isNewPet = isNewPet,
            )
    }

    // ===== UPDATE APPOINTMENT COMMAND BUILDERS =====

    fun anUpdateAppointmentCommand(): UpdateAppointmentCommandBuilder = UpdateAppointmentCommandBuilder()

    class UpdateAppointmentCommandBuilder {
        private var appointmentId: AppointmentId = AppointmentId.generate()
        private var salonId: SalonId = SalonId.generate()
        private var updaterUserId: UserId = UserId.generate()
        private var appointmentDate: LocalDate? = null
        private var startTime: LocalTime? = null
        private var endTime: LocalTime? = null
        private var serviceIds: List<ServiceId>? = null
        private var notes: String? = null

        fun withAppointmentId(appointmentId: AppointmentId) = apply { this.appointmentId = appointmentId }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withUpdaterUserId(userId: UserId) = apply { this.updaterUserId = userId }

        fun withDate(date: LocalDate) = apply { this.appointmentDate = date }

        fun withTimeSlot(
            start: LocalTime,
            end: LocalTime,
        ) = apply {
            this.startTime = start
            this.endTime = end
        }

        fun withServiceIds(serviceIds: List<ServiceId>) = apply { this.serviceIds = serviceIds }

        fun withNotes(notes: String?) = apply { this.notes = notes }

        fun build(): UpdateAppointmentCommand =
            UpdateAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                updaterUserId = updaterUserId,
                appointmentDate = appointmentDate,
                startTime = startTime,
                endTime = endTime,
                serviceIds = serviceIds,
                notes = notes,
            )
    }

    // ===== CANCEL APPOINTMENT COMMAND BUILDERS =====

    fun aCancelAppointmentCommand(): CancelAppointmentCommandBuilder = CancelAppointmentCommandBuilder()

    class CancelAppointmentCommandBuilder {
        private var appointmentId: AppointmentId = AppointmentId.generate()
        private var salonId: SalonId = SalonId.generate()
        private var cancellerUserId: UserId = UserId.generate()
        private var reason: String? = "Test cancellation"

        fun withAppointmentId(appointmentId: AppointmentId) = apply { this.appointmentId = appointmentId }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withCancellerUserId(userId: UserId) = apply { this.cancellerUserId = userId }

        fun withReason(reason: String?) = apply { this.reason = reason }

        fun build(): CancelAppointmentCommand =
            CancelAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                cancellerUserId = cancellerUserId,
                reason = reason,
            )
    }

    // ===== COMPLETE APPOINTMENT COMMAND BUILDERS =====

    fun aCompleteAppointmentCommand(): CompleteAppointmentCommandBuilder = CompleteAppointmentCommandBuilder()

    class CompleteAppointmentCommandBuilder {
        private var appointmentId: AppointmentId = AppointmentId.generate()
        private var salonId: SalonId = SalonId.generate()
        private var completerUserId: UserId = UserId.generate()
        private var notes: String? = "Service completed successfully"

        fun withAppointmentId(appointmentId: AppointmentId) = apply { this.appointmentId = appointmentId }

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withCompleterUserId(userId: UserId) = apply { this.completerUserId = userId }

        fun withNotes(notes: String?) = apply { this.notes = notes }

        fun build(): CompleteAppointmentCommand =
            CompleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                completerUserId = completerUserId,
                notes = notes,
            )
    }

    // ===== QUERY BUILDERS =====

    fun aGetAppointmentByIdQuery(): GetAppointmentByIdQueryBuilder = GetAppointmentByIdQueryBuilder()

    class GetAppointmentByIdQueryBuilder {
        private var appointmentId: AppointmentId = AppointmentId.generate()
        private var salonId: SalonId? = null
        private var requesterId: UserId? = null

        fun withAppointmentId(appointmentId: AppointmentId) = apply { this.appointmentId = appointmentId }

        fun withSalonId(salonId: SalonId?) = apply { this.salonId = salonId }

        fun withRequesterId(requesterId: UserId?) = apply { this.requesterId = requesterId }

        fun build(): GetAppointmentByIdQuery =
            GetAppointmentByIdQuery(
                appointmentId = appointmentId,
                salonId = salonId,
                requesterId = requesterId,
            )
    }

    fun aGetSalonAppointmentsQuery(): GetSalonAppointmentsQueryBuilder = GetSalonAppointmentsQueryBuilder()

    class GetSalonAppointmentsQueryBuilder {
        private var salonId: SalonId = SalonId.generate()
        private var requesterId: UserId = UserId.generate()
        private var date: LocalDate? = null
        private var startDate: LocalDate? = null
        private var endDate: LocalDate? = null
        private var status: AppointmentStatus? = null
        private var clientId: ClientId? = null
        private var staffId: StaffId? = null

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withRequesterId(requesterId: UserId) = apply { this.requesterId = requesterId }

        fun withDate(date: LocalDate?) = apply { this.date = date }

        fun withDateRange(
            startDate: LocalDate?,
            endDate: LocalDate?,
        ) = apply {
            this.startDate = startDate
            this.endDate = endDate
        }

        fun withStatus(status: AppointmentStatus?) = apply { this.status = status }

        fun withClientId(clientId: ClientId?) = apply { this.clientId = clientId }

        fun withStaffId(staffId: StaffId?) = apply { this.staffId = staffId }

        fun build(): GetSalonAppointmentsQuery =
            GetSalonAppointmentsQuery(
                salonId = salonId,
                requesterId = requesterId,
                date = date,
                startDate = startDate,
                endDate = endDate,
                status = status,
                clientId = clientId,
                staffId = staffId,
            )
    }

    fun aGetStaffAppointmentsQuery(): GetStaffAppointmentsQueryBuilder = GetStaffAppointmentsQueryBuilder()

    fun createBlockTimeCommand(): CreateBlockTimeCommandBuilder = CreateBlockTimeCommandBuilder()

    class GetStaffAppointmentsQueryBuilder {
        private var salonId: SalonId = SalonId.generate()
        private var staffId: StaffId = StaffId.generate()
        private var requesterId: UserId = UserId.generate()
        private var date: LocalDate? = null
        private var startDate: LocalDate? = null
        private var endDate: LocalDate? = null
        private var status: AppointmentStatus? = null

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withStaffId(staffId: StaffId) = apply { this.staffId = staffId }

        fun withRequesterId(requesterId: UserId) = apply { this.requesterId = requesterId }

        fun withDate(date: LocalDate?) = apply { this.date = date }

        fun withDateRange(
            startDate: LocalDate?,
            endDate: LocalDate?,
        ) = apply {
            this.startDate = startDate
            this.endDate = endDate
        }

        fun withStatus(status: AppointmentStatus?) = apply { this.status = status }

        fun build(): GetStaffAppointmentsQuery =
            GetStaffAppointmentsQuery(
                salonId = salonId,
                staffId = staffId,
                requesterId = requesterId,
                date = date,
                startDate = startDate,
                endDate = endDate,
                status = status,
            )
    }

    class CreateBlockTimeCommandBuilder {
        private var salonId: SalonId = SalonId.generate()
        private var startTime: ZonedDateTime = ZonedDateTime.now().plusHours(1)
        private var endTime: ZonedDateTime = ZonedDateTime.now().plusHours(3)
        private var reason: BlockReason = BlockReason.PAUZA
        private var customReason: String? = null
        private var staffIds: Set<StaffId> = emptySet()
        private var isRecurring: Boolean = false
        private var recurrencePattern: RecurrencePattern? = null
        private var notes: String? = null
        private var createdBy: UserId = UserId.generate()

        fun withSalonId(salonId: SalonId) = apply { this.salonId = salonId }

        fun withTimeRange(
            start: ZonedDateTime,
            end: ZonedDateTime,
        ) = apply {
            this.startTime = start
            this.endTime = end
        }

        fun withReason(
            reason: BlockReason,
            customReason: String? = null,
        ) = apply {
            this.reason = reason
            this.customReason = customReason
        }

        fun withStaffIds(staffIds: Set<StaffId>) = apply { this.staffIds = staffIds }

        fun withStaff(staffId: StaffId) = apply { this.staffIds = setOf(staffId) }

        fun withMultipleStaff(staffIds: List<StaffId>) = apply { this.staffIds = staffIds.toSet() }

        fun withRecurring(
            isRecurring: Boolean,
            pattern: RecurrencePattern? = null,
        ) = apply {
            this.isRecurring = isRecurring
            this.recurrencePattern = pattern
        }

        fun withNotes(notes: String?) = apply { this.notes = notes }

        fun withCreatedBy(userId: UserId) = apply { this.createdBy = userId }

        fun build(): CreateBlockTimeCommand {
            return CreateBlockTimeCommand(
                salonId = salonId,
                startTime = startTime,
                endTime = endTime,
                reason = reason,
                customReason = customReason,
                staffIds = staffIds,
                isRecurring = isRecurring,
                recurrencePattern = recurrencePattern,
                notes = notes,
                createdBy = createdBy,
            )
        }
    }
}
