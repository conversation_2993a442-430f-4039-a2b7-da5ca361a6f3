# User-Specific Notification Settings Tests

This document describes the comprehensive test suite for the user-specific notification settings feature.

## Overview

The user-specific notification settings feature transforms the notification system from salon-based to user-based, allowing each user to have personalized notification preferences for each salon they're associated with.

## Test Structure

### 1. Domain Model Tests (`UserSpecificNotificationSettingsTest.kt`)

**Purpose**: Test the core domain logic and business rules.

**Key Test Areas**:
- Creation and initialization with userId and salonId
- Notification logic (DND, rules, priorities)
- Settings updates while preserving identity
- User-specific behavior isolation

**Example Test Cases**:
```kotlin
@Test
fun `should create notification settings with userId and salonId`()

@Test
fun `should allow different settings for same user in different salons`()

@Test
fun `should respect do not disturb settings`()
```

### 2. Repository Tests (`UserSpecificNotificationSettingsRepositoryTest.kt`)

**Purpose**: Test the persistence layer with user-specific composite keys.

**Key Test Areas**:
- Save operations with userId + salonId composite key
- Find operations by user and salon combination
- Existence checks for specific user-salon pairs
- Delete operations maintaining data isolation

**Example Test Cases**:
```kotlin
@Test
fun `should save notification settings with userId and salonId`()

@Test
fun `should find notification settings by userId and salonId`()

@Test
fun `should handle same user with different salons`()
```

### 3. Use Case Tests (`UserSpecificNotificationSettingsUseCaseTest.kt`)

**Purpose**: Test business logic orchestration and user isolation.

**Key Test Areas**:
- Getting settings with automatic default creation
- Updating settings for specific user-salon combinations
- Error handling for invalid salons
- User isolation across different scenarios

**Example Test Cases**:
```kotlin
@Test
fun `should create default settings when none exist for user and salon`()

@Test
fun `should maintain separate settings for same user across different salons`()

@Test
fun `should handle complex do not disturb settings update`()
```

### 4. Controller Tests (`UserSpecificNotificationSettingsControllerTest.kt`)

**Purpose**: Test HTTP API layer with authentication and user context.

**Key Test Areas**:
- GET requests using authenticated user's ID
- PUT requests with user-specific updates
- Authentication and authorization
- Error responses for invalid requests

**Example Test Cases**:
```kotlin
@Test
fun `should get notification settings for authenticated user`()

@Test
fun `should handle different users accessing same salon`()

@Test
fun `should return 401 when user not authenticated`()
```

### 5. Integration Tests (`UserSpecificNotificationSettingsIntegrationTest.kt`)

**Purpose**: Test complete end-to-end workflows with real database.

**Key Test Areas**:
- User isolation in real database scenarios
- Repository integration with actual persistence
- Complete user workflows across multiple salons
- Data consistency and isolation verification

**Example Test Cases**:
```kotlin
@Test
fun `should maintain separate settings for different users in same salon`()

@Test
fun `should handle complete user workflow with multiple salons`()

@Test
fun `should persist and retrieve user-specific settings correctly`()
```

### 6. Edge Case Tests (`UserSpecificNotificationSettingsEdgeCasesTest.kt`)

**Purpose**: Test boundary conditions and error scenarios.

**Key Test Areas**:
- Do Not Disturb periods crossing midnight
- Invalid time configurations
- All notifications disabled scenarios
- Rapid settings updates
- Boundary time conditions

**Example Test Cases**:
```kotlin
@Test
fun `should handle do not disturb period crossing midnight`()

@Test
fun `should reject invalid do not disturb settings with same start and end time`()

@Test
fun `should handle push notifications disabled overriding all other settings`()
```

## Running the Tests

### Run All User-Specific Notification Settings Tests
```bash
./gradlew test --tests "*UserSpecific*NotificationSettings*Test*"
```

### Run Specific Test Suites

#### Domain Tests Only (Fast)
```bash
./gradlew test --tests "UserSpecificNotificationSettingsDomainTestSuite"
```

#### Repository Tests Only
```bash
./gradlew test --tests "UserSpecificNotificationSettingsRepositoryTestSuite"
```

#### Use Case Tests Only
```bash
./gradlew test --tests "UserSpecificNotificationSettingsUseCaseTestSuite"
```

#### Controller Tests Only
```bash
./gradlew test --tests "UserSpecificNotificationSettingsControllerTestSuite"
```

#### Integration Tests Only
```bash
./gradlew test --tests "UserSpecificNotificationSettingsIntegrationTestSuite"
```

#### Complete Test Suite
```bash
./gradlew test --tests "UserSpecificNotificationSettingsTestSuite"
```

### Run Individual Test Classes
```bash
# Domain model tests
./gradlew test --tests "UserSpecificNotificationSettingsTest"

# Repository tests
./gradlew test --tests "UserSpecificNotificationSettingsRepositoryTest"

# Use case tests
./gradlew test --tests "UserSpecificNotificationSettingsUseCaseTest"

# Controller tests
./gradlew test --tests "UserSpecificNotificationSettingsControllerTest"

# Integration tests
./gradlew test --tests "UserSpecificNotificationSettingsIntegrationTest"

# Edge case tests
./gradlew test --tests "UserSpecificNotificationSettingsEdgeCasesTest"
```

## Test Coverage

The test suite provides comprehensive coverage of:

### Functional Requirements
- ✅ User-specific notification settings storage
- ✅ Salon-specific settings per user
- ✅ Settings isolation between users
- ✅ Settings isolation between salons for same user
- ✅ Default settings creation
- ✅ Settings updates and persistence

### Technical Requirements
- ✅ Composite key handling (userId + salonId)
- ✅ Repository layer with new methods
- ✅ Use case layer with user context
- ✅ Controller layer with authentication
- ✅ Entity mapping with userId field

### Edge Cases and Error Handling
- ✅ Invalid time configurations
- ✅ Boundary conditions
- ✅ Authentication failures
- ✅ Non-existent salon handling
- ✅ Rapid updates
- ✅ Complex DND scenarios

### Data Integrity
- ✅ User-salon combination uniqueness
- ✅ Settings isolation verification
- ✅ Proper cleanup on deletion
- ✅ Consistent timestamps
- ✅ Identity preservation through updates

## Key Test Scenarios

### Scenario 1: Multi-User Same Salon
Tests that different users can have completely different notification preferences for the same salon.

### Scenario 2: Same User Multi-Salon
Tests that the same user can have different notification preferences for different salons.

### Scenario 3: Complex Workflow
Tests a complete user journey from joining salons to configuring detailed preferences.

### Scenario 4: Edge Cases
Tests boundary conditions, error scenarios, and invalid configurations.

### Scenario 5: Data Isolation
Tests that user data is properly isolated and cannot be accessed by other users.

## Test Data Patterns

The tests use consistent patterns for test data:

- **User IDs**: Generated using `UserId.generate()`
- **Salon IDs**: Generated using `SalonId.generate()`
- **Settings**: Created using `NotificationSettings.createDefault(userId, salonId)`
- **Updates**: Applied using `.updateSettings()` method
- **Time Values**: Use `LocalTime.of(hour, minute)` for consistency

## Assertions and Verification

Tests verify:
- **Identity Preservation**: userId and salonId never change
- **Settings Isolation**: Changes to one user-salon combination don't affect others
- **Business Rules**: DND periods, notification rules, and priorities work correctly
- **Persistence**: Data is correctly saved and retrieved
- **Error Handling**: Appropriate exceptions and error responses

## Continuous Integration

These tests are designed to run in CI/CD pipelines:
- **Fast Feedback**: Domain tests run quickly for rapid feedback
- **Comprehensive Coverage**: Full suite ensures complete feature validation
- **Isolated Tests**: Each test is independent and can run in parallel
- **Deterministic**: Tests produce consistent results across environments

## Maintenance

When modifying the user-specific notification settings feature:

1. **Update Domain Tests**: If business rules change
2. **Update Repository Tests**: If persistence logic changes
3. **Update Use Case Tests**: If orchestration logic changes
4. **Update Controller Tests**: If API contracts change
5. **Update Integration Tests**: If end-to-end workflows change
6. **Update Edge Case Tests**: If new edge cases are discovered

The test suite is designed to catch regressions and ensure the feature continues to work correctly as the codebase evolves.
