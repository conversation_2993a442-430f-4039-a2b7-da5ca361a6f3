package ro.animaliaprogramari.animalia.performance

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaAppointmentRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringNotificationHistoryRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.OptimizedConflictDetectionService
import ro.animaliaprogramari.animalia.domain.service.OptimizedSuggestionService
import ro.animaliaprogramari.animalia.domain.service.QueryPerformanceMonitor
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertTrue

/**
 * Performance tests to validate database query optimizations
 * Ensures all critical queries meet the <100ms target
 */
@SpringBootTest
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Disabled("Requires database setup not available in CI")
class DatabasePerformanceTest {

    @Autowired
    private lateinit var appointmentRepository: JpaAppointmentRepository

    @Autowired
    private lateinit var notificationHistoryRepository: SpringNotificationHistoryRepository

    @Autowired
    private lateinit var conflictDetectionService: OptimizedConflictDetectionService

    @Autowired
    private lateinit var suggestionService: OptimizedSuggestionService

    @Autowired
    private lateinit var performanceMonitor: QueryPerformanceMonitor

    companion object {
        private const val PERFORMANCE_TARGET_MS = 100L
        private const val BATCH_SIZE = 50
    }

    @Test
    @Transactional
    fun `test appointment query performance with filters`() {
        // Arrange
        val salonId = SalonId.generate()
        val startDate = LocalDate.now()
        val endDate = startDate.plusDays(7)

        // Act & Assert
        val executionTime = measureExecutionTime {
            appointmentRepository.findBySalonIdWithFilters(
                salonId = salonId,
                date = null,
                startDate = startDate,
                endDate = endDate,
                status = null,
                clientId = null,
                staffId = null
            )
        }

        assertTrue(
            executionTime < PERFORMANCE_TARGET_MS,
            "Appointment query with filters took ${executionTime}ms, exceeds target of ${PERFORMANCE_TARGET_MS}ms"
        )
    }

    @Test
    @Transactional
    fun `test conflict detection performance`() {
        // Arrange
        val staffId = StaffId.generate()
        val salonId = SalonId.generate()
        val date = LocalDate.now().plusDays(1)
        val startTime = LocalTime.of(10, 0)
        val endTime = LocalTime.of(11, 0)

        // Act & Assert
        val executionTime = measureExecutionTime {
            conflictDetectionService.detectConflictsOptimized(
                date = date,
                startTime = startTime,
                endTime = endTime,
                staffId = staffId,
                salonId = salonId
            )
        }

        assertTrue(
            executionTime < PERFORMANCE_TARGET_MS,
            "Conflict detection took ${executionTime}ms, exceeds target of ${PERFORMANCE_TARGET_MS}ms"
        )
    }

    @Test
    @Transactional
    fun `test batch conflict detection performance`() {
        // Arrange
        val staffId = StaffId.generate()
        val salonId = SalonId.generate()
        val date = LocalDate.now().plusDays(1)
        val timeSlots = generateTimeSlots(10) // 10 time slots

        // Act & Assert
        val executionTime = measureExecutionTime {
            conflictDetectionService.detectConflictsBatch(
                date = date,
                timeSlots = timeSlots,
                staffId = staffId,
                salonId = salonId
            )
        }

        assertTrue(
            executionTime < PERFORMANCE_TARGET_MS,
            "Batch conflict detection took ${executionTime}ms, exceeds target of ${PERFORMANCE_TARGET_MS}ms"
        )
    }

    @Test
    @Transactional
    fun `test appointment suggestions performance`() {
        // Arrange
        val salonId = SalonId.generate()
        val staffIds = (1..5).map { StaffId.generate() }
        val date = LocalDate.now().plusDays(1)
        val durationMinutes = 60L

        // Act & Assert
        val executionTime = measureExecutionTime {
            suggestionService.findBatchSuggestionsOptimized(
                salonId = salonId,
                date = date,
                durationMinutes = durationMinutes,
                staffIds = staffIds,
                limit = 10
            )
        }

        assertTrue(
            executionTime < PERFORMANCE_TARGET_MS,
            "Appointment suggestions took ${executionTime}ms, exceeds target of ${PERFORMANCE_TARGET_MS}ms"
        )
    }

    @Test
    @Transactional
    fun `test notification history query performance`() {
        // Arrange
        val salonId = SalonId.generate().value
        val startDate = LocalDateTime.now().minusDays(30)
        val endDate = LocalDateTime.now()

        // Act & Assert
        val executionTime = measureExecutionTime {
            notificationHistoryRepository.findBySalonIdWithFilters(
                salonId = salonId,
                type = null,
                readStatus = null,
                startDate = startDate,
                endDate = endDate,
                pageable = org.springframework.data.domain.PageRequest.of(0, 20)
            )
        }

        assertTrue(
            executionTime < PERFORMANCE_TARGET_MS,
            "Notification history query took ${executionTime}ms, exceeds target of ${PERFORMANCE_TARGET_MS}ms"
        )
    }

    @Test
    @Transactional
    fun `test notification dashboard summary performance`() {
        // Arrange
        val salonId = SalonId.generate().value
        val startDate = LocalDateTime.now().minusDays(7)

        // Act & Assert
        val executionTime = measureExecutionTime {
            notificationHistoryRepository.getDashboardSummary(
                salonId = salonId,
                startDate = startDate
            )
        }

        assertTrue(
            executionTime < PERFORMANCE_TARGET_MS,
            "Notification dashboard summary took ${executionTime}ms, exceeds target of ${PERFORMANCE_TARGET_MS}ms"
        )
    }

    @Test
    @Transactional
    fun `test staff query with specializations performance`() {
        // This test would require the JpaStaffRepository to be available
        // For now, we'll test the concept
        val executionTime = measureExecutionTime {
            // Simulate staff query with eager loading
            Thread.sleep(10) // Simulate fast query
        }

        assertTrue(
            executionTime < PERFORMANCE_TARGET_MS,
            "Staff query with specializations took ${executionTime}ms, exceeds target of ${PERFORMANCE_TARGET_MS}ms"
        )
    }

    @Test
    fun `test performance monitoring overhead`() {
        // Test that performance monitoring itself doesn't add significant overhead
        val withoutMonitoring = measureExecutionTime {
            // Simple operation
            Thread.sleep(10)
        }

        val withMonitoring = measureExecutionTime {
            performanceMonitor.monitorQuery("test_query", ro.animaliaprogramari.animalia.domain.service.QueryType.SELECT) {
                Thread.sleep(10)
            }
        }

        val overhead = withMonitoring - withoutMonitoring
        assertTrue(
            overhead < 10,
            "Performance monitoring adds ${overhead}ms overhead, should be minimal"
        )
    }

    @Test
    fun `validate all slow queries are identified`() {
        // Get current slow queries
        val slowQueries = performanceMonitor.getSlowQueries()

        // Log slow queries for analysis
        slowQueries.forEach { query ->
            println("Slow query detected: ${query.queryName} - ${query.averageTimeMs}ms average")
        }

        // In a real scenario, we might want to assert that certain known slow queries are fixed
        // For now, we'll just ensure the monitoring is working
        assertTrue(slowQueries.size >= 0, "Slow query monitoring should be functional")
    }

    private fun measureExecutionTime(operation: () -> Unit): Long {
        val startTime = System.currentTimeMillis()
        operation()
        return System.currentTimeMillis() - startTime
    }

    private fun generateTimeSlots(count: Int): List<ro.animaliaprogramari.animalia.domain.service.TimeSlot> {
        return (0 until count).map { i ->
            val startTime = LocalTime.of(9, 0).plusMinutes((i * 30).toLong())
            val endTime = startTime.plusMinutes(30)
            ro.animaliaprogramari.animalia.domain.service.TimeSlot(startTime, endTime)
        }
    }
}
