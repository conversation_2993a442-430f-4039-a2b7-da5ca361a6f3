package ro.animaliaprogramari.animalia.config

import io.mockk.mockk
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime

/**
 * Test configuration for Staff Working Hours tests
 * Provides mock implementations and test data
 */
@TestConfiguration
@Profile("test")
class StaffWorkingHoursTestConfig {
    @Bean
    @Primary
    fun mockStaffWorkingHoursRepository(): StaffWorkingHoursRepository {
        return mockk<StaffWorkingHoursRepository>(relaxed = true)
    }

    companion object {
        /**
         * Create test working hours settings
         */
        fun createTestWorkingHoursSettings(
            staffId: StaffId = StaffId.of("test-staff-123"),
            salonId: SalonId = SalonId.of("test-salon-456"),
        ): StaffWorkingHoursSettings {
            return StaffWorkingHoursSettings.createDefault(staffId, salonId)
        }

        /**
         * Create test staff holiday
         */
        fun createTestHoliday(
            salonId: SalonId = SalonId.of("test-salon-456"),
            name: String = "Test Holiday",
            date: LocalDate = LocalDate.of(2024, 1, 1),
            isWorkingDay: Boolean = false,
        ): StaffHoliday {
            return StaffHoliday.create(salonId, name, date, isWorkingDay)
        }

        /**
         * Create test custom closure
         */
        fun createTestCustomClosure(
            salonId: SalonId = SalonId.of("test-salon-456"),
            reason: String = "Test Closure",
            date: LocalDate = LocalDate.of(2024, 6, 15),
            description: String? = "Test description",
        ): StaffCustomClosure {
            return StaffCustomClosure.create(salonId, reason, date, description)
        }

        /**
         * Create test staff member
         */
        fun createTestStaff(
            userId: UserId = UserId.of("test-user-123"),
            salonId: SalonId = SalonId.of("test-salon-456"),
            role: StaffRole = StaffRole.GROOMER,
        ): Staff {
            return when (role) {
                StaffRole.CHIEF_GROOMER -> Staff.createChiefGroomer(userId, salonId)
                else -> Staff.createGroomer(userId, salonId, StaffPermissions.defaultGroomerAccess())
            }
        }

        /**
         * Create test appointment
         */
        fun createTestAppointment(
            salonId: SalonId = SalonId.of("test-salon-456"),
            staffId: StaffId = StaffId.of("test-user-123"),
            date: LocalDate = LocalDate.of(2024, 1, 8),
            startTime: LocalTime = LocalTime.of(10, 0),
            endTime: LocalTime = LocalTime.of(11, 0),
        ): Appointment {
            val testService =
                SalonService.create(
                    salonId = salonId,
                    name = "Basic Grooming",
                    description = "Basic grooming service",
                    basePrice = Money.of("50.00"),
                    duration = Duration.ofMinutes(60),
                    category = ServiceCategory.GROOMING,
                )

            return Appointment.create(
                salonId = salonId,
                clientId = ClientId.of("test-client-123"),
                petId = PetId.of("test-pet-456"),
                staffId = staffId,
                appointmentDate = date,
                startTime = startTime,
                endTime = endTime,
                salonServices = listOf(testService),
            )
        }

        /**
         * Create custom weekly schedule for testing
         */
        fun createCustomWeeklySchedule(
            workingDays: Set<DayOfWeek> =
                setOf(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY),
            startTime: LocalTime = LocalTime.of(9, 0),
            endTime: LocalTime = LocalTime.of(17, 0),
            breakStart: LocalTime? = LocalTime.of(12, 0),
            breakEnd: LocalTime? = LocalTime.of(13, 0),
        ): Map<DayOfWeek, DaySchedule> {
            return DayOfWeek.values().associateWith { day ->
                if (day in workingDays) {
                    DaySchedule.workingDay(startTime, endTime, breakStart, breakEnd)
                } else {
                    DaySchedule.dayOff()
                }
            }
        }

        /**
         * Create test availability report
         */
        fun createTestAvailabilityReport(
            date: LocalDate = LocalDate.of(2024, 1, 8),
            staffList: List<Staff> = listOf(createTestStaff()),
            availabilityMap: Map<Staff, Boolean> = emptyMap(),
        ): StaffAvailabilityReport {
            val staffAvailability =
                staffList.map { staff ->
                    val isAvailable = availabilityMap[staff] ?: true
                    StaffMemberAvailability(
                        staff = staff,
                        isAvailable = isAvailable,
                        workingHours =
                            if (isAvailable) {
                                DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0))
                            } else {
                                null
                            },
                        conflictingAppointments = emptyList(),
                        reason = if (!isAvailable) "Day off" else null,
                    )
                }

            return StaffAvailabilityReport(
                date = date,
                staffAvailability = staffAvailability,
            )
        }

        /**
         * Create Romanian holidays for testing
         */
        fun createRomanianHolidays(
            salonId: SalonId = SalonId.of("test-salon-456"),
            year: Int = 2024,
        ): List<StaffHoliday> {
            return listOf(
                StaffHoliday.create(salonId, "Anul Nou", LocalDate.of(year, 1, 1)),
                StaffHoliday.create(salonId, "Bobotează", LocalDate.of(year, 1, 6)),
                StaffHoliday.create(salonId, "Ziua Muncii", LocalDate.of(year, 5, 1)),
                StaffHoliday.create(salonId, "Ziua Copilului", LocalDate.of(year, 6, 1)),
                StaffHoliday.create(salonId, "Adormirea Maicii Domnului", LocalDate.of(year, 8, 15)),
                StaffHoliday.create(salonId, "Sfântul Andrei", LocalDate.of(year, 11, 30)),
                StaffHoliday.create(salonId, "Ziua Națională", LocalDate.of(year, 12, 1)),
                StaffHoliday.create(salonId, "Crăciunul", LocalDate.of(year, 12, 25)),
                StaffHoliday.create(salonId, "A doua zi de Crăciun", LocalDate.of(year, 12, 26)),
            )
        }
    }
}
