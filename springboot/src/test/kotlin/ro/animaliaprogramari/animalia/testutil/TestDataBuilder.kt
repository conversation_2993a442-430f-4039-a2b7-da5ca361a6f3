package ro.animaliaprogramari.animalia.testutil

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.ZonedDateTime

/**
 * Test data builder for creating test objects with sensible defaults
 * Follows the Test Data Builder pattern for maintainable and readable tests
 */
object TestDataBuilder {
    /**
     * Creates a valid BlockTime for testing
     */
    fun aBlockTime(
        id: BlockTimeId = BlockTimeId.generate(),
        salonId: SalonId = SalonId.of("test-salon-1"),
        startTime: ZonedDateTime = ZonedDateTime.now().plusHours(1),
        endTime: ZonedDateTime = ZonedDateTime.now().plusHours(3),
        reason: BlockReason = BlockReason.PAUZA,
        customReason: String? = null,
        staffIds: Set<StaffId> = setOf(StaffId.of("staff-1")),
        createdBy: UserId = UserId.of("user-1"),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedBy: UserId? = null,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        isRecurring: Boolean = false,
        recurrencePattern: RecurrencePattern? = null,
        notes: String? = null,
        status: BlockTimeStatus = BlockTimeStatus.ACTIVE,
    ): BlockTime {
        return BlockTime(
            id = id,
            salonId = salonId,
            startTime = startTime,
            endTime = endTime,
            reason = reason,
            customReason = customReason,
            staffIds = staffIds,
            createdBy = createdBy,
            createdAt = createdAt,
            updatedBy = updatedBy,
            updatedAt = updatedAt,
            isRecurring = isRecurring,
            recurrencePattern = recurrencePattern,
            notes = notes,
            status = status,
        )
    }

    /**
     * Creates a valid RecurrencePattern for testing
     */
    fun aRecurrencePattern(
        type: RecurrenceType = RecurrenceType.WEEKLY,
        interval: Int = 1,
        daysOfWeek: Set<DayOfWeek>? = setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY),
        dayOfMonth: Int? = null,
        endDate: ZonedDateTime? = null,
        occurrences: Int? = null,
    ): RecurrencePattern {
        return RecurrencePattern(
            type = type,
            interval = interval,
            daysOfWeek = daysOfWeek,
            dayOfMonth = dayOfMonth,
            endDate = endDate,
            occurrences = occurrences,
        )
    }

    /**
     * Creates a valid BlockTimeConflict for testing
     */
    fun aBlockTimeConflict(
        staffId: StaffId = StaffId.of("staff-1"),
        staffName: String = "Test Staff",
        conflictType: ConflictType = ConflictType.APPOINTMENT,
        conflictDetails: ConflictDetails = aConflictDetails(),
    ): BlockTimeConflict {
        return BlockTimeConflict(
            staffId = staffId,
            staffName = staffName,
            conflictType = conflictType,
            conflictDetails = conflictDetails,
        )
    }

    /**
     * Creates valid ConflictDetails for testing
     */
    fun aConflictDetails(
        id: String = "conflict-1",
        startTime: ZonedDateTime = ZonedDateTime.now().plusHours(1),
        endTime: ZonedDateTime = ZonedDateTime.now().plusHours(2),
        description: String = "Test conflict",
    ): ConflictDetails {
        return ConflictDetails(
            id = id,
            startTime = startTime,
            endTime = endTime,
            description = description,
        )
    }

    /**
     * Creates a valid Staff for testing
     */
    fun aStaff(
        id: StaffId = StaffId.of("staff-1"),
        userId: UserId = UserId.of("user-1"),
        salonId: SalonId = SalonId.of("salon-1"),
        role: StaffRole = StaffRole.GROOMER,
        nickname: String? = "TestGroomer",
        isActive: Boolean = true,
        permissions: StaffPermissions = aStaffPermissions(),
    ): Staff {
        return Staff(
            id = id,
            userId = userId,
            salonId = salonId,
            role = role,
            nickname = nickname,
            isActive = isActive,
            permissions = permissions,
        )
    }

    /**
     * Creates valid StaffPermissions for testing
     */
    fun aStaffPermissions(
        clientDataAccess: ClientDataAccess = ClientDataAccess.LIMITED,
        canManageAppointments: Boolean = true,
        canManageServices: Boolean = false,
        canViewReports: Boolean = false,
        canManageSchedule: Boolean = true,
    ): StaffPermissions {
        return StaffPermissions(
            clientDataAccess = clientDataAccess,
            canManageAppointments = canManageAppointments,
            canManageServices = canManageServices,
            canViewReports = canViewReports,
            canManageSchedule = canManageSchedule,
        )
    }

    /**
     * Creates a valid User for testing
     */
    fun aUser(
        id: UserId = UserId.of("user-1"),
        firebaseUid: String = "firebase-uid-1",
        name: String = "Test User",
        email: Email? = Email.of("<EMAIL>"),
        phoneNumber: String? = "+40123456789",
        role: UserRole = UserRole.USER,
    ): User {
        return User(
            id = id,
            firebaseUid = firebaseUid,
            name = name,
            email = email,
            phoneNumber = phoneNumber,
            role = role,
        )
    }

    /**
     * Creates a valid Salon for testing
     */
    fun aSalon(
        id: SalonId = SalonId.of("salon-1"),
        name: String = "Test Salon",
        address: String? = "Test Address",
        city: String? = "Test City",
        phone: PhoneNumber? = PhoneNumber.of("+40123456789"),
        email: Email? = Email.of("<EMAIL>"),
        ownerId: UserId = UserId.of("owner-1"),
        description: String? = "Test salon description",
    ): Salon {
        return Salon(
            id = id,
            name = name,
            address = address,
            city = city,
            phone = phone,
            email = email,
            ownerId = ownerId,
            description = description,
        )
    }

    /**
     * Creates a valid Appointment for testing
     */
    fun anAppointment(
        id: AppointmentId = AppointmentId.of("appointment-1"),
        salonId: SalonId = SalonId.of("salon-1"),
        staffId: StaffId = StaffId.of("staff-1"),
        clientId: ClientId = ClientId.of("client-1"),
        petId: PetId = PetId.of("pet-1"),
        appointmentDate: java.time.LocalDate = java.time.LocalDate.now().plusDays(1),
        startTime: java.time.LocalTime = java.time.LocalTime.of(10, 0),
        endTime: java.time.LocalTime = java.time.LocalTime.of(11, 0),
        status: AppointmentStatus = AppointmentStatus.CONFIRMED,
        serviceIds: List<ServiceId> = listOf(ServiceId.of("service-1")),
        totalPrice: Money = Money.of(100.0),
        totalDuration: Duration = Duration.ofHours(1),
        notes: String? = null,
        repetitionFrequency: RepetitionFrequency? = null,
    ): Appointment {
        return Appointment(
            id = id,
            salonId = salonId,
            clientId = clientId,
            petId = petId,
            staffId = staffId,
            appointmentDate = appointmentDate,
            startTime = startTime,
            endTime = endTime,
            status = status,
            serviceIds = serviceIds,
            totalPrice = totalPrice,
            totalDuration = totalDuration,
            notes = notes,
            repetitionFrequency = repetitionFrequency,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            version = 0,
        )
    }

    /**
     * Builder class for more complex BlockTime scenarios
     */
    class BlockTimeBuilder {
        private var blockTime = aBlockTime()

        fun withId(id: BlockTimeId) = apply { blockTime = blockTime.copy(id = id) }

        fun withSalonId(salonId: SalonId) = apply { blockTime = blockTime.copy(salonId = salonId) }

        fun withTimeRange(
            start: ZonedDateTime,
            end: ZonedDateTime,
        ) = apply {
            blockTime = blockTime.copy(startTime = start, endTime = end)
        }

        fun withReason(
            reason: BlockReason,
            customReason: String? = null,
        ) = apply {
            blockTime = blockTime.copy(reason = reason, customReason = customReason)
        }

        fun withStaff(vararg staffIds: String) =
            apply {
                blockTime = blockTime.copy(staffIds = staffIds.map { StaffId.of(it) }.toSet())
            }

        fun withCreatedBy(userId: UserId) = apply { blockTime = blockTime.copy(createdBy = userId) }

        fun withStatus(status: BlockTimeStatus) = apply { blockTime = blockTime.copy(status = status) }

        fun withRecurrence(pattern: RecurrencePattern) =
            apply {
                blockTime = blockTime.copy(isRecurring = true, recurrencePattern = pattern)
            }

        fun withNotes(notes: String) = apply { blockTime = blockTime.copy(notes = notes) }

        fun cancelled() = apply { blockTime = blockTime.copy(status = BlockTimeStatus.CANCELLED) }

        fun expired() = apply { blockTime = blockTime.copy(status = BlockTimeStatus.EXPIRED) }

        fun inPast() =
            apply {
                val pastStart = ZonedDateTime.now().minusHours(3)
                val pastEnd = ZonedDateTime.now().minusHours(1)
                blockTime = blockTime.copy(startTime = pastStart, endTime = pastEnd)
            }

        fun inFuture() =
            apply {
                val futureStart = ZonedDateTime.now().plusHours(1)
                val futureEnd = ZonedDateTime.now().plusHours(3)
                blockTime = blockTime.copy(startTime = futureStart, endTime = futureEnd)
            }

        fun build(): BlockTime = blockTime
    }

    /**
     * Creates a BlockTimeBuilder for fluent test data creation
     */
    fun blockTime(): BlockTimeBuilder = BlockTimeBuilder()

    /**
     * Creates a valid Client for testing
     */
    fun aClient(
        id: ClientId = ClientId.generate(),
        name: String = "Test Client",
        phone: PhoneNumber? = PhoneNumber.of("+40123456789"),
        email: Email? = Email.of("<EMAIL>"),
        address: String? = "Test Address",
        notes: String? = null,
    ): Client {
        return Client(
            id = id,
            name = name,
            phone = phone,
            email = email,
            address = address,
            notes = notes,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            salonId = null,
        )
    }

    /**
     * Creates a valid Pet for testing
     */
    fun aPet(
        id: PetId = PetId.generate(),
        clientId: ClientId = ClientId.generate(),
        name: String = "Test Pet",
        species: String = "Dog",
        breed: String? = "Golden Retriever",
        size: String? = "M",
        birthDate: java.time.LocalDate? = null,
        notes: String? = null,
    ): Pet {
        return Pet(
            id = id,
            clientId = clientId,
            name = name,
            breed = breed,
            age = null,
            weight = null,
            color = null,
            gender = null,
            notes = notes,
            medicalConditions = null,
            size = size,
            species = species,
            photoUrl = null,
        )
    }

    /**
     * Creates a valid SalonService for testing
     */
    fun aSalonService(
        id: ServiceId = ServiceId.generate(),
        salonId: SalonId = SalonId.generate(),
        name: String = "Test Service",
        description: String? = "Test service description",
        basePrice: Money = Money.of(50.0),
        duration: ro.animaliaprogramari.animalia.domain.model.Duration =
            ro.animaliaprogramari.animalia.domain.model.Duration.ofMinutes(
                60,
            ),
        category: ServiceCategory = ServiceCategory.GROOMING,
        isActive: Boolean = true,
    ): SalonService {
        return SalonService(
            id = id,
            salonId = salonId,
            name = name,
            description = description,
            basePrice = basePrice,
            duration = duration,
            category = category,
            isActive = isActive,
        )
    }

    /**
     * Builder classes for fluent API
     */
    class SalonBuilder {
        private var salon = aSalon()

        fun withName(name: String) = apply { salon = salon.copy(name = name) }

        fun withId(id: SalonId) = apply { salon = salon.copy(id = id) }

        fun withOwnerId(ownerId: UserId) = apply { salon = salon.copy(ownerId = ownerId) }

        fun withAddress(address: String) = apply { salon = salon.copy(address = address) }

        fun withCity(city: String) = apply { salon = salon.copy(city = city) }

        fun withPhone(phone: PhoneNumber) = apply { salon = salon.copy(phone = phone) }

        fun withEmail(email: Email) = apply { salon = salon.copy(email = email) }

        fun build(): Salon = salon
    }

    class ClientBuilder {
        private var client = aClient()

        fun withName(name: String) = apply { client = client.copy(name = name) }

        fun withId(id: ClientId) = apply { client = client.copy(id = id) }

        fun withPhone(phone: String) = apply { client = client.copy(phone = PhoneNumber.of(phone)) }

        fun withEmail(email: String) = apply { client = client.copy(email = Email.of(email)) }

        fun withAddress(address: String) = apply { client = client.copy(address = address) }

        fun withNotes(notes: String) = apply { client = client.copy(notes = notes) }

        fun build(): Client = client
    }

    class PetBuilder {
        private var pet = aPet()

        fun withName(name: String) = apply { pet = pet.copy(name = name) }

        fun withId(id: PetId) = apply { pet = pet.copy(id = id) }

        fun withClientId(clientId: ClientId) = apply { pet = pet.copy(clientId = clientId) }

        fun withSpecies(species: String) = apply { pet = pet.copy(species = species) }

        fun withBreed(breed: String) = apply { pet = pet.copy(breed = breed) }

        fun withSize(size: String) = apply { pet = pet.copy(size = size) }

        fun withBirthDate(birthDate: java.time.LocalDate) = apply { pet = pet.copy(age = null) }

        fun withNotes(notes: String) = apply { pet = pet.copy(notes = notes) }

        fun build(): Pet = pet
    }

    class StaffBuilder {
        private var staff = aStaff()

        fun withId(id: StaffId) = apply { staff = staff.copy(id = id) }

        fun withUserId(userId: UserId) = apply { staff = staff.copy(userId = userId) }

        fun withSalonId(salonId: SalonId) = apply { staff = staff.copy(salonId = salonId) }

        fun withRole(role: StaffRole) = apply { staff = staff.copy(role = role) }

        fun withNickname(nickname: String) = apply { staff = staff.copy(nickname = nickname) }

        fun withActive(isActive: Boolean) = apply { staff = staff.copy(isActive = isActive) }

        fun withPermissions(permissions: StaffPermissions) = apply { staff = staff.copy(permissions = permissions) }

        fun build(): Staff = staff
    }

    class SalonServiceBuilder {
        private var service = aSalonService()

        fun withId(id: ServiceId) = apply { service = service.copy(id = id) }

        fun withSalonId(salonId: SalonId) = apply { service = service.copy(salonId = salonId) }

        fun withName(name: String) = apply { service = service.copy(name = name) }

        fun withDescription(description: String) = apply { service = service.copy(description = description) }

        fun withPrice(price: Money) = apply { service = service.copy(basePrice = price) }

        fun withDuration(duration: ro.animaliaprogramari.animalia.domain.model.Duration) =
            apply {
                service = service.copy(duration = duration)
            }

        fun withCategory(category: ServiceCategory) = apply { service = service.copy(category = category) }

        fun withActive(isActive: Boolean) = apply { service = service.copy(isActive = isActive) }

        fun build(): SalonService = service
    }
}
