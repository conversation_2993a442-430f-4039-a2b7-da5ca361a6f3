package ro.animaliaprogramari.animalia.testutil

import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime

/**
 * Test data builder for Staff Working Hours related objects
 * Provides fluent API for creating test data with sensible defaults
 */
object StaffWorkingHoursTestDataBuilder {
    fun aStaffWorkingHoursSettings(
        staffId: StaffId = StaffId.of("staff-123"),
        salonId: SalonId = SalonId.of("salon-456"),
    ): StaffWorkingHoursSettingsBuilder {
        return StaffWorkingHoursSettingsBuilder(staffId, salonId)
    }

    fun aStaffHoliday(salonId: SalonId = SalonId.of("salon-456")): StaffHolidayBuilder {
        return StaffHolidayBuilder(salonId)
    }

    fun aStaffCustomClosure(salonId: SalonId = SalonId.of("salon-456")): StaffCustomClosureBuilder {
        return StaffCustomClosureBuilder(salonId)
    }

    fun anUpdateStaffWorkingHoursRequest(): UpdateStaffWorkingHoursRequestBuilder {
        return UpdateStaffWorkingHoursRequestBuilder()
    }

    fun aStaffAvailabilityReport(date: LocalDate = LocalDate.of(2024, 1, 8)): StaffAvailabilityReportBuilder {
        return StaffAvailabilityReportBuilder(date)
    }

    class StaffWorkingHoursSettingsBuilder(
        private val staffId: StaffId,
        private val salonId: SalonId,
    ) {
        private var weeklySchedule: Map<DayOfWeek, DaySchedule> = createDefaultWeeklySchedule()
        private var holidays: List<StaffHoliday> = emptyList()
        private var customClosures: List<StaffCustomClosure> = emptyList()

        fun withWeeklySchedule(schedule: Map<DayOfWeek, DaySchedule>) =
            apply {
                this.weeklySchedule = schedule
            }

        fun withHolidays(holidays: List<StaffHoliday>) =
            apply {
                this.holidays = holidays
            }

        fun withCustomClosures(closures: List<StaffCustomClosure>) =
            apply {
                this.customClosures = closures
            }

        fun withWorkingDays(vararg days: DayOfWeek) =
            apply {
                val newSchedule = weeklySchedule.toMutableMap()
                DayOfWeek.values().forEach { day ->
                    newSchedule[day] =
                        if (day in days) {
                            DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0))
                        } else {
                            DaySchedule.dayOff()
                        }
                }
                this.weeklySchedule = newSchedule
            }

        fun build(): StaffWorkingHoursSettings {
            return StaffWorkingHoursSettings(
                staffId = staffId,
                salonId = salonId,
                weeklySchedule = weeklySchedule,
                holidays = holidays,
                customClosures = customClosures,
            )
        }

        private fun createDefaultWeeklySchedule(): Map<DayOfWeek, DaySchedule> {
            return mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.SATURDAY to DaySchedule.dayOff(),
                DayOfWeek.SUNDAY to DaySchedule.dayOff(),
            )
        }
    }

    class StaffHolidayBuilder(private val salonId: SalonId) {
        private var name: String = "Test Holiday"
        private var date: LocalDate = LocalDate.of(2024, 1, 1)
        private var isWorkingDay: Boolean = false
        private var type: HolidayType = HolidayType.LEGAL

        fun withName(name: String) = apply { this.name = name }

        fun withDate(date: LocalDate) = apply { this.date = date }

        fun asWorkingDay() = apply { this.isWorkingDay = true }

        fun asNonWorkingDay() = apply { this.isWorkingDay = false }

        fun withType(type: HolidayType) = apply { this.type = type }

        fun build(): StaffHoliday {
            return StaffHoliday.create(salonId, name, date, isWorkingDay, type)
        }
    }

    class StaffCustomClosureBuilder(private val salonId: SalonId) {
        private var reason: String = "Personal leave"
        private var date: LocalDate = LocalDate.of(2024, 6, 15)
        private var description: String? = null

        fun withReason(reason: String) = apply { this.reason = reason }

        fun withDate(date: LocalDate) = apply { this.date = date }

        fun withDescription(description: String?) = apply { this.description = description }

        fun build(): StaffCustomClosure {
            return StaffCustomClosure.create(salonId, reason, date, description)
        }
    }

    class UpdateStaffWorkingHoursRequestBuilder {
        private var weeklySchedule: Map<String, DayScheduleRequest> = createDefaultWeeklyScheduleRequest()
        private var holidays: List<StaffHolidayRequest> = emptyList()
        private var customClosures: List<StaffCustomClosureRequest> = emptyList()

        fun withWeeklySchedule(schedule: Map<String, DayScheduleRequest>) =
            apply {
                this.weeklySchedule = schedule
            }

        fun withHolidays(holidays: List<StaffHolidayRequest>) =
            apply {
                this.holidays = holidays
            }

        fun withCustomClosures(closures: List<StaffCustomClosureRequest>) =
            apply {
                this.customClosures = closures
            }

        fun build(): UpdateStaffWorkingHoursRequest {
            return UpdateStaffWorkingHoursRequest(
                weeklySchedule = weeklySchedule,
                holidays = holidays,
                customClosures = customClosures,
            )
        }

        private fun createDefaultWeeklyScheduleRequest(): Map<String, DayScheduleRequest> {
            return mapOf(
                "monday" to DayScheduleRequest("09:00", "17:00", true, "12:00", "13:00"),
                "tuesday" to DayScheduleRequest("09:00", "17:00", true, "12:00", "13:00"),
                "wednesday" to DayScheduleRequest("09:00", "17:00", true, "12:00", "13:00"),
                "thursday" to DayScheduleRequest("09:00", "17:00", true, "12:00", "13:00"),
                "friday" to DayScheduleRequest("09:00", "17:00", true, "12:00", "13:00"),
                "saturday" to DayScheduleRequest(null, null, false, null, null),
                "sunday" to DayScheduleRequest(null, null, false, null, null),
            )
        }
    }

    class StaffAvailabilityReportBuilder(private val date: LocalDate) {
        private var staffAvailability: List<StaffMemberAvailability> = emptyList()

        fun withStaffAvailability(availability: List<StaffMemberAvailability>) =
            apply {
                this.staffAvailability = availability
            }

        fun withAvailableStaff(staff: Staff) =
            apply {
                val availability =
                    StaffMemberAvailability(
                        staff = staff,
                        isAvailable = true,
                        workingHours = DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                        conflictingAppointments = emptyList(),
                        reason = null,
                    )
                this.staffAvailability = this.staffAvailability + availability
            }

        fun withUnavailableStaff(
            staff: Staff,
            reason: String,
        ) = apply {
            val availability =
                StaffMemberAvailability(
                    staff = staff,
                    isAvailable = false,
                    workingHours = null,
                    conflictingAppointments = emptyList(),
                    reason = reason,
                )
            this.staffAvailability = this.staffAvailability + availability
        }

        fun withStaffWithConflicts(
            staff: Staff,
            appointments: List<Appointment>,
        ) = apply {
            val availability =
                StaffMemberAvailability(
                    staff = staff,
                    isAvailable = false,
                    workingHours = DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                    conflictingAppointments = appointments,
                    reason = "Conflicting appointment",
                )
            this.staffAvailability = this.staffAvailability + availability
        }

        fun build(): StaffAvailabilityReport {
            return StaffAvailabilityReport(
                date = date,
                staffAvailability = staffAvailability,
            )
        }
    }
}
