package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationSettings as NotificationSettingsEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationSettingsId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaNotificationSettingsRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringNotificationSettingsRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.NotificationSettingsEntityMapper
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Unit tests for user-specific notification settings repository
 */
@ExtendWith(MockitoExtension::class)
@DisplayName("User-Specific Notification Settings Repository Tests")
class UserSpecificNotificationSettingsRepositoryTest {

    @Mock
    private lateinit var springRepository: SpringNotificationSettingsRepository

    @Mock
    private lateinit var mapper: NotificationSettingsEntityMapper

    private lateinit var repository: JpaNotificationSettingsRepository

    private val testUserId = UserId.generate()
    private val testSalonId = SalonId.generate()
    private val testUserId2 = UserId.generate()
    private val testSalonId2 = SalonId.generate()

    @BeforeEach
    fun setUp() {
        repository = JpaNotificationSettingsRepository(springRepository, mapper)
    }

    @Nested
    @DisplayName("Save Operations")
    inner class SaveOperationsTests {

        @Test
        fun `should save notification settings with userId and salonId`() {
            // Given
            val domainSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val entitySettings = createTestEntity(testUserId.value, testSalonId.value)
            val savedEntity = createTestEntity(testUserId.value, testSalonId.value)

            whenever(mapper.toEntity(domainSettings)).thenReturn(entitySettings)
            whenever(springRepository.save(entitySettings)).thenReturn(savedEntity)
            whenever(mapper.toDomain(savedEntity)).thenReturn(domainSettings)

            // When
            val result = repository.save(domainSettings)

            // Then
            assertEquals(domainSettings, result)
            verify(mapper).toEntity(domainSettings)
            verify(springRepository).save(entitySettings)
            verify(mapper).toDomain(savedEntity)
        }

        @Test
        fun `should handle save operation for multiple users in same salon`() {
            // Given
            val settings1 = NotificationSettings.createDefault(testUserId, testSalonId)
            val settings2 = NotificationSettings.createDefault(testUserId2, testSalonId)
            
            val entity1 = createTestEntity(testUserId.value, testSalonId.value)
            val entity2 = createTestEntity(testUserId2.value, testSalonId.value)

            whenever(mapper.toEntity(settings1)).thenReturn(entity1)
            whenever(mapper.toEntity(settings2)).thenReturn(entity2)
            whenever(springRepository.save(entity1)).thenReturn(entity1)
            whenever(springRepository.save(entity2)).thenReturn(entity2)
            whenever(mapper.toDomain(entity1)).thenReturn(settings1)
            whenever(mapper.toDomain(entity2)).thenReturn(settings2)

            // When
            val result1 = repository.save(settings1)
            val result2 = repository.save(settings2)

            // Then
            assertEquals(settings1, result1)
            assertEquals(settings2, result2)
            verify(springRepository, times(2)).save(any())
        }
    }

    @Nested
    @DisplayName("Find Operations")
    inner class FindOperationsTests {

        @Test
        fun `should find notification settings by userId and salonId`() {
            // Given
            val entity = createTestEntity(testUserId.value, testSalonId.value)
            val domainSettings = NotificationSettings.createDefault(testUserId, testSalonId)

            whenever(springRepository.findByUserIdAndSalonId(testUserId.value, testSalonId.value))
                .thenReturn(entity)
            whenever(mapper.toDomain(entity)).thenReturn(domainSettings)

            // When
            val result = repository.findByUserIdAndSalonId(testUserId, testSalonId)

            // Then
            assertEquals(domainSettings, result)
            verify(springRepository).findByUserIdAndSalonId(testUserId.value, testSalonId.value)
            verify(mapper).toDomain(entity)
        }

        @Test
        fun `should return null when notification settings not found`() {
            // Given
            whenever(springRepository.findByUserIdAndSalonId(testUserId.value, testSalonId.value))
                .thenReturn(null)

            // When
            val result = repository.findByUserIdAndSalonId(testUserId, testSalonId)

            // Then
            assertNull(result)
            verify(springRepository).findByUserIdAndSalonId(testUserId.value, testSalonId.value)
            verify(mapper, never()).toDomain(any())
        }

        @Test
        fun `should find all notification settings for a salon`() {
            // Given
            val entity1 = createTestEntity(testUserId.value, testSalonId.value)
            val entity2 = createTestEntity(testUserId2.value, testSalonId.value)
            val entities = listOf(entity1, entity2)
            
            val settings1 = NotificationSettings.createDefault(testUserId, testSalonId)
            val settings2 = NotificationSettings.createDefault(testUserId2, testSalonId)

            whenever(springRepository.findBySalonId(testSalonId.value)).thenReturn(entities)
            whenever(mapper.toDomain(entity1)).thenReturn(settings1)
            whenever(mapper.toDomain(entity2)).thenReturn(settings2)

            // When
            val result = repository.findBySalonId(testSalonId)

            // Then
            assertEquals(2, result.size)
            assertTrue(result.contains(settings1))
            assertTrue(result.contains(settings2))
            verify(springRepository).findBySalonId(testSalonId.value)
            verify(mapper, times(2)).toDomain(any())
        }

        @Test
        fun `should return empty list when no settings found for salon`() {
            // Given
            whenever(springRepository.findBySalonId(testSalonId.value)).thenReturn(emptyList())

            // When
            val result = repository.findBySalonId(testSalonId)

            // Then
            assertTrue(result.isEmpty())
            verify(springRepository).findBySalonId(testSalonId.value)
            verify(mapper, never()).toDomain(any())
        }
    }

    @Nested
    @DisplayName("Existence Check Operations")
    inner class ExistenceCheckTests {

        @Test
        fun `should check if notification settings exist for user and salon`() {
            // Given
            whenever(springRepository.existsByUserIdAndSalonId(testUserId.value, testSalonId.value))
                .thenReturn(true)

            // When
            val result = repository.existsByUserIdAndSalonId(testUserId, testSalonId)

            // Then
            assertTrue(result)
            verify(springRepository).existsByUserIdAndSalonId(testUserId.value, testSalonId.value)
        }

        @Test
        fun `should return false when notification settings do not exist`() {
            // Given
            whenever(springRepository.existsByUserIdAndSalonId(testUserId.value, testSalonId.value))
                .thenReturn(false)

            // When
            val result = repository.existsByUserIdAndSalonId(testUserId, testSalonId)

            // Then
            assertFalse(result)
            verify(springRepository).existsByUserIdAndSalonId(testUserId.value, testSalonId.value)
        }
    }

    @Nested
    @DisplayName("Delete Operations")
    inner class DeleteOperationsTests {

        @Test
        fun `should delete notification settings by userId and salonId`() {
            // When
            repository.deleteByUserIdAndSalonId(testUserId, testSalonId)

            // Then
            verify(springRepository).deleteByUserIdAndSalonId(testUserId.value, testSalonId.value)
        }

        @Test
        fun `should handle multiple delete operations`() {
            // When
            repository.deleteByUserIdAndSalonId(testUserId, testSalonId)
            repository.deleteByUserIdAndSalonId(testUserId2, testSalonId)
            repository.deleteByUserIdAndSalonId(testUserId, testSalonId2)

            // Then
            verify(springRepository).deleteByUserIdAndSalonId(testUserId.value, testSalonId.value)
            verify(springRepository).deleteByUserIdAndSalonId(testUserId2.value, testSalonId.value)
            verify(springRepository).deleteByUserIdAndSalonId(testUserId.value, testSalonId2.value)
        }
    }

    @Nested
    @DisplayName("User-Specific Scenarios")
    inner class UserSpecificScenariosTests {

        @Test
        fun `should handle same user with different salons`() {
            // Given
            val entity1 = createTestEntity(testUserId.value, testSalonId.value)
            val entity2 = createTestEntity(testUserId.value, testSalonId2.value)
            val settings1 = NotificationSettings.createDefault(testUserId, testSalonId)
            val settings2 = NotificationSettings.createDefault(testUserId, testSalonId2)

            whenever(springRepository.findByUserIdAndSalonId(testUserId.value, testSalonId.value))
                .thenReturn(entity1)
            whenever(springRepository.findByUserIdAndSalonId(testUserId.value, testSalonId2.value))
                .thenReturn(entity2)
            whenever(mapper.toDomain(entity1)).thenReturn(settings1)
            whenever(mapper.toDomain(entity2)).thenReturn(settings2)

            // When
            val result1 = repository.findByUserIdAndSalonId(testUserId, testSalonId)
            val result2 = repository.findByUserIdAndSalonId(testUserId, testSalonId2)

            // Then
            assertEquals(settings1, result1)
            assertEquals(settings2, result2)
            assertNotEquals(result1?.salonId, result2?.salonId)
            assertEquals(result1?.userId, result2?.userId)
        }

        @Test
        fun `should handle different users in same salon`() {
            // Given
            val entity1 = createTestEntity(testUserId.value, testSalonId.value)
            val entity2 = createTestEntity(testUserId2.value, testSalonId.value)
            val settings1 = NotificationSettings.createDefault(testUserId, testSalonId)
            val settings2 = NotificationSettings.createDefault(testUserId2, testSalonId)

            whenever(springRepository.findByUserIdAndSalonId(testUserId.value, testSalonId.value))
                .thenReturn(entity1)
            whenever(springRepository.findByUserIdAndSalonId(testUserId2.value, testSalonId.value))
                .thenReturn(entity2)
            whenever(mapper.toDomain(entity1)).thenReturn(settings1)
            whenever(mapper.toDomain(entity2)).thenReturn(settings2)

            // When
            val result1 = repository.findByUserIdAndSalonId(testUserId, testSalonId)
            val result2 = repository.findByUserIdAndSalonId(testUserId2, testSalonId)

            // Then
            assertEquals(settings1, result1)
            assertEquals(settings2, result2)
            assertEquals(result1?.salonId, result2?.salonId)
            assertNotEquals(result1?.userId, result2?.userId)
        }
    }

    private fun createTestEntity(userId: String, salonId: String): NotificationSettingsEntity {
        return NotificationSettingsEntity(
            userId = userId,
            salonId = salonId,
            pushNotificationsEnabled = true,
            soundPreference = SoundPreference.DEFAULT,
            vibrationEnabled = true,
            dndEnabled = false,
            dndStartTime = LocalTime.of(22, 0),
            dndEndTime = LocalTime.of(8, 0),
            dndAllowCritical = true,
            newAppointments = true,
            appointmentCancellations = true,
            paymentConfirmations = true,
            teamMemberUpdates = true,
            systemMaintenanceAlerts = true,
            defaultPriority = NotificationPriority.NORMAL,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
}
