package ro.animaliaprogramari.animalia.adapter.inbound.rest

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ScheduleAppointmentRequest
import ro.animaliaprogramari.animalia.domain.model.PetId
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertNull

/**
 * Test class to verify petId handling in appointment scheduling
 */
@DisplayName("Pet ID Handling Tests")
class PetIdHandlingTest {
    @Nested
    @DisplayName("ScheduleAppointmentRequest petId handling")
    inner class ScheduleAppointmentRequestTests {
        @Test
        fun `should handle null petId correctly`() {
            // Given
            val request =
                ScheduleAppointmentRequest(
                    clientId = "client-123",
                    petId = null, // Explicitly null
                    staffId = "staff-123",
                    appointmentDate = LocalDate.now().plusDays(1),
                    startTime = LocalTime.of(10, 0),
                    endTime = LocalTime.of(11, 0),
                    serviceIds = listOf("service-123"),
                    clientName = "Test Client",
                    clientPhone = null,
                    petName = "Test Pet",
                    petSpecies = "Dog",
                    petBreed = "Golden Retriever",
                    petSize = "M",
                )

            // Then
            assertNull(request.petId)
            assertEquals("Test Pet", request.petName)
            assertEquals("Dog", request.petSpecies)
        }

        @Test
        fun `should handle empty string petId correctly`() {
            // Given
            val request =
                ScheduleAppointmentRequest(
                    clientId = "client-123",
                    petId = "", // Empty string
                    staffId = "staff-123",
                    appointmentDate = LocalDate.now().plusDays(1),
                    startTime = LocalTime.of(10, 0),
                    endTime = LocalTime.of(11, 0),
                    serviceIds = listOf("service-123"),
                    clientName = "Test Client",
                    clientPhone = null,
                    petName = "Test Pet",
                    petSpecies = "Cat",
                    petBreed = "Persian",
                    petSize = "S",
                )

            // Then
            assertEquals("", request.petId) // Empty string is preserved in DTO
            assertEquals("Test Pet", request.petName)
            assertEquals("Cat", request.petSpecies)
        }

        @Test
        fun `should handle valid petId correctly`() {
            // Given
            val validPetId = PetId.generate().value
            val request =
                ScheduleAppointmentRequest(
                    clientId = "client-123",
                    petId = validPetId,
                    staffId = "staff-123",
                    appointmentDate = LocalDate.now().plusDays(1),
                    startTime = LocalTime.of(10, 0),
                    endTime = LocalTime.of(11, 0),
                    serviceIds = listOf("service-123"),
                    clientName = null,
                    clientPhone = null,
                    petName = null,
                    petSpecies = null,
                    petBreed = null,
                    petSize = null,
                )

            // Then
            assertEquals(validPetId, request.petId)
            assertNull(request.petName) // Not needed when petId is provided
            assertNull(request.petSpecies) // Not needed when petId is provided
        }
    }

    @Nested
    @DisplayName("PetId value object validation")
    inner class PetIdValidationTests {
        @Test
        fun `should create PetId from valid string`() {
            // Given
            val validId = "valid-pet-id-123"

            // When
            val petId = PetId.of(validId)

            // Then
            assertEquals(validId, petId.value)
        }

        @Test
        fun `should throw exception for blank PetId`() {
            // Given
            val blankId = ""

            // When & Then
            try {
                PetId.of(blankId)
                throw AssertionError("Expected IllegalArgumentException")
            } catch (e: IllegalArgumentException) {
                assertEquals("Pet ID cannot be blank", e.message)
            }
        }

        @Test
        fun `should generate valid PetId`() {
            // When
            val petId = PetId.generate()

            // Then
            assert(petId.value.isNotBlank())
            assert(petId.value.length > 10) // UUID should be longer than 10 chars
        }
    }
}
