package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import java.time.ZoneOffset
import java.time.ZonedDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/**
 * Test class to verify ZonedDateTime JSON deserialization in Block Time DTOs
 */
@DisplayName("Block Time JSON Deserialization Tests")
class BlockTimeJsonDeserializationTest {
    private val objectMapper =
        ObjectMapper().apply {
            registerModule(JavaTimeModule())
            registerModule(KotlinModule.Builder().build())
        }

    @Test
    @DisplayName("Should deserialize CreateBlockTimeRequest with UTC time zone")
    fun `should deserialize CreateBlockTimeRequest with UTC time zone`() {
        // Given - JSON with UTC time zone (the problematic format from the error)
        val json =
            """
            {
                "startTime": "2025-06-10T10:15:00.000Z",
                "endTime": "2025-06-10T11:15:00.000Z",
                "reason": "Pauză",
                "staffIds": ["staff-123"],
                "isRecurring": false
            }
            """.trimIndent()

        // When & Then - Should not throw exception
        val request =
            assertDoesNotThrow {
                objectMapper.readValue(json, CreateBlockTimeRequest::class.java)
            }

        // Verify the parsed values
        assertNotNull(request)
        assertEquals(ZonedDateTime.of(2025, 6, 10, 10, 15, 0, 0, ZoneOffset.UTC), request.startTime)
        assertEquals(ZonedDateTime.of(2025, 6, 10, 11, 15, 0, 0, ZoneOffset.UTC), request.endTime)
        assertEquals("Pauză", request.reason)
        assertEquals(listOf("staff-123"), request.staffIds)
        assertEquals(false, request.isRecurring)
    }

    @Test
    @DisplayName("Should deserialize CreateBlockTimeRequest with different time zones")
    fun `should deserialize CreateBlockTimeRequest with different time zones`() {
        // Given - JSON with Bucharest time zone
        val json =
            """
            {
                "startTime": "2025-06-10T13:15:00.000+03:00",
                "endTime": "2025-06-10T14:15:00.000+03:00",
                "reason": "Întâlnire",
                "staffIds": ["staff-456"],
                "isRecurring": false
            }
            """.trimIndent()

        // When & Then - Should not throw exception
        val request =
            assertDoesNotThrow {
                objectMapper.readValue(json, CreateBlockTimeRequest::class.java)
            }

        // Verify the parsed values - Jackson converts to UTC automatically
        assertNotNull(request)
        // The time zone +03:00 is converted to UTC, so 13:15+03:00 becomes 10:15Z
        assertEquals(ZonedDateTime.of(2025, 6, 10, 10, 15, 0, 0, ZoneOffset.UTC), request.startTime)
        assertEquals(ZonedDateTime.of(2025, 6, 10, 11, 15, 0, 0, ZoneOffset.UTC), request.endTime)
        assertEquals("Întâlnire", request.reason)
        assertEquals(listOf("staff-456"), request.staffIds)
    }

    @Test
    @DisplayName("Should deserialize UpdateBlockTimeRequest with UTC time zone")
    fun `should deserialize UpdateBlockTimeRequest with UTC time zone`() {
        // Given
        val json =
            """
            {
                "startTime": "2025-06-10T08:30:00.000Z",
                "endTime": "2025-06-10T09:30:00.000Z",
                "reason": "Pauză modificată"
            }
            """.trimIndent()

        // When & Then
        val request =
            assertDoesNotThrow {
                objectMapper.readValue(json, UpdateBlockTimeRequest::class.java)
            }

        assertNotNull(request)
        assertEquals(ZonedDateTime.of(2025, 6, 10, 8, 30, 0, 0, ZoneOffset.UTC), request.startTime)
        assertEquals(ZonedDateTime.of(2025, 6, 10, 9, 30, 0, 0, ZoneOffset.UTC), request.endTime)
        assertEquals("Pauză modificată", request.reason)
    }

    @Test
    @DisplayName("Should deserialize CheckTimeAvailabilityRequest with UTC time zone")
    fun `should deserialize CheckTimeAvailabilityRequest with UTC time zone`() {
        // Given
        val json =
            """
            {
                "startTime": "2025-06-10T14:00:00.000Z",
                "endTime": "2025-06-10T15:00:00.000Z",
                "staffIds": ["staff-789", "staff-101"]
            }
            """.trimIndent()

        // When & Then
        val request =
            assertDoesNotThrow {
                objectMapper.readValue(json, CheckTimeAvailabilityRequest::class.java)
            }

        assertNotNull(request)
        assertEquals(ZonedDateTime.of(2025, 6, 10, 14, 0, 0, 0, ZoneOffset.UTC), request.startTime)
        assertEquals(ZonedDateTime.of(2025, 6, 10, 15, 0, 0, 0, ZoneOffset.UTC), request.endTime)
        assertEquals(listOf("staff-789", "staff-101"), request.staffIds)
    }

    @Test
    @DisplayName("Should serialize and deserialize BlockTimeResponse correctly")
    fun `should serialize and deserialize BlockTimeResponse correctly`() {
        // Given
        val response =
            BlockTimeResponse(
                blockId = "block-123",
                salonId = "salon-456",
                startTime = ZonedDateTime.of(2025, 6, 10, 10, 0, 0, 0, ZoneOffset.UTC),
                endTime = ZonedDateTime.of(2025, 6, 10, 11, 0, 0, 0, ZoneOffset.UTC),
                duration = 60,
                reason = "Test Block",
                staffIds = listOf("staff-123"),
                createdBy = "user-456",
                createdAt = java.time.LocalDateTime.of(2025, 6, 10, 9, 0),
                updatedAt = java.time.LocalDateTime.of(2025, 6, 10, 9, 0),
                isRecurring = false,
                status = "ACTIVE",
            )

        // When - Serialize to JSON
        val json = objectMapper.writeValueAsString(response)

        // Then - Deserialize back
        val deserializedResponse =
            assertDoesNotThrow {
                objectMapper.readValue(json, BlockTimeResponse::class.java)
            }

        assertEquals(response.blockId, deserializedResponse.blockId)
        assertEquals(response.startTime, deserializedResponse.startTime)
        assertEquals(response.endTime, deserializedResponse.endTime)
        assertEquals(response.reason, deserializedResponse.reason)
    }
}
