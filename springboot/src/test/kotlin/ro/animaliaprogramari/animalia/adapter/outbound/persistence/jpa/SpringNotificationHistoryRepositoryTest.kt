package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import org.springframework.data.domain.PageRequest
import org.springframework.test.context.ActiveProfiles
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationHistory
import java.time.LocalDateTime

/**
 * Integration tests for SpringNotificationHistoryRepository
 * Tests all repository methods with real database interactions
 */
@DataJpaTest
@ActiveProfiles("test")
@DisplayName("SpringNotificationHistoryRepository Tests")
class SpringNotificationHistoryRepositoryTest {

    @Autowired
    private lateinit var repository: SpringNotificationHistoryRepository

    @Autowired
    private lateinit var entityManager: TestEntityManager

    private lateinit var testSalonId: String
    private lateinit var testAppointmentId: String
    private lateinit var testClientId: String

    @BeforeEach
    fun setUp() {
        testSalonId = "salon-123"
        testAppointmentId = "appointment-456"
        testClientId = "client-789"
        
        // Clear any existing data
        repository.deleteAll()
        entityManager.flush()
        entityManager.clear()
    }

    @Nested
    @DisplayName("Basic CRUD Operations")
    inner class BasicCrudOperations {

        @Test
        fun `should save and retrieve notification`() {
            // Given
            val notification = NotificationHistory(
                salonId = testSalonId,
                title = "Test Notification",
                message = "Test message",
                type = "TEST",
                appointmentId = testAppointmentId,
                clientId = testClientId
            )

            // When
            val saved = repository.save(notification)
            entityManager.flush()
            entityManager.clear()

            // Then
            val retrieved = repository.findById(saved.id).orElse(null)
            assertNotNull(retrieved)
            assertEquals(notification.salonId, retrieved.salonId)
            assertEquals(notification.title, retrieved.title)
            assertEquals(notification.message, retrieved.message)
            assertEquals(notification.type, retrieved.type)
            assertEquals(notification.appointmentId, retrieved.appointmentId)
            assertEquals(notification.clientId, retrieved.clientId)
        }

        @Test
        fun `should delete notification`() {
            // Given
            val notification = createTestNotification()
            val saved = repository.save(notification)
            entityManager.flush()

            // When
            repository.deleteById(saved.id)
            entityManager.flush()

            // Then
            assertFalse(repository.existsById(saved.id))
        }
    }

    @Nested
    @DisplayName("Query Methods")
    inner class QueryMethods {

        @Test
        fun `should find notifications by salon with filters`() {
            // Given
            createTestNotifications()

            // When
            val result = repository.findBySalonIdWithFilters(
                salonId = testSalonId,
                type = "APPOINTMENT_SCHEDULED",
                readStatus = false,
                startDate = LocalDateTime.now().minusDays(1),
                endDate = LocalDateTime.now().plusDays(1),
                pageable = PageRequest.of(0, 10)
            )

            // Then
            assertTrue(result.content.isNotEmpty())
            result.content.forEach { notification ->
                assertEquals(testSalonId, notification.salonId)
                assertEquals("APPOINTMENT_SCHEDULED", notification.type)
                assertFalse(notification.readStatus)
            }
        }

        @Test
        fun `should find recent unread notifications`() {
            // Given
            createTestNotifications()

            // When
            val result = repository.findRecentUnreadBySalonId(
                salonId = testSalonId,
                pageable = PageRequest.of(0, 5)
            )

            // Then
            assertTrue(result.content.isNotEmpty())
            result.content.forEach { notification ->
                assertEquals(testSalonId, notification.salonId)
                assertFalse(notification.readStatus)
            }
        }

        @Test
        fun `should count unread notifications by salon`() {
            // Given
            createTestNotifications()

            // When
            val count = repository.countBySalonIdAndReadStatus(testSalonId, false)

            // Then
            assertTrue(count > 0)
        }

        @Test
        fun `should find notifications by appointment ID`() {
            // Given
            createTestNotifications()

            // When
            val notifications = repository.findByAppointmentIdOrderByTimestampDesc(testAppointmentId)

            // Then
            assertTrue(notifications.isNotEmpty())
            notifications.forEach { notification ->
                assertEquals(testAppointmentId, notification.appointmentId)
            }
        }

        @Test
        fun `should find notifications by client ID`() {
            // Given
            createTestNotifications()

            // When
            val notifications = repository.findByClientIdOrderByTimestampDesc(testClientId)

            // Then
            assertTrue(notifications.isNotEmpty())
            notifications.forEach { notification ->
                assertEquals(testClientId, notification.clientId)
            }
        }

        @Test
        fun `should check if notification exists by appointment and type`() {
            // Given
            createTestNotifications()

            // When
            val exists = repository.existsByAppointmentIdAndType(testAppointmentId, "APPOINTMENT_SCHEDULED")

            // Then
            assertTrue(exists)
        }

        @Test
        fun `should find latest notification by appointment and type`() {
            // Given
            createTestNotifications()

            // When
            val notification = repository.findFirstByAppointmentIdAndTypeOrderByTimestampDesc(
                testAppointmentId, 
                "APPOINTMENT_SCHEDULED"
            )

            // Then
            assertNotNull(notification)
            assertEquals(testAppointmentId, notification?.appointmentId)
            assertEquals("APPOINTMENT_SCHEDULED", notification?.type)
        }
    }

    @Nested
    @DisplayName("Batch Operations")
    inner class BatchOperations {

        @Test
        fun `should mark notifications as read in batch`() {
            // Given
            val notifications = createTestNotifications()
            val notificationIds = notifications.map { it.id }

            // When
            val updatedCount = repository.markAsReadBatch(notificationIds)
            entityManager.flush()
            entityManager.clear()

            // Then
            assertEquals(notifications.size, updatedCount)
            
            val updatedNotifications = repository.findAllById(notificationIds)
            updatedNotifications.forEach { notification ->
                assertTrue(notification.readStatus)
            }
        }

        @Test
        fun `should mark all notifications as read for salon`() {
            // Given
            createTestNotifications()
            val initialUnreadCount = repository.countBySalonIdAndReadStatus(testSalonId, false)

            // When
            val updatedCount = repository.markAllAsReadBySalonId(testSalonId)
            entityManager.flush()

            // Then
            assertEquals(initialUnreadCount, updatedCount.toLong())
            
            val finalUnreadCount = repository.countBySalonIdAndReadStatus(testSalonId, false)
            assertEquals(0, finalUnreadCount)
        }

        @Test
        fun `should delete old notifications`() {
            // Given
            createOldTestNotifications()
            val cutoffDate = LocalDateTime.now().minusDays(30)

            // When
            val deletedCount = repository.deleteOldNotifications(cutoffDate)
            entityManager.flush()

            // Then
            assertTrue(deletedCount > 0)
        }
    }

    @Nested
    @DisplayName("Statistics and Analytics")
    inner class StatisticsAndAnalytics {

        @Test
        fun `should get notification statistics`() {
            // Given
            createTestNotifications()

            // When
            val statistics = repository.getNotificationStatistics(
                testSalonId,
                LocalDateTime.now().minusDays(7)
            )

            // Then
            assertTrue(statistics.isNotEmpty())
            statistics.forEach { stat ->
                assertTrue(stat.size >= 3) // type, total count, unread count
            }
        }

        @Test
        fun `should get dashboard summary`() {
            // Given
            createTestNotifications()

            // When
            val summary = repository.getDashboardSummary(
                testSalonId,
                LocalDateTime.now().minusDays(7)
            )

            // Then
            assertTrue(summary.isNotEmpty())
            summary.forEach { entry ->
                assertTrue(entry.containsKey("type"))
                assertTrue(entry.containsKey("total"))
                assertTrue(entry.containsKey("unread"))
                assertTrue(entry.containsKey("lastNotification"))
            }
        }

        @Test
        fun `should find notifications by type and date range`() {
            // Given
            createTestNotifications()

            // When
            val notifications = repository.findByTypeAndDateRange(
                salonId = testSalonId,
                type = "APPOINTMENT_SCHEDULED",
                startDate = LocalDateTime.now().minusDays(1),
                endDate = LocalDateTime.now().plusDays(1)
            )

            // Then
            assertTrue(notifications.isNotEmpty())
            notifications.forEach { notification ->
                assertEquals(testSalonId, notification.salonId)
                assertEquals("APPOINTMENT_SCHEDULED", notification.type)
            }
        }
    }

    // Helper methods
    private fun createTestNotification(
        type: String = "TEST",
        readStatus: Boolean = false,
        appointmentId: String? = testAppointmentId,
        clientId: String? = testClientId
    ): NotificationHistory {
        return NotificationHistory(
            salonId = testSalonId,
            title = "Test $type",
            message = "Test message for $type",
            type = type,
            readStatus = readStatus,
            appointmentId = appointmentId,
            clientId = clientId
        )
    }

    private fun createTestNotifications(): List<NotificationHistory> {
        val notifications = listOf(
            createTestNotification("APPOINTMENT_SCHEDULED", false),
            createTestNotification("APPOINTMENT_REMINDER", false),
            createTestNotification("APPOINTMENT_CANCELLED", true),
            createTestNotification("STAFF_INVITATION", false, null, null)
        )
        
        return repository.saveAll(notifications).also {
            entityManager.flush()
        }
    }

    private fun createOldTestNotifications(): List<NotificationHistory> {
        val oldTimestamp = LocalDateTime.now().minusDays(60)
        val notifications = listOf(
            NotificationHistory(
                salonId = testSalonId,
                title = "Old Notification 1",
                message = "Old message 1",
                type = "OLD_TYPE",
                timestamp = oldTimestamp
            ),
            NotificationHistory(
                salonId = testSalonId,
                title = "Old Notification 2",
                message = "Old message 2",
                type = "OLD_TYPE",
                timestamp = oldTimestamp.minusDays(10)
            )
        )
        
        return repository.saveAll(notifications).also {
            entityManager.flush()
        }
    }
}
