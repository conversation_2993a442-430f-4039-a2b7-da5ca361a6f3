package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime

class StaffWorkingHoursDtoMapperTest {
    private val mapper = StaffWorkingHoursDtoMapper()
    private val staffId = StaffId.of("staff-123")
    private val salonId = SalonId.of("salon-456")
    private val userId = UserId.of("user-789")

    @Test
    fun `should convert domain model to response DTO`() {
        // Given
        val workingHours = StaffWorkingHoursSettings.createDefault(staffId, salonId)

        // When
        val response = mapper.toResponse(workingHours)

        // Then
        assertEquals(staffId.value, response.staffId)
        assertEquals(salonId.value, response.salonId)
        assertEquals(7, response.weeklySchedule.size)
        assertTrue(response.holidays.isNotEmpty())
        assertTrue(response.customClosures.isEmpty())

        // Check Monday schedule
        val mondaySchedule = response.weeklySchedule["monday"]
        assertNotNull(mondaySchedule)
        assertTrue(mondaySchedule!!.isWorkingDay)
        assertEquals("09:00", mondaySchedule.startTime)
        assertEquals("17:00", mondaySchedule.endTime)
        assertEquals("12:00", mondaySchedule.breakStart)
        assertEquals("13:00", mondaySchedule.breakEnd)

        // Check Sunday schedule (day off)
        val sundaySchedule = response.weeklySchedule["sunday"]
        assertNotNull(sundaySchedule)
        assertFalse(sundaySchedule!!.isWorkingDay)
        assertNull(sundaySchedule.startTime)
        assertNull(sundaySchedule.endTime)
    }

    @Test
    fun `should convert request DTO to update command`() {
        // Given
        val request =
            UpdateStaffWorkingHoursRequest(
                weeklySchedule =
                    mapOf(
                        "monday" to
                            DayScheduleRequest(
                                startTime = "08:00",
                                endTime = "16:00",
                                isWorkingDay = true,
                                breakStart = "12:00",
                                breakEnd = "13:00",
                            ),
                        "tuesday" to
                            DayScheduleRequest(
                                startTime = "08:00",
                                endTime = "16:00",
                                isWorkingDay = true,
                                breakStart = "12:00",
                                breakEnd = "13:00",
                            ),
                        "wednesday" to
                            DayScheduleRequest(
                                startTime = "08:00",
                                endTime = "16:00",
                                isWorkingDay = true,
                                breakStart = "12:00",
                                breakEnd = "13:00",
                            ),
                        "thursday" to
                            DayScheduleRequest(
                                startTime = "08:00",
                                endTime = "16:00",
                                isWorkingDay = true,
                                breakStart = "12:00",
                                breakEnd = "13:00",
                            ),
                        "friday" to
                            DayScheduleRequest(
                                startTime = "08:00",
                                endTime = "16:00",
                                isWorkingDay = true,
                                breakStart = "12:00",
                                breakEnd = "13:00",
                            ),
                        "saturday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                        "sunday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                    ),
                holidays =
                    listOf(
                        StaffHolidayRequest(
                            name = "Custom Holiday",
                            date = LocalDate.of(2024, 7, 4),
                            isWorkingDay = false,
                            type = "LEGAL",
                        ),
                    ),
                customClosures =
                    listOf(
                        StaffCustomClosureRequest(
                            reason = "Personal leave",
                            date = LocalDate.of(2024, 8, 15),
                            description = "Family event",
                        ),
                    ),
            )

        // When
        val command = mapper.toUpdateCommand(staffId, salonId, userId, request)

        // Then
        assertEquals(staffId, command.staffId)
        assertEquals(salonId, command.salonId)
        assertEquals(userId, command.updaterUserId)
        assertEquals(7, command.weeklySchedule.size)

        // Check Monday schedule
        val mondaySchedule = command.weeklySchedule[DayOfWeek.MONDAY]
        assertNotNull(mondaySchedule)
        assertTrue(mondaySchedule!!.isWorkingDay)
        assertEquals(LocalTime.of(8, 0), mondaySchedule.startTime)
        assertEquals(LocalTime.of(16, 0), mondaySchedule.endTime)

        // Check holidays
        assertEquals(1, command.holidays.size)
        assertEquals("Custom Holiday", command.holidays[0].name)
        assertEquals(LocalDate.of(2024, 7, 4), command.holidays[0].date)

        // Check custom closures
        assertEquals(1, command.customClosures.size)
        assertEquals("Personal leave", command.customClosures[0].reason)
        assertEquals(LocalDate.of(2024, 8, 15), command.customClosures[0].date)
        assertEquals("Family event", command.customClosures[0].description)
    }

    @Test
    fun `should convert query parameters to get working hours query`() {
        // When
        val query = mapper.toGetWorkingHoursQuery("staff-123", "salon-456", userId)

        // Then
        assertEquals(StaffId.of("staff-123"), query.staffId)
        assertEquals(SalonId.of("salon-456"), query.salonId)
        assertEquals(userId, query.requesterId)
    }

    @Test
    fun `should convert availability query parameters to domain query`() {
        // When
        val query =
            mapper.toGetAvailabilityQuery(
                "salon-456",
                LocalDate.of(2024, 1, 8),
                "10:00",
                "12:00",
                userId,
            )

        // Then
        assertEquals(SalonId.of("salon-456"), query.salonId)
        assertEquals(LocalDate.of(2024, 1, 8), query.date)
        assertEquals(LocalTime.of(10, 0), query.startTime)
        assertEquals(LocalTime.of(12, 0), query.endTime)
        assertEquals(userId, query.requesterId)
    }

    @Test
    fun `should convert availability query without time parameters`() {
        // When
        val query =
            mapper.toGetAvailabilityQuery(
                "salon-456",
                LocalDate.of(2024, 1, 8),
                null,
                null,
                userId,
            )

        // Then
        assertEquals(SalonId.of("salon-456"), query.salonId)
        assertEquals(LocalDate.of(2024, 1, 8), query.date)
        assertNull(query.startTime)
        assertNull(query.endTime)
        assertEquals(userId, query.requesterId)
    }

    @Test
    fun `should convert availability report to response DTO`() {
        // Given
        val staff = Staff.createGroomer(userId, salonId, StaffPermissions.defaultGroomerAccess())
        val daySchedule = DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0))
        val testService =
            TestDataBuilder.aSalonService()
                .withSalonId(salonId)
                .withName("Basic Grooming")
                .withPrice(Money.of(BigDecimal("50.00")))
                .withDuration(Duration.ofMinutes(60))
                .build()

        val appointment =
            Appointment.create(
                salonId = salonId,
                clientId = ClientId.of("client-123"),
                petId = PetId.of("pet-456"),
                staffId = staffId,
                appointmentDate = LocalDate.of(2024, 1, 8),
                startTime = LocalTime.of(10, 0),
                endTime = LocalTime.of(11, 0),
                salonServices = listOf(testService),
            )

        val staffAvailability =
            StaffMemberAvailability(
                staff = staff,
                isAvailable = false,
                workingHours = daySchedule,
                conflictingAppointments = listOf(appointment),
                reason = "Conflicting appointment",
            )

        val report =
            StaffAvailabilityReport(
                date = LocalDate.of(2024, 1, 8),
                staffAvailability = listOf(staffAvailability),
            )

        // When
        val response = mapper.toAvailabilityResponse(report)

        // Then
        assertEquals(LocalDate.of(2024, 1, 8), response.date)
        assertEquals(1, response.staffAvailability.size)

        val staffResponse = response.staffAvailability[0]
        assertEquals(userId.value, staffResponse.staffId)
        assertFalse(staffResponse.isAvailable)
        assertEquals("Conflicting appointment", staffResponse.reason)
        assertEquals(1, staffResponse.conflictingAppointments.size)

        val appointmentResponse = staffResponse.conflictingAppointments[0]
        assertEquals(appointment.id.value, appointmentResponse.appointmentId)
        assertEquals("10:00", appointmentResponse.startTime)
        assertEquals("11:00", appointmentResponse.endTime)
    }

    @Test
    fun `should handle day off schedule in request`() {
        // Given
        val request =
            UpdateStaffWorkingHoursRequest(
                weeklySchedule =
                    mapOf(
                        "monday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                        "tuesday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                        "wednesday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                        "thursday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                        "friday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                        "saturday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                        "sunday" to
                            DayScheduleRequest(
                                startTime = null,
                                endTime = null,
                                isWorkingDay = false,
                                breakStart = null,
                                breakEnd = null,
                            ),
                    ),
            )

        // When
        val command = mapper.toUpdateCommand(staffId, salonId, userId, request)

        // Then
        val mondaySchedule = command.weeklySchedule[DayOfWeek.MONDAY]
        assertNotNull(mondaySchedule)
        assertFalse(mondaySchedule!!.isWorkingDay)
        assertNull(mondaySchedule.startTime)
        assertNull(mondaySchedule.endTime)
    }
}
