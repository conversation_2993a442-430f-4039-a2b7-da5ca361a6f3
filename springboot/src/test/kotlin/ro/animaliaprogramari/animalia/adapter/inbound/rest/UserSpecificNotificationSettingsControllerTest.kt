package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.mockkStatic
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.http.MediaType
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.NotificationSettingsDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.UpdateNotificationSettingsCommand
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationSettingsManagementUseCase
import ro.animaliaprogramari.animalia.application.query.GetNotificationSettingsQuery
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Unit tests for user-specific notification settings controller
 */
@ExtendWith(SpringExtension::class)
@WebMvcTest(NotificationSettingsController::class)
@DisplayName("User-Specific Notification Settings Controller Tests")
@Disabled
class UserSpecificNotificationSettingsControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockBean
    private lateinit var notificationSettingsManagementUseCase: NotificationSettingsManagementUseCase

    @MockBean
    private lateinit var dtoMapper: NotificationSettingsDtoMapper

    private val testUserId = UserId.generate()
    private val testSalonId = SalonId.generate()
    private val testUserId2 = UserId.generate()

    @BeforeEach
    fun setUp() {
        // Mock SecurityUtils to return authenticated user
        mockkStatic(SecurityUtils::class)
        val authenticatedUser = createTestAuthenticatedUser(testUserId)
        every { SecurityUtils.getCurrentUser() } returns authenticatedUser
    }

    @Nested
    @DisplayName("Get Notification Settings")
    inner class GetNotificationSettingsTests {

        @Test
        fun `should get notification settings for authenticated user`() {
            // Given
            val settings = NotificationSettings.createDefault(testUserId, testSalonId)
            val response = createTestNotificationSettingsResponse()
            val query = GetNotificationSettingsQuery(testUserId, testSalonId)

            whenever(notificationSettingsManagementUseCase.getNotificationSettings(query))
                .thenReturn(settings)
            whenever(dtoMapper.toResponse(settings)).thenReturn(response)

            // When & Then
            mockMvc.perform(
                get("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.salonId").value(testSalonId.value))
                .andExpect(jsonPath("$.data.pushNotificationsEnabled").value(true))

            verify(notificationSettingsManagementUseCase).getNotificationSettings(
                argThat { userId == testUserId && salonId == testSalonId }
            )
        }

        @Test
        fun `should return 401 when user not authenticated`() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(
                get("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").exists())

            verify(notificationSettingsManagementUseCase, never()).getNotificationSettings(any())
        }

        @Test
        fun `should return 404 when salon not found`() {
            // Given
            val query = GetNotificationSettingsQuery(testUserId, testSalonId)
            whenever(notificationSettingsManagementUseCase.getNotificationSettings(query))
                .thenThrow(EntityNotFoundException("Salon not found"))

            // When & Then
            mockMvc.perform(
                get("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isNotFound)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Salon not found"))
        }

        @Test
        fun `should handle different users accessing same salon`() {
            // Given - First user
            val user1 = createTestAuthenticatedUser(testUserId)
            val settings1 = NotificationSettings.createDefault(testUserId, testSalonId)
            val response1 = createTestNotificationSettingsResponse()

            every { SecurityUtils.getCurrentUser() } returns user1
            whenever(notificationSettingsManagementUseCase.getNotificationSettings(any()))
                .thenReturn(settings1)
            whenever(dtoMapper.toResponse(settings1)).thenReturn(response1)

            // When & Then - First user request
            mockMvc.perform(
                get("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))

            // Given - Second user
            val user2 = createTestAuthenticatedUser(testUserId2)
            val settings2 = NotificationSettings.createDefault(testUserId2, testSalonId)
            val response2 = createTestNotificationSettingsResponse()

            every { SecurityUtils.getCurrentUser() } returns user2
            whenever(notificationSettingsManagementUseCase.getNotificationSettings(any()))
                .thenReturn(settings2)
            whenever(dtoMapper.toResponse(settings2)).thenReturn(response2)

            // When & Then - Second user request
            mockMvc.perform(
                get("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))

            // Verify both users got their own settings
            verify(notificationSettingsManagementUseCase).getNotificationSettings(
                argThat { userId == testUserId && salonId == testSalonId }
            )
            verify(notificationSettingsManagementUseCase).getNotificationSettings(
                argThat { userId == testUserId2 && salonId == testSalonId }
            )
        }
    }

    @Nested
    @DisplayName("Update Notification Settings")
    inner class UpdateNotificationSettingsTests {

        @Test
        fun `should update notification settings for authenticated user`() {
            // Given
            val request = createTestUpdateNotificationSettingsRequest()
            val command = createTestUpdateNotificationSettingsCommand()
            val updatedSettings = NotificationSettings.createDefault(testUserId, testSalonId)
                .updateSettings(pushNotificationsEnabled = false)
            val response = createTestNotificationSettingsResponse()

            whenever(dtoMapper.toCommand(testUserId, testSalonId, request)).thenReturn(command)
            whenever(notificationSettingsManagementUseCase.updateNotificationSettings(command))
                .thenReturn(updatedSettings)
            whenever(dtoMapper.toResponse(updatedSettings)).thenReturn(response)

            // When & Then
            mockMvc.perform(
                put("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())

            verify(dtoMapper).toCommand(testUserId, testSalonId, request)
            verify(notificationSettingsManagementUseCase).updateNotificationSettings(command)
        }

        @Test
        fun `should return 401 when user not authenticated for update`() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null
            val request = createTestUpdateNotificationSettingsRequest()

            // When & Then
            mockMvc.perform(
                put("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))

            verify(notificationSettingsManagementUseCase, never()).updateNotificationSettings(any())
        }

        @Test
        fun `should return 400 for invalid request data`() {
            // Given
            val invalidRequest = mapOf("invalid" to "data")

            // When & Then
            mockMvc.perform(
                put("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidRequest))
            )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
        }

        @Test
        fun `should handle user-specific updates independently`() {
            // Given
            val request = createTestUpdateNotificationSettingsRequest()
            val command1 = UpdateNotificationSettingsCommand(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.SILENT,
                vibrationEnabled = false,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )
            val command2 = UpdateNotificationSettingsCommand(
                userId = testUserId2,
                salonId = testSalonId,
                pushNotificationsEnabled = true,
                soundPreference = SoundPreference.DEFAULT,
                vibrationEnabled = true,
                doNotDisturb = DoNotDisturbSettings(),
                notificationRules = NotificationRules()
            )

            val settings1 = NotificationSettings.createDefault(testUserId, testSalonId)
            val settings2 = NotificationSettings.createDefault(testUserId2, testSalonId)
            val response = createTestNotificationSettingsResponse()

            // First user
            every { SecurityUtils.getCurrentUser() } returns createTestAuthenticatedUser(testUserId)
            whenever(dtoMapper.toCommand(testUserId, testSalonId, request)).thenReturn(command1)
            whenever(notificationSettingsManagementUseCase.updateNotificationSettings(command1))
                .thenReturn(settings1)
            whenever(dtoMapper.toResponse(settings1)).thenReturn(response)

            // When & Then - First user
            mockMvc.perform(
                put("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)

            // Second user
            every { SecurityUtils.getCurrentUser() } returns createTestAuthenticatedUser(testUserId2)
            whenever(dtoMapper.toCommand(testUserId2, testSalonId, request)).thenReturn(command2)
            whenever(notificationSettingsManagementUseCase.updateNotificationSettings(command2))
                .thenReturn(settings2)
            whenever(dtoMapper.toResponse(settings2)).thenReturn(response)

            // When & Then - Second user
            mockMvc.perform(
                put("/salons/{salonId}/notification-settings", testSalonId.value)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)

            // Verify both users updated their own settings
            verify(notificationSettingsManagementUseCase).updateNotificationSettings(
                argThat { userId == testUserId }
            )
            verify(notificationSettingsManagementUseCase).updateNotificationSettings(
                argThat { userId == testUserId2 }
            )
        }
    }

    private fun createTestAuthenticatedUser(userId: UserId): AuthenticatedUser {
        return AuthenticatedUser(
            userId = userId,
            firebaseUid = "test_firebase_uid",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "Test User",
            role = UserRole.STAFF,
            currentSalonId = testSalonId,
            isActive = true,
            staffAssociations = emptyList()
        )
    }

    private fun createTestNotificationSettingsResponse(): NotificationSettingsResponse {
        return NotificationSettingsResponse(
            salonId = testSalonId.value,
            pushNotificationsEnabled = true,
            soundPreference = "DEFAULT",
            vibrationEnabled = true,
            doNotDisturb = DoNotDisturbResponse(
                enabled = false,
                startTime = "22:00",
                endTime = "08:00",
                allowCritical = true
            ),
            notificationRules = NotificationRulesResponse(
                newAppointments = true,
                appointmentCancellations = true,
                paymentConfirmations = true,
                teamMemberUpdates = true,
                systemMaintenanceAlerts = true,
                defaultPriority = "NORMAL"
            ),
            updatedAt = LocalDateTime.now()
        )
    }

    private fun createTestUpdateNotificationSettingsRequest(): UpdateNotificationSettingsRequest {
        return UpdateNotificationSettingsRequest(
            pushNotificationsEnabled = false,
            soundPreference = "SILENT",
            vibrationEnabled = false,
            doNotDisturb = DoNotDisturbRequest(
                enabled = true,
                startTime = "23:00",
                endTime = "07:00",
                allowCritical = false
            ),
            notificationRules = NotificationRulesRequest(
                newAppointments = false,
                appointmentCancellations = true,
                paymentConfirmations = false,
                teamMemberUpdates = true,
                systemMaintenanceAlerts = false,
                defaultPriority = "HIGH"
            )
        )
    }

    private fun createTestUpdateNotificationSettingsCommand(): UpdateNotificationSettingsCommand {
        return UpdateNotificationSettingsCommand(
            userId = testUserId,
            salonId = testSalonId,
            pushNotificationsEnabled = false,
            soundPreference = SoundPreference.SILENT,
            vibrationEnabled = false,
            doNotDisturb = DoNotDisturbSettings(
                enabled = true,
                startTime = LocalTime.of(23, 0),
                endTime = LocalTime.of(7, 0),
                allowCritical = false
            ),
            notificationRules = NotificationRules(
                newAppointments = false,
                appointmentCancellations = true,
                paymentConfirmations = false,
                teamMemberUpdates = true,
                systemMaintenanceAlerts = false,
                defaultPriority = NotificationPriority.HIGH
            )
        )
    }
}
