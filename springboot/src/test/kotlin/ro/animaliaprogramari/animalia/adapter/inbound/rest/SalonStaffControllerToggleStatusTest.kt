package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import ro.animaliaprogramari.animalia.application.port.inbound.InvitationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SalonStaffManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.LocalDateTime

@WebMvcTest(SalonStaffController::class)
@Disabled
class SalonStaffControllerToggleStatusTest {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockBean
    private lateinit var salonStaffManagementUseCase: SalonStaffManagementUseCase

    @MockBean
    private lateinit var invitationManagementUseCase: InvitationManagementUseCase

    @MockBean
    private lateinit var userRepository: UserRepository

    private val testSalonId: SalonId = SalonId.generate()
    private val testStaffId: StaffId = StaffId.generate()
    private lateinit var testStaff: Staff
    private lateinit var testSalonStaffInfo: SalonStaffInfo

    @BeforeEach
    fun setUp() {
        testStaff =
            TestDataBuilder.aStaff(
                id = testStaffId,
                salonId = testSalonId,
                role = StaffRole.GROOMER,
                isActive = true,
            )

        val testUser =
            TestDataBuilder.aUser(
                id = testStaff.userId,
                name = "Test Staff Member",
                phoneNumber = "+40123456789",
            )

        testSalonStaffInfo = SalonStaffInfo.from(testStaff, testUser)
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    fun `should toggle staff status successfully`() {
        // Given
        val updatedStaff = testStaff.copy(isActive = false, updatedAt = LocalDateTime.now())

        every {
            salonStaffManagementUseCase.toggleStaffStatusByStaffId(testSalonId, testStaffId)
        } returns updatedStaff

        every {
            salonStaffManagementUseCase.getSalonStaff(testSalonId, false, null)
        } returns listOf(testSalonStaffInfo.copy(staff = updatedStaff))

        // When & Then
        mockMvc.perform(
            patch("/salons/${testSalonId.value}/staff/${testStaffId.value}/toggle-status")
                .contentType(MediaType.APPLICATION_JSON),
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.id").value(testStaffId.value))
            .andExpect(jsonPath("$.data.isActive").value(false))

        verify(exactly = 1) {
            salonStaffManagementUseCase.toggleStaffStatusByStaffId(testSalonId, testStaffId)
        }
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    fun `should return 404 when staff not found`() {
        // Given
        every {
            salonStaffManagementUseCase.toggleStaffStatusByStaffId(testSalonId, testStaffId)
        } throws IllegalArgumentException("Angajatul nu a fost găsit")

        // When & Then
        mockMvc.perform(
            patch("/salons/${testSalonId.value}/staff/${testStaffId.value}/toggle-status")
                .contentType(MediaType.APPLICATION_JSON),
        )
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.error").value("Angajatul nu a fost găsit"))

        verify(exactly = 1) {
            salonStaffManagementUseCase.toggleStaffStatusByStaffId(testSalonId, testStaffId)
        }
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    fun `should return 400 when staff does not belong to salon`() {
        // Given
        every {
            salonStaffManagementUseCase.toggleStaffStatusByStaffId(testSalonId, testStaffId)
        } throws IllegalArgumentException("Angajatul nu aparține acestui salon")

        // When & Then
        mockMvc.perform(
            patch("/salons/${testSalonId.value}/staff/${testStaffId.value}/toggle-status")
                .contentType(MediaType.APPLICATION_JSON),
        )
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.error").value("Angajatul nu aparține acestui salon"))

        verify(exactly = 1) {
            salonStaffManagementUseCase.toggleStaffStatusByStaffId(testSalonId, testStaffId)
        }
    }
}
