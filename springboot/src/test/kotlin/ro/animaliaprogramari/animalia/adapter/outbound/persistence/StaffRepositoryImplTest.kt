package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.orm.jpa.JpaSystemException
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffRoleEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaStaffRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.StaffEntityMapper
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestDataBuilder
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/**
 * Unit tests for StaffRepositoryImpl focusing on JPA instantiation issues
 */
@DisplayName("StaffRepositoryImpl")
class StaffRepositoryImplTest {
    private val jpaRepository = mockk<JpaStaffRepository>()
    private val staffEntityMapper = mockk<StaffEntityMapper>()
    private val repository = StaffRepositoryImpl(jpaRepository, staffEntityMapper)

    @BeforeEach
    fun setUp() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("save method")
    inner class SaveMethod {
        @Test
        fun `should save staff successfully when all mappings are correct`() {
            // Given
            val staff = TestDataBuilder.aStaff().build()
            val staffEntity = TestDataBuilder.aStaffEntity().build()
            val savedEntity = staffEntity

            every { staffEntityMapper.toEntity(staff) } returns staffEntity
            every { jpaRepository.save(staffEntity) } returns savedEntity
            every { staffEntityMapper.toDomain(savedEntity) } returns staff

            // When
            val result = repository.save(staff)

            // Then
            assertEquals(staff, result)
            verify { staffEntityMapper.toEntity(staff) }
            verify { jpaRepository.save(staffEntity) }
            verify { staffEntityMapper.toDomain(savedEntity) }
        }

        @Test
        fun `should throw JpaSystemException when StaffEntity cannot be instantiated`() {
            // Given
            val staff = TestDataBuilder.aStaff().build()
            val invalidEntity =
                StaffEntity(
                    id = "invalid", // This should cause validation error
                    userId = "invalid",
                    salonId = "invalid",
                    role = StaffRoleEntity.GROOMER,
                    workingHours = "{}",
                    hireDate = staff.hiredAt.toLocalDate(),
                    specializations = emptySet(),
                    isActive = true,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                )

            every { staffEntityMapper.toEntity(staff) } returns invalidEntity
            every { jpaRepository.save(invalidEntity) } throws
                JpaSystemException(
                    RuntimeException("Could not instantiate entity 'StaffEntity' due to: null"),
                )

            // When & Then
            assertThrows<JpaSystemException> {
                repository.save(staff)
            }

            verify { staffEntityMapper.toEntity(staff) }
            verify { jpaRepository.save(invalidEntity) }
        }

        @Test
        fun `should handle chief groomer staff creation correctly`() {
            // Given
            val chiefGroomerStaff =
                Staff.createChiefGroomer(
                    userId = UserId.generate(),
                    salonId = SalonId.generate(),
                )
            val staffEntity =
                TestDataBuilder.aStaffEntity()
                    .withId(chiefGroomerStaff.id)
                    .withUserId(chiefGroomerStaff.userId)
                    .withSalonId(chiefGroomerStaff.salonId)
                    .withRole(StaffRoleEntity.CHIEF_GROOMER)
                    .build()
            val savedEntity = staffEntity

            every { staffEntityMapper.toEntity(chiefGroomerStaff) } returns staffEntity
            every { jpaRepository.save(staffEntity) } returns savedEntity
            every { staffEntityMapper.toDomain(savedEntity) } returns chiefGroomerStaff

            // When
            val result = repository.save(chiefGroomerStaff)

            // Then
            assertEquals(chiefGroomerStaff, result)
            assertEquals(StaffRole.CHIEF_GROOMER, result.role)
            verify { staffEntityMapper.toEntity(chiefGroomerStaff) }
            verify { jpaRepository.save(staffEntity) }
            verify { staffEntityMapper.toDomain(savedEntity) }
        }
    }

    @Nested
    @DisplayName("Value Object Conversion Issues")
    inner class ValueObjectConversionIssues {
        @Test
        fun `should reproduce JPA instantiation error with empty value objects`() {
            // Given - This simulates the current problematic scenario
            val staff = TestDataBuilder.aStaff().build()

            // This entity has the problematic empty string IDs that cause JPA instantiation errors
            val problematicEntity = StaffEntity() // Uses default constructor with empty strings

            every { staffEntityMapper.toEntity(staff) } returns problematicEntity
            every { jpaRepository.save(problematicEntity) } throws
                JpaSystemException(
                    RuntimeException("Could not instantiate entity 'ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StaffEntity' due to: null"),
                )

            // When & Then
            val exception =
                assertThrows<JpaSystemException> {
                    repository.save(staff)
                }

            assertNotNull(exception.message)
            verify { staffEntityMapper.toEntity(staff) }
            verify { jpaRepository.save(problematicEntity) }
        }
    }
}
