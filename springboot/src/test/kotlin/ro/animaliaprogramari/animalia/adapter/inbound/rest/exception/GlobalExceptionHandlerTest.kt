package ro.animaliaprogramari.animalia.adapter.inbound.rest.exception

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.springframework.http.HttpStatus
import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.web.context.request.ServletWebRequest
import ro.animaliaprogramari.animalia.domain.exception.SchedulingConflictException
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.ZonedDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class GlobalExceptionHandlerTest {

    private val globalExceptionHandler = GlobalExceptionHandler()

    @Test
    fun `should handle SchedulingConflictException and return proper response`() {
        // Given
        val staffId = StaffId.generate()
        val conflictDetails = ConflictDetails(
            id = "appointment-123",
            startTime = ZonedDateTime.now(),
            endTime = ZonedDateTime.now().plusHours(1),
            description = "Existing appointment with client"
        )
        
        val conflict = BlockTimeConflict(
            staffId = staffId,
            staffName = "John Doe",
            conflictType = ConflictType.APPOINTMENT,
            conflictDetails = conflictDetails
        )
        
        val suggestedActions = listOf(
            SuggestedAction.RESCHEDULE_APPOINTMENTS,
            SuggestedAction.ADJUST_TIME_RANGE
        )
        
        val exception = SchedulingConflictException(
            message = "Interval indisponibil pentru personal",
            conflicts = listOf(conflict),
            suggestedActions = suggestedActions
        )
        
        val request = ServletWebRequest(MockHttpServletRequest())

        // When
        val response = globalExceptionHandler.handleSchedulingConflict(exception, request)

        // Then
        assertAll(
            { assertEquals(HttpStatus.CONFLICT, response.statusCode) },
            { assertNotNull(response.body) },
            { assertEquals(false, response.body!!.success) },
            { assertEquals("SCHEDULING_CONFLICT", response.body!!.errorCode) },
            { assertEquals("Interval indisponibil pentru personal", response.body!!.error) },
            { assertNotNull(response.body!!.details) }
        )

        // Verify details structure
        val details = response.body!!.details as Map<*, *>
        assertAll(
            { assertNotNull(details["conflicts"]) },
            { assertNotNull(details["suggestedActions"]) }
        )

        // Verify conflicts structure
        val conflicts = details["conflicts"] as List<*>
        assertEquals(1, conflicts.size)
        
        val conflictMap = conflicts[0] as Map<*, *>
        assertAll(
            { assertEquals(staffId.value, conflictMap["staffId"]) },
            { assertEquals("John Doe", conflictMap["staffName"]) },
            { assertEquals("APPOINTMENT", conflictMap["conflictType"]) },
            { assertNotNull(conflictMap["conflictDetails"]) }
        )

        // Verify suggested actions structure
        val actions = details["suggestedActions"] as List<*>
        assertEquals(2, actions.size)
        
        val firstAction = actions[0] as Map<*, *>
        assertAll(
            { assertEquals("RESCHEDULE_APPOINTMENTS", firstAction["type"]) },
            { assertEquals("Reschedule Appointments", firstAction["description"]) }
        )
    }

    @Test
    fun `should handle SchedulingConflictException with empty conflicts and actions`() {
        // Given
        val exception = SchedulingConflictException(
            message = "Interval indisponibil pentru personal",
            conflicts = emptyList(),
            suggestedActions = emptyList()
        )
        
        val request = ServletWebRequest(MockHttpServletRequest())

        // When
        val response = globalExceptionHandler.handleSchedulingConflict(exception, request)

        // Then
        assertAll(
            { assertEquals(HttpStatus.CONFLICT, response.statusCode) },
            { assertNotNull(response.body) },
            { assertEquals(false, response.body!!.success) },
            { assertEquals("SCHEDULING_CONFLICT", response.body!!.errorCode) },
            { assertEquals("Interval indisponibil pentru personal", response.body!!.error) }
        )

        // Verify details structure with empty lists
        val details = response.body!!.details as Map<*, *>
        val conflicts = details["conflicts"] as List<*>
        val actions = details["suggestedActions"] as List<*>
        
        assertAll(
            { assertEquals(0, conflicts.size) },
            { assertEquals(0, actions.size) }
        )
    }

    @Test
    fun `should handle SchedulingConflictException with null message`() {
        // Given
        val exception = SchedulingConflictException(
            message = "",
            conflicts = emptyList(),
            suggestedActions = emptyList()
        )
        
        val request = ServletWebRequest(MockHttpServletRequest())

        // When
        val response = globalExceptionHandler.handleSchedulingConflict(exception, request)

        // Then
        assertAll(
            { assertEquals(HttpStatus.CONFLICT, response.statusCode) },
            { assertNotNull(response.body) },
            { assertEquals("Interval indisponibil pentru personal", response.body!!.error) } // fallback message
        )
    }
}
