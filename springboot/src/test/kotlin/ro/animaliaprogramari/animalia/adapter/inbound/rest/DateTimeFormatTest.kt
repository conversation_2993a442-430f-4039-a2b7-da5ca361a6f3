package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.RescheduleAppointmentRequest
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/**
 * Test class to verify LocalDateTime format handling in DTOs
 */
@DisplayName("DateTime Format Tests")
class DateTimeFormatTest {
    private val objectMapper =
        ObjectMapper().apply {
            registerModule(JavaTimeModule())
            registerModule(KotlinModule.Builder().build())
        }

    @Nested
    @DisplayName("RescheduleAppointmentRequest DateTime Parsing")
    inner class RescheduleAppointmentRequestTests {
        @Test
        fun `should parse LocalDateTime with milliseconds`() {
            // Given - JSON with milliseconds (the problematic format)
            val json =
                """
                {
                    "startTime": "2025-06-11T10:45:00.000",
                    "endTime": "2025-06-11T11:45:00.000",
                    "reason": "Client requested time change"
                }
                """.trimIndent()

            // When
            val request = objectMapper.readValue(json, RescheduleAppointmentRequest::class.java)

            // Then
            assertNotNull(request)
            assertEquals(LocalDateTime.of(2025, 6, 11, 10, 45, 0, 0), request.startTime)
            assertEquals(LocalDateTime.of(2025, 6, 11, 11, 45, 0, 0), request.endTime)
            assertEquals("Client requested time change", request.reason)
        }

        @Test
        fun `should parse LocalDateTime without milliseconds`() {
            // Given - JSON without milliseconds (standard format)
            val json =
                """
                {
                    "startTime": "2025-06-11T10:45:00",
                    "endTime": "2025-06-11T11:45:00",
                    "reason": "Standard format"
                }
                """.trimIndent()

            // When
            val request = objectMapper.readValue(json, RescheduleAppointmentRequest::class.java)

            // Then
            assertNotNull(request)
            assertEquals(LocalDateTime.of(2025, 6, 11, 10, 45, 0), request.startTime)
            assertEquals(LocalDateTime.of(2025, 6, 11, 11, 45, 0), request.endTime)
            assertEquals("Standard format", request.reason)
        }

        @Test
        fun `should parse LocalDateTime with different millisecond values`() {
            // Given - JSON with various millisecond values
            val json =
                """
                {
                    "startTime": "2025-06-11T10:45:00.123",
                    "endTime": "2025-06-11T11:45:00.456",
                    "reason": "Different milliseconds"
                }
                """.trimIndent()

            // When
            val request = objectMapper.readValue(json, RescheduleAppointmentRequest::class.java)

            // Then
            assertNotNull(request)
            assertEquals(LocalDateTime.of(2025, 6, 11, 10, 45, 0, 123_000_000), request.startTime)
            assertEquals(LocalDateTime.of(2025, 6, 11, 11, 45, 0, 456_000_000), request.endTime)
            assertEquals("Different milliseconds", request.reason)
        }

        @Test
        fun `should serialize LocalDateTime correctly`() {
            // Given
            val request =
                RescheduleAppointmentRequest(
                    startTime = LocalDateTime.of(2025, 6, 11, 10, 45, 0),
                    endTime = LocalDateTime.of(2025, 6, 11, 11, 45, 0),
                    reason = "Test serialization",
                )

            // When
            val json = objectMapper.writeValueAsString(request)

            // Then
            assertNotNull(json)
            // Should contain the datetime in the expected format
            assert(json.contains("2025-06-11T10:45:00"))
            assert(json.contains("2025-06-11T11:45:00"))
            assert(json.contains("Test serialization"))
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    inner class EdgeCaseTests {
        @Test
        fun `should handle minimum required fields`() {
            // Given - JSON with only required fields
            val json =
                """
                {
                    "startTime": "2025-06-11T10:45:00.000",
                    "endTime": "2025-06-11T11:45:00.000"
                }
                """.trimIndent()

            // When
            val request = objectMapper.readValue(json, RescheduleAppointmentRequest::class.java)

            // Then
            assertNotNull(request)
            assertEquals(LocalDateTime.of(2025, 6, 11, 10, 45, 0, 0), request.startTime)
            assertEquals(LocalDateTime.of(2025, 6, 11, 11, 45, 0, 0), request.endTime)
            assertEquals(null, request.reason) // Optional field
        }

        @Test
        fun `should handle midnight times`() {
            // Given - JSON with midnight times
            val json =
                """
                {
                    "startTime": "2025-06-11T00:00:00.000",
                    "endTime": "2025-06-11T23:59:59.999"
                }
                """.trimIndent()

            // When
            val request = objectMapper.readValue(json, RescheduleAppointmentRequest::class.java)

            // Then
            assertNotNull(request)
            assertEquals(LocalDateTime.of(2025, 6, 11, 0, 0, 0, 0), request.startTime)
            assertEquals(LocalDateTime.of(2025, 6, 11, 23, 59, 59, 999_000_000), request.endTime)
        }
    }
}
