package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

/**
 * Unit tests for NotificationHistory entity
 * Tests entity creation, validation, and JPA mapping
 */
@DisplayName("NotificationHistory Entity Tests")
class NotificationHistoryTest {

    @Nested
    @DisplayName("Entity Creation")
    inner class EntityCreation {

        @Test
        fun `should create notification history with all fields`() {
            // Given
            val salonId = "salon-123"
            val title = "Appointment Scheduled"
            val message = "Your appointment has been scheduled for tomorrow"
            val type = "APPOINTMENT_SCHEDULED"
            val readStatus = false
            val timestamp = LocalDateTime.now()
            val appointmentId = "appointment-456"
            val clientId = "client-789"
            val metadata = """{"reminderType": "DAY_BEFORE"}"""

            // When
            val notification = NotificationHistory(
                salonId = salonId,
                title = title,
                message = message,
                type = type,
                readStatus = readStatus,
                timestamp = timestamp,
                appointmentId = appointmentId,
                clientId = clientId,
                metadata = metadata
            )

            // Then
            assertEquals(salonId, notification.salonId)
            assertEquals(title, notification.title)
            assertEquals(message, notification.message)
            assertEquals(type, notification.type)
            assertEquals(readStatus, notification.readStatus)
            assertEquals(timestamp, notification.timestamp)
            assertEquals(appointmentId, notification.appointmentId)
            assertEquals(clientId, notification.clientId)
            assertEquals(metadata, notification.metadata)
            assertNotNull(notification.createdAt)
            assertNotNull(notification.updatedAt)
        }

        @Test
        fun `should create notification history with minimal required fields`() {
            // Given
            val salonId = "salon-123"
            val title = "Test Notification"
            val message = "Test message"
            val type = "TEST"

            // When
            val notification = NotificationHistory(
                salonId = salonId,
                title = title,
                message = message,
                type = type
            )

            // Then
            assertEquals(salonId, notification.salonId)
            assertEquals(title, notification.title)
            assertEquals(message, notification.message)
            assertEquals(type, notification.type)
            assertFalse(notification.readStatus) // Default value
            assertNotNull(notification.timestamp)
            assertNull(notification.appointmentId)
            assertNull(notification.clientId)
            assertNull(notification.metadata)
        }

        @Test
        fun `should create notification history using default constructor`() {
            // When
            val notification = NotificationHistory()

            // Then
            assertEquals("", notification.salonId)
            assertEquals("", notification.title)
            assertEquals("", notification.message)
            assertEquals("", notification.type)
            assertFalse(notification.readStatus)
            assertNotNull(notification.timestamp)
            assertNull(notification.appointmentId)
            assertNull(notification.clientId)
            assertNull(notification.metadata)
            assertNotNull(notification.createdAt)
            assertNotNull(notification.updatedAt)
        }
    }

    @Nested
    @DisplayName("Field Validation")
    inner class FieldValidation {

        @Test
        fun `should handle long message content`() {
            // Given
            val longMessage = "A".repeat(5000) // Very long message
            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Long Message Test",
                message = longMessage,
                type = "TEST"
            )

            // Then
            assertEquals(longMessage, notification.message)
        }

        @Test
        fun `should handle special characters in message`() {
            // Given
            val specialMessage = "Programare pentru 🐾 Fluffy la 14:30! Vă așteptăm cu drag 😊"
            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Special Characters",
                message = specialMessage,
                type = "APPOINTMENT_SCHEDULED"
            )

            // Then
            assertEquals(specialMessage, notification.message)
        }

        @Test
        fun `should handle JSON metadata`() {
            // Given
            val jsonMetadata = """
                {
                    "appointmentDate": "2024-06-15",
                    "clientName": "John Doe",
                    "petName": "Fluffy",
                    "reminderType": "DAY_BEFORE",
                    "salonPhone": "+40731446895"
                }
            """.trimIndent()

            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Metadata Test",
                message = "Test message",
                type = "APPOINTMENT_REMINDER",
                metadata = jsonMetadata
            )

            // Then
            assertEquals(jsonMetadata, notification.metadata)
        }
    }

    @Nested
    @DisplayName("Notification Types")
    inner class NotificationTypes {

        @Test
        fun `should support appointment notification types`() {
            val appointmentTypes = listOf(
                "APPOINTMENT_SCHEDULED",
                "APPOINTMENT_CANCELLED",
                "APPOINTMENT_RESCHEDULED",
                "APPOINTMENT_REMINDER",
                "APPOINTMENT_DELETED"
            )

            appointmentTypes.forEach { type ->
                val notification = NotificationHistory(
                    salonId = "salon-123",
                    title = "Test $type",
                    message = "Test message for $type",
                    type = type
                )

                assertEquals(type, notification.type)
            }
        }

        @Test
        fun `should support system notification types`() {
            val systemTypes = listOf(
                "STAFF_INVITATION",
                "SALON_CREATED",
                "PAYMENT_REMINDER",
                "SYSTEM_MAINTENANCE"
            )

            systemTypes.forEach { type ->
                val notification = NotificationHistory(
                    salonId = "salon-123",
                    title = "Test $type",
                    message = "Test message for $type",
                    type = type
                )

                assertEquals(type, notification.type)
            }
        }
    }

    @Nested
    @DisplayName("Timestamp Handling")
    inner class TimestampHandling {

        @Test
        fun `should set current timestamp by default`() {
            // Given
            val beforeCreation = LocalDateTime.now().minusSeconds(1)

            // When
            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Timestamp Test",
                message = "Test message",
                type = "TEST"
            )

            // Then
            val afterCreation = LocalDateTime.now().plusSeconds(1)
            assertTrue(notification.timestamp.isAfter(beforeCreation))
            assertTrue(notification.timestamp.isBefore(afterCreation))
            assertTrue(notification.createdAt.isAfter(beforeCreation))
            assertTrue(notification.createdAt.isBefore(afterCreation))
            assertTrue(notification.updatedAt.isAfter(beforeCreation))
            assertTrue(notification.updatedAt.isBefore(afterCreation))
        }

        @Test
        fun `should allow custom timestamp`() {
            // Given
            val customTimestamp = LocalDateTime.of(2024, 6, 15, 14, 30, 0)

            // When
            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Custom Timestamp Test",
                message = "Test message",
                type = "TEST",
                timestamp = customTimestamp
            )

            // Then
            assertEquals(customTimestamp, notification.timestamp)
        }
    }

    @Nested
    @DisplayName("Association Fields")
    inner class AssociationFields {

        @Test
        fun `should handle appointment association`() {
            // Given
            val appointmentId = "appointment-123"
            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Appointment Notification",
                message = "Your appointment is confirmed",
                type = "APPOINTMENT_SCHEDULED",
                appointmentId = appointmentId
            )

            // Then
            assertEquals(appointmentId, notification.appointmentId)
            assertNull(notification.clientId)
        }

        @Test
        fun `should handle client association`() {
            // Given
            val clientId = "client-456"
            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Client Notification",
                message = "Welcome to our salon",
                type = "CLIENT_WELCOME",
                clientId = clientId
            )

            // Then
            assertEquals(clientId, notification.clientId)
            assertNull(notification.appointmentId)
        }

        @Test
        fun `should handle both appointment and client associations`() {
            // Given
            val appointmentId = "appointment-123"
            val clientId = "client-456"
            val notification = NotificationHistory(
                salonId = "salon-123",
                title = "Appointment for Client",
                message = "Your appointment is confirmed",
                type = "APPOINTMENT_SCHEDULED",
                appointmentId = appointmentId,
                clientId = clientId
            )

            // Then
            assertEquals(appointmentId, notification.appointmentId)
            assertEquals(clientId, notification.clientId)
        }
    }
}
