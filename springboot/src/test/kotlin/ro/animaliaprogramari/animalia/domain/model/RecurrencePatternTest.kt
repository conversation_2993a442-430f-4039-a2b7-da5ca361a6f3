package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.DayOfWeek
import java.time.ZonedDateTime

/**
 * Comprehensive unit tests for RecurrencePattern domain model
 * Tests validation, pattern generation, and business logic
 */
@DisplayName("RecurrencePattern Domain Model")
class RecurrencePatternTest {
    @Nested
    @DisplayName("Validation")
    inner class Validation {
        @Test
        fun `should create valid weekly recurrence pattern`() {
            // When
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.WEEKLY,
                    interval = 1,
                    daysOfWeek = setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY),
                )

            // Then
            assertEquals(RecurrenceType.WEEKLY, pattern.type)
            assertEquals(1, pattern.interval)
            assertEquals(setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY), pattern.daysOfWeek)
        }

        @Test
        fun `should create valid monthly recurrence pattern`() {
            // When
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.MONTHLY,
                    interval = 1,
                    daysOfWeek = null,
                    dayOfMonth = 15,
                )

            // Then
            assertEquals(RecurrenceType.MONTHLY, pattern.type)
            assertEquals(1, pattern.interval)
            assertEquals(15, pattern.dayOfMonth)
            assertNull(pattern.daysOfWeek)
        }

        @Test
        fun `should create valid daily recurrence pattern`() {
            // When
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.DAILY,
                    interval = 2,
                    daysOfWeek = null,
                    dayOfMonth = null,
                )

            // Then
            assertEquals(RecurrenceType.DAILY, pattern.type)
            assertEquals(2, pattern.interval)
            assertNull(pattern.daysOfWeek)
            assertNull(pattern.dayOfMonth)
        }

        @Test
        fun `should throw exception when interval is zero or negative`() {
            // When & Then
            assertThrows<IllegalArgumentException> {
                TestDataBuilder.aRecurrencePattern(interval = 0)
            }

            assertThrows<IllegalArgumentException> {
                TestDataBuilder.aRecurrencePattern(interval = -1)
            }
        }

        @Test
        fun `should throw exception when interval exceeds 365`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    TestDataBuilder.aRecurrencePattern(interval = 366)
                }
            assertEquals("Interval cannot exceed 365", exception.message)
        }

        @Test
        fun `should throw exception when weekly pattern has no days of week`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    TestDataBuilder.aRecurrencePattern(
                        type = RecurrenceType.WEEKLY,
                        daysOfWeek = null,
                    )
                }
            assertEquals("Days of week must be specified for weekly recurrence", exception.message)
        }

        @Test
        fun `should throw exception when weekly pattern has empty days of week`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    TestDataBuilder.aRecurrencePattern(
                        type = RecurrenceType.WEEKLY,
                        daysOfWeek = emptySet(),
                    )
                }
            assertEquals("Days of week must be specified for weekly recurrence", exception.message)
        }

        @Test
        fun `should throw exception when monthly pattern has no day of month`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    TestDataBuilder.aRecurrencePattern(
                        type = RecurrenceType.MONTHLY,
                        dayOfMonth = null,
                    )
                }
            assertEquals("Day of month must be specified for monthly recurrence", exception.message)
        }

        @Test
        fun `should throw exception when day of month is invalid`() {
            // When & Then
            assertThrows<IllegalArgumentException> {
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.MONTHLY,
                    dayOfMonth = 0,
                )
            }

            assertThrows<IllegalArgumentException> {
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.MONTHLY,
                    dayOfMonth = 32,
                )
            }
        }

        @Test
        fun `should throw exception when both end date and occurrences are specified`() {
            // When & Then
            val exception =
                assertThrows<IllegalArgumentException> {
                    TestDataBuilder.aRecurrencePattern(
                        endDate = ZonedDateTime.now().plusDays(30),
                        occurrences = 10,
                    )
                }
            assertEquals("Cannot specify both end date and number of occurrences", exception.message)
        }

        @Test
        fun `should throw exception when occurrences is invalid`() {
            // When & Then
            assertThrows<IllegalArgumentException> {
                TestDataBuilder.aRecurrencePattern(occurrences = 0)
            }

            assertThrows<IllegalArgumentException> {
                TestDataBuilder.aRecurrencePattern(occurrences = -1)
            }

            assertThrows<IllegalArgumentException> {
                TestDataBuilder.aRecurrencePattern(occurrences = 366)
            }
        }
    }

    @Nested
    @DisplayName("Pattern Generation")
    inner class PatternGeneration {
        @Test
        fun `should generate next daily occurrence`() {
            // Given
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.DAILY,
                    interval = 2,
                )
            val currentDate = ZonedDateTime.now()

            // When
            val nextOccurrence = pattern.getNextOccurrence(currentDate)

            // Then
            assertNotNull(nextOccurrence)
            assertEquals(currentDate.plusDays(2), nextOccurrence)
        }

        @Test
        fun `should generate next weekly occurrence same week`() {
            // Given
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.WEEKLY,
                    interval = 1,
                    daysOfWeek = setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY),
                )
            // Start on Monday
            val monday = ZonedDateTime.now().with(DayOfWeek.MONDAY)

            // When
            val nextOccurrence = pattern.getNextOccurrence(monday)

            // Then
            assertNotNull(nextOccurrence)
            assertEquals(DayOfWeek.WEDNESDAY, nextOccurrence!!.dayOfWeek)
        }

        @Test
        fun `should generate next weekly occurrence next week`() {
            // Given
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.WEEKLY,
                    interval = 1,
                    daysOfWeek = setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY),
                )
            // Start on Friday (last day of week, next occurrence should be Monday next week)
            val friday = ZonedDateTime.now().with(DayOfWeek.FRIDAY)

            // When
            val nextOccurrence = pattern.getNextOccurrence(friday)

            // Then
            assertNotNull(nextOccurrence)
            assertEquals(DayOfWeek.MONDAY, nextOccurrence!!.dayOfWeek)
            assertTrue(nextOccurrence.isAfter(friday.plusDays(1)))
        }

        @Test
        fun `should generate next monthly occurrence`() {
            // Given
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.MONTHLY,
                    interval = 1,
                    dayOfMonth = 15,
                )
            val currentDate = ZonedDateTime.now().withDayOfMonth(10)

            // When
            val nextOccurrence = pattern.getNextOccurrence(currentDate)

            // Then
            assertNotNull(nextOccurrence)
            assertEquals(15, nextOccurrence!!.dayOfMonth)
            assertEquals(currentDate.month.plus(1), nextOccurrence.month)
        }

        @Test
        fun `should return null when past end date`() {
            // Given
            val endDate = ZonedDateTime.now().plusDays(5)
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.DAILY,
                    interval = 1,
                    endDate = endDate,
                )
            val pastEndDate = endDate.plusDays(1)

            // When
            val nextOccurrence = pattern.getNextOccurrence(pastEndDate)

            // Then
            assertNull(nextOccurrence)
        }

        @Test
        fun `should generate occurrences within date range`() {
            // Given
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.DAILY,
                    interval = 1,
                    occurrences = 5,
                )
            val startDate = ZonedDateTime.now()
            val rangeStart = startDate
            val rangeEnd = startDate.plusDays(10)

            // When
            val occurrences = pattern.generateOccurrences(startDate, rangeStart, rangeEnd)

            // Then
            assertEquals(5, occurrences.size)
            occurrences.forEachIndexed { index, occurrence ->
                assertEquals(startDate.plusDays(index.toLong()), occurrence)
            }
        }

        @Test
        fun `should limit occurrences by end date`() {
            // Given
            val startDate = ZonedDateTime.now()
            val endDate = startDate.plusDays(3)
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.DAILY,
                    interval = 1,
                    endDate = endDate,
                )
            val rangeStart = startDate
            val rangeEnd = startDate.plusDays(10)

            // When
            val occurrences = pattern.generateOccurrences(startDate, rangeStart, rangeEnd)

            // Then
            // Should include start date and up to but not including end date
            assertTrue(occurrences.size >= 1) // At least the start date
            assertTrue(occurrences.all { it.isBefore(endDate) || it.isEqual(endDate) })
            assertTrue(occurrences.contains(startDate)) // Should include start date
        }

        @Test
        fun `should validate pattern for start date`() {
            // Given
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.WEEKLY,
                    daysOfWeek = setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY),
                )
            val mondayDate = ZonedDateTime.now().with(DayOfWeek.MONDAY)
            val tuesdayDate = ZonedDateTime.now().with(DayOfWeek.TUESDAY)

            // When & Then
            assertTrue(pattern.isValidForStartDate(mondayDate))
            assertFalse(pattern.isValidForStartDate(tuesdayDate))
        }

        @Test
        fun `should validate monthly pattern for start date`() {
            // Given
            val pattern =
                TestDataBuilder.aRecurrencePattern(
                    type = RecurrenceType.MONTHLY,
                    dayOfMonth = 15,
                )
            val date15th = ZonedDateTime.now().withDayOfMonth(15)
            val date10th = ZonedDateTime.now().withDayOfMonth(10)

            // When & Then
            assertTrue(pattern.isValidForStartDate(date15th))
            assertFalse(pattern.isValidForStartDate(date10th))
        }

        @Test
        fun `should always validate daily pattern for any start date`() {
            // Given
            val pattern = TestDataBuilder.aRecurrencePattern(type = RecurrenceType.DAILY)
            val anyDate = ZonedDateTime.now()

            // When & Then
            assertTrue(pattern.isValidForStartDate(anyDate))
        }
    }
}
