package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate

class StaffHolidayTest {
    private val salonId = SalonId.of("salon-123")

    @Test
    fun `should create staff holiday successfully`() {
        val holiday =
            StaffHoliday.create(
                salonId = salonId,
                name = "Anul Nou",
                date = LocalDate.of(2024, 1, 1),
                isWorkingDay = false,
                type = HolidayType.LEGAL,
            )

        assertEquals("Anul Nou", holiday.name)
        assertEquals(LocalDate.of(2024, 1, 1), holiday.date)
        assertFalse(holiday.isWorkingDay)
        assertEquals(HolidayType.LEGAL, holiday.type)
        assertEquals(salonId, holiday.salonId)
        assertNotNull(holiday.id)
    }

    @Test
    fun `should not allow blank holiday name`() {
        assertThrows<IllegalArgumentException> {
            StaffHoliday.create(
                salonId = salonId,
                name = "",
                date = LocalDate.of(2024, 1, 1),
            )
        }
    }

    @Test
    fun `should not allow holiday name longer than 255 characters`() {
        val longName = "a".repeat(256)

        assertThrows<IllegalArgumentException> {
            StaffHoliday.create(
                salonId = salonId,
                name = longName,
                date = LocalDate.of(2024, 1, 1),
            )
        }
    }

    @Test
    fun `should update working status`() {
        val holiday =
            StaffHoliday.create(
                salonId = salonId,
                name = "Anul Nou",
                date = LocalDate.of(2024, 1, 1),
                isWorkingDay = false,
            )

        val updatedHoliday = holiday.updateWorkingStatus(true)

        assertTrue(updatedHoliday.isWorkingDay)
        assertNotEquals(holiday.updatedAt, updatedHoliday.updatedAt)
    }

    @Test
    fun `should update holiday name`() {
        val holiday =
            StaffHoliday.create(
                salonId = salonId,
                name = "Old Name",
                date = LocalDate.of(2024, 1, 1),
            )

        val updatedHoliday = holiday.updateName("New Name")

        assertEquals("New Name", updatedHoliday.name)
        assertNotEquals(holiday.updatedAt, updatedHoliday.updatedAt)
    }

    @Test
    fun `should not allow updating to blank name`() {
        val holiday =
            StaffHoliday.create(
                salonId = salonId,
                name = "Valid Name",
                date = LocalDate.of(2024, 1, 1),
            )

        assertThrows<IllegalArgumentException> {
            holiday.updateName("")
        }
    }

    @Test
    fun `should generate unique holiday IDs`() {
        val holiday1 = StaffHoliday.create(salonId, "Holiday 1", LocalDate.of(2024, 1, 1))
        val holiday2 = StaffHoliday.create(salonId, "Holiday 2", LocalDate.of(2024, 1, 2))

        assertNotEquals(holiday1.id, holiday2.id)
    }
}

class StaffCustomClosureTest {
    private val salonId = SalonId.of("salon-123")

    @Test
    fun `should create staff custom closure successfully`() {
        val closure =
            StaffCustomClosure.create(
                salonId = salonId,
                reason = "Concediu medical",
                date = LocalDate.of(2024, 6, 15),
                description = "Recuperare după intervenție",
            )

        assertEquals("Concediu medical", closure.reason)
        assertEquals(LocalDate.of(2024, 6, 15), closure.date)
        assertEquals("Recuperare după intervenție", closure.description)
        assertEquals(salonId, closure.salonId)
        assertNotNull(closure.id)
    }

    @Test
    fun `should create closure without description`() {
        val closure =
            StaffCustomClosure.create(
                salonId = salonId,
                reason = "Concediu",
                date = LocalDate.of(2024, 6, 15),
            )

        assertEquals("Concediu", closure.reason)
        assertNull(closure.description)
    }

    @Test
    fun `should not allow blank reason`() {
        assertThrows<IllegalArgumentException> {
            StaffCustomClosure.create(
                salonId = salonId,
                reason = "",
                date = LocalDate.of(2024, 6, 15),
            )
        }
    }

    @Test
    fun `should not allow reason longer than 255 characters`() {
        val longReason = "a".repeat(256)

        assertThrows<IllegalArgumentException> {
            StaffCustomClosure.create(
                salonId = salonId,
                reason = longReason,
                date = LocalDate.of(2024, 6, 15),
            )
        }
    }

    @Test
    fun `should not allow description longer than 1000 characters`() {
        val longDescription = "a".repeat(1001)

        assertThrows<IllegalArgumentException> {
            StaffCustomClosure.create(
                salonId = salonId,
                reason = "Valid reason",
                date = LocalDate.of(2024, 6, 15),
                description = longDescription,
            )
        }
    }

    @Test
    fun `should update reason`() {
        val closure =
            StaffCustomClosure.create(
                salonId = salonId,
                reason = "Old reason",
                date = LocalDate.of(2024, 6, 15),
            )

        val updatedClosure = closure.updateReason("New reason")

        assertEquals("New reason", updatedClosure.reason)
        assertNotEquals(closure.updatedAt, updatedClosure.updatedAt)
    }

    @Test
    fun `should update description`() {
        val closure =
            StaffCustomClosure.create(
                salonId = salonId,
                reason = "Concediu",
                date = LocalDate.of(2024, 6, 15),
                description = "Old description",
            )

        val updatedClosure = closure.updateDescription("New description")

        assertEquals("New description", updatedClosure.description)
        assertNotEquals(closure.updatedAt, updatedClosure.updatedAt)
    }

    @Test
    fun `should update date`() {
        val closure =
            StaffCustomClosure.create(
                salonId = salonId,
                reason = "Concediu",
                date = LocalDate.of(2024, 6, 15),
            )

        val newDate = LocalDate.of(2024, 6, 20)
        val updatedClosure = closure.updateDate(newDate)

        assertEquals(newDate, updatedClosure.date)
        assertNotEquals(closure.updatedAt, updatedClosure.updatedAt)
    }

    @Test
    fun `should generate unique closure IDs`() {
        val closure1 = StaffCustomClosure.create(salonId, "Reason 1", LocalDate.of(2024, 6, 15))
        val closure2 = StaffCustomClosure.create(salonId, "Reason 2", LocalDate.of(2024, 6, 16))

        assertNotEquals(closure1.id, closure2.id)
    }
}
