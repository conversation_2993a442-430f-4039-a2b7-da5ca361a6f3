package ro.animaliaprogramari.animalia.domain.model

import kotlin.test.Test
import kotlin.test.assertEquals

class DurationTest {
    @Test
    fun `should add and subtract durations correctly`() {
        val d1 = Duration.ofHours(1)
        val d2 = Duration.ofMinutes(30)
        assertEquals(90, d1.add(d2).minutes)
        assertEquals(30, d1.subtract(d2).minutes)
    }

    @Test
    fun `should convert to hours and minutes`() {
        val d = Duration.ofMinutes(125)
        val (h, m) = d.toHoursAndMinutes()
        assertEquals(2, h)
        assertEquals(5, m)
        assertEquals(125 / 60.0, d.toHours())
    }
}
