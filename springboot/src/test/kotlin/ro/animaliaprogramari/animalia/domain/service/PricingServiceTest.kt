package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal
import kotlin.test.Test
import kotlin.test.assertEquals

class PricingServiceTest {
    private val service = PricingService()

    @Test
    fun `calculateAppointmentPrice should apply weight multiplier and discounts`() {
        val service1 =
            SalonService(
                ServiceId.of("1"),
                SalonId.of("salon-1"),
                "Bath",
                null,
                Money.of(50.0),
                Duration.ofMinutes(30),
                ServiceCategory.GROOMING,
            )
        val pet =
            Pet(
                id = PetId.of("p1"),
                clientId = ClientId.of("c1"),
                name = "<PERSON>",
                breed = null,
                age = null,
                weight = BigDecimal("40"),
                color = null,
                gender = null,
                notes = null,
                medicalConditions = null,
                size = "L",
                species = "Dog",
                photoUrl = null,
            )
        val client = Client("<PERSON>", PhoneNumber.of("+40123456789"))
        val discount = Discount(DiscountType.FIXED_AMOUNT, BigDecimal("10"), "Promo")

        val price = service.calculateAppointmentPrice(listOf(service1), pet, client, listOf(discount))
        assertEquals(Money.of(BigDecimal("50.0")).multiply(BigDecimal("1.5")).subtract(Money.of(10.0)), price)
    }
}
