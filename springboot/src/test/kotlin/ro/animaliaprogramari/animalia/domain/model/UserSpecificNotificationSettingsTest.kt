package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalTime

/**
 * Unit tests for user-specific notification settings domain model
 */
@DisplayName("User-Specific Notification Settings Domain Model Tests")
class UserSpecificNotificationSettingsTest {

    private val testUserId = UserId.generate()
    private val testSalonId = SalonId.generate()

    @Nested
    @DisplayName("Creation and Initialization")
    inner class CreationTests {

        @Test
        fun `should create notification settings with userId and salonId`() {
            // When
            val settings = NotificationSettings.createDefault(testUserId, testSalonId)

            // Then
            assertEquals(testUserId, settings.userId)
            assertEquals(testSalonId, settings.salonId)
            assertTrue(settings.pushNotificationsEnabled)
            assertEquals(SoundPreference.DEFAULT, settings.soundPreference)
            assertTrue(settings.vibrationEnabled)
            assertFalse(settings.doNotDisturb.enabled)
            assertTrue(settings.notificationRules.newAppointments)
        }

        @Test
        fun `should create notification settings with custom parameters`() {
            // Given
            val doNotDisturb = DoNotDisturbSettings(
                enabled = true,
                startTime = LocalTime.of(22, 0),
                endTime = LocalTime.of(8, 0),
                allowCritical = false
            )
            val notificationRules = NotificationRules(
                newAppointments = false,
                appointmentCancellations = true,
                paymentConfirmations = false,
                teamMemberUpdates = true,
                systemMaintenanceAlerts = false
            )

            // When
            val settings = NotificationSettings(
                userId = testUserId,
                salonId = testSalonId,
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.SILENT,
                vibrationEnabled = false,
                doNotDisturb = doNotDisturb,
                notificationRules = notificationRules
            )

            // Then
            assertEquals(testUserId, settings.userId)
            assertEquals(testSalonId, settings.salonId)
            assertFalse(settings.pushNotificationsEnabled)
            assertEquals(SoundPreference.SILENT, settings.soundPreference)
            assertFalse(settings.vibrationEnabled)
            assertTrue(settings.doNotDisturb.enabled)
            assertFalse(settings.notificationRules.newAppointments)
            assertTrue(settings.notificationRules.appointmentCancellations)
        }

        @Test
        fun `should require different userId and salonId combinations for uniqueness`() {
            // Given
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()
            val salonId1 = SalonId.generate()
            val salonId2 = SalonId.generate()

            // When
            val settings1 = NotificationSettings.createDefault(userId1, salonId1)
            val settings2 = NotificationSettings.createDefault(userId1, salonId2)
            val settings3 = NotificationSettings.createDefault(userId2, salonId1)
            val settings4 = NotificationSettings.createDefault(userId2, salonId2)

            // Then - Each combination should be unique
            assertNotEquals(settings1, settings2)
            assertNotEquals(settings1, settings3)
            assertNotEquals(settings1, settings4)
            assertNotEquals(settings2, settings3)
            assertNotEquals(settings2, settings4)
            assertNotEquals(settings3, settings4)
        }
    }

    @Nested
    @DisplayName("Notification Logic")
    inner class NotificationLogicTests {

        @Test
        fun `should allow notifications when push notifications enabled and rules allow`() {
            // Given
            val settings = NotificationSettings.createDefault(testUserId, testSalonId)

            // When & Then
            assertTrue(settings.shouldSendNotification(NotificationType.NEW_APPOINTMENT))
            assertTrue(settings.shouldSendNotification(NotificationType.APPOINTMENT_CANCELLATION))
            assertTrue(settings.shouldSendNotification(NotificationType.PAYMENT_CONFIRMATION))
        }

        @Test
        fun `should block notifications when push notifications disabled`() {
            // Given
            val settings = NotificationSettings.createDefault(testUserId, testSalonId)
                .updateSettings(pushNotificationsEnabled = false)

            // When & Then
            assertFalse(settings.shouldSendNotification(NotificationType.NEW_APPOINTMENT))
            assertFalse(settings.shouldSendNotification(NotificationType.APPOINTMENT_CANCELLATION))
            assertFalse(settings.shouldSendNotification(NotificationType.PAYMENT_CONFIRMATION))
        }

        @Test
        fun `should respect do not disturb settings`() {
            // Given
            val dndSettings = DoNotDisturbSettings(
                enabled = true,
                startTime = LocalTime.of(22, 0),
                endTime = LocalTime.of(8, 0),
                allowCritical = false
            )
            val settings = NotificationSettings.createDefault(testUserId, testSalonId)
                .updateSettings(doNotDisturb = dndSettings)

            // When & Then - During DND hours
            assertFalse(settings.shouldSendNotification(
                NotificationType.NEW_APPOINTMENT,
                NotificationPriority.NORMAL,
                LocalTime.of(23, 0)
            ))
            
            // When & Then - Outside DND hours
            assertTrue(settings.shouldSendNotification(
                NotificationType.NEW_APPOINTMENT,
                NotificationPriority.NORMAL,
                LocalTime.of(10, 0)
            ))
        }

        @Test
        fun `should allow critical notifications during do not disturb when configured`() {
            // Given
            val dndSettings = DoNotDisturbSettings(
                enabled = true,
                startTime = LocalTime.of(22, 0),
                endTime = LocalTime.of(8, 0),
                allowCritical = true
            )
            val settings = NotificationSettings.createDefault(testUserId, testSalonId)
                .updateSettings(doNotDisturb = dndSettings)

            // When & Then - Critical notification during DND
            assertTrue(settings.shouldSendNotification(
                NotificationType.NEW_APPOINTMENT,
                NotificationPriority.CRITICAL,
                LocalTime.of(23, 0)
            ))
            
            // When & Then - Normal notification during DND
            assertFalse(settings.shouldSendNotification(
                NotificationType.NEW_APPOINTMENT,
                NotificationPriority.NORMAL,
                LocalTime.of(23, 0)
            ))
        }
    }

    @Nested
    @DisplayName("Settings Updates")
    inner class SettingsUpdateTests {

        @Test
        fun `should update settings and preserve userId and salonId`() {
            // Given
            val originalSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val newDoNotDisturb = DoNotDisturbSettings(enabled = true)
            val newNotificationRules = NotificationRules(newAppointments = false)

            // When
            val updatedSettings = originalSettings.updateSettings(
                pushNotificationsEnabled = false,
                soundPreference = SoundPreference.CUSTOM1,
                vibrationEnabled = false,
                doNotDisturb = newDoNotDisturb,
                notificationRules = newNotificationRules
            )

            // Then
            assertEquals(testUserId, updatedSettings.userId)
            assertEquals(testSalonId, updatedSettings.salonId)
            assertFalse(updatedSettings.pushNotificationsEnabled)
            assertEquals(SoundPreference.CUSTOM1, updatedSettings.soundPreference)
            assertFalse(updatedSettings.vibrationEnabled)
            assertTrue(updatedSettings.doNotDisturb.enabled)
            assertFalse(updatedSettings.notificationRules.newAppointments)
        }

        @Test
        fun `should update timestamp when settings are modified`() {
            // Given
            val originalSettings = NotificationSettings.createDefault(testUserId, testSalonId)
            val originalTimestamp = originalSettings.updatedAt

            // Wait a bit to ensure timestamp difference
            Thread.sleep(10)

            // When
            val updatedSettings = originalSettings.updateSettings(
                pushNotificationsEnabled = false
            )

            // Then
            assertTrue(updatedSettings.updatedAt.isAfter(originalTimestamp))
        }
    }

    @Nested
    @DisplayName("User-Specific Behavior")
    inner class UserSpecificBehaviorTests {

        @Test
        fun `should allow different settings for same user in different salons`() {
            // Given
            val userId = UserId.generate()
            val salon1 = SalonId.generate()
            val salon2 = SalonId.generate()

            // When
            val settings1 = NotificationSettings.createDefault(userId, salon1)
                .updateSettings(pushNotificationsEnabled = true, soundPreference = SoundPreference.DEFAULT)
            
            val settings2 = NotificationSettings.createDefault(userId, salon2)
                .updateSettings(pushNotificationsEnabled = false, soundPreference = SoundPreference.SILENT)

            // Then
            assertEquals(userId, settings1.userId)
            assertEquals(userId, settings2.userId)
            assertEquals(salon1, settings1.salonId)
            assertEquals(salon2, settings2.salonId)
            assertTrue(settings1.pushNotificationsEnabled)
            assertFalse(settings2.pushNotificationsEnabled)
            assertEquals(SoundPreference.DEFAULT, settings1.soundPreference)
            assertEquals(SoundPreference.SILENT, settings2.soundPreference)
        }

        @Test
        fun `should allow different settings for different users in same salon`() {
            // Given
            val user1 = UserId.generate()
            val user2 = UserId.generate()
            val salonId = SalonId.generate()

            // When
            val settings1 = NotificationSettings.createDefault(user1, salonId)
                .updateSettings(vibrationEnabled = true)
            
            val settings2 = NotificationSettings.createDefault(user2, salonId)
                .updateSettings(vibrationEnabled = false)

            // Then
            assertEquals(user1, settings1.userId)
            assertEquals(user2, settings2.userId)
            assertEquals(salonId, settings1.salonId)
            assertEquals(salonId, settings2.salonId)
            assertTrue(settings1.vibrationEnabled)
            assertFalse(settings2.vibrationEnabled)
        }
    }
}
