import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

class PhoneNumberTest {
    @Test
    fun `toInternationalFormat converts local`() {
        val pn = PhoneNumber.of("0712345678")
        assertEquals("+40712345678", pn.toInternationalFormat())
    }

    @Test
    fun `ofNullable returns null for blank`() {
        assertNull(PhoneNumber.ofNullable(""))
        assertNull(PhoneNumber.ofNullable(null))
    }

    @Test
    fun `romanian number detection`() {
        val pn = PhoneNumber.of("+40712345678")
        assertTrue(pn.isRomanianNumber())
        assertTrue(PhoneNumber.isValidRomanianFormat("+40712345678"))
    }
}
