package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime

/**
 * Unit tests for staff working hours inheritance from business hours
 */
class StaffWorkingHoursInheritanceTest {
    private val salonId = SalonId.generate()
    private val staffId = StaffId.generate()

    @Test
    fun `should inherit business hours when inheritance is enabled`() {
        // Given: Business hours 9-17 Monday-Friday
        val businessHours =
            createBusinessHours(
                mondayToFriday = DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                saturday = DaySchedule.dayOff(),
                sunday = DaySchedule.dayOff(),
            )

        // And: Staff with inheritance enabled
        val staffHours = createStaffHours(inheritFromBusiness = true)

        // When: Getting effective working hours
        val effectiveMonday =
            staffHours.getEffectiveWorkingHoursFor(
                LocalDate.of(2024, 1, 1), // Monday
                businessHours,
            )

        // Then: Staff inherits business hours
        assertNotNull(effectiveMonday)
        assertEquals(LocalTime.of(9, 0), effectiveMonday!!.startTime)
        assertEquals(LocalTime.of(17, 0), effectiveMonday.endTime)
        assertTrue(effectiveMonday.isWorkingDay)
    }

    @Test
    fun `should not work when business is closed even if staff schedule allows it`() {
        // Given: Business closed on Monday
        val businessHours =
            createBusinessHours(
                monday = DaySchedule.dayOff(),
                tuesdayToFriday = DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
            )

        // And: Staff wants to work on Monday
        val staffSchedule =
            mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(16, 0)),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.SATURDAY to DaySchedule.dayOff(),
                DayOfWeek.SUNDAY to DaySchedule.dayOff(),
            )

        val staffHours =
            StaffWorkingHoursSettings(
                staffId = staffId,
                salonId = salonId,
                weeklySchedule = staffSchedule,
                holidays = emptyList(),
                customClosures = emptyList(),
                inheritFromBusiness = true,
            )

        // When: Checking availability on Monday
        val isAvailable =
            staffHours.isAvailableOnWithBusinessHours(
                LocalDate.of(2024, 1, 1), // Monday
                businessHours,
            )

        // Then: Staff is not available because business is closed
        assertFalse(isAvailable)
    }

    @Test
    fun `should merge schedules taking most restrictive constraints`() {
        // Given: Business hours 8-18
        val businessHours =
            createBusinessHours(
                mondayToFriday = DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(18, 0)),
            )

        // And: Staff prefers 9-17 with lunch break
        val staffSchedule =
            mapOf(
                DayOfWeek.MONDAY to
                    DaySchedule.workingDay(
                        LocalTime.of(9, 0), LocalTime.of(17, 0),
                        LocalTime.of(12, 0), LocalTime.of(13, 0),
                    ),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.SATURDAY to DaySchedule.dayOff(),
                DayOfWeek.SUNDAY to DaySchedule.dayOff(),
            )

        val staffHours =
            StaffWorkingHoursSettings(
                staffId = staffId,
                salonId = salonId,
                weeklySchedule = staffSchedule,
                holidays = emptyList(),
                customClosures = emptyList(),
                inheritFromBusiness = true,
            )

        // When: Getting effective working hours
        val effectiveMonday =
            staffHours.getEffectiveWorkingHoursFor(
                LocalDate.of(2024, 1, 1), // Monday
                businessHours,
            )

        // Then: Should take staff's more restrictive hours (9-17) and keep lunch break
        assertNotNull(effectiveMonday)
        assertEquals(LocalTime.of(9, 0), effectiveMonday!!.startTime) // Staff's later start
        assertEquals(LocalTime.of(17, 0), effectiveMonday.endTime) // Staff's earlier end
        assertEquals(LocalTime.of(12, 0), effectiveMonday.breakStart) // Staff's break preserved
        assertEquals(LocalTime.of(13, 0), effectiveMonday.breakEnd)
    }

    @Test
    fun `should validate staff schedule against business hours`() {
        // Given: Business hours 9-17
        val businessHours =
            createBusinessHours(
                mondayToFriday = DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
            )

        // And: Staff trying to work outside business hours
        val invalidStaffSchedule =
            mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(8, 0), LocalTime.of(18, 0)), // Outside business hours
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.SATURDAY to DaySchedule.workingDay(LocalTime.of(10, 0), LocalTime.of(15, 0)), // Business closed
                DayOfWeek.SUNDAY to DaySchedule.dayOff(),
            )

        val staffHours =
            StaffWorkingHoursSettings(
                staffId = staffId,
                salonId = salonId,
                weeklySchedule = invalidStaffSchedule,
                holidays = emptyList(),
                customClosures = emptyList(),
                inheritFromBusiness = true,
            )

        // When: Validating against business hours
        val violations = staffHours.validateAgainstBusinessHours(businessHours)

        // Then: Should have violations
        assertTrue(violations.isNotEmpty())
        assertTrue(violations.any { it.contains("before business start time") })
        assertTrue(violations.any { it.contains("after business end time") })
        assertTrue(violations.any { it.contains("when business is closed") })
    }

    @Test
    fun `should create staff schedule from business hours`() {
        // Given: Business hours
        val businessHours =
            createBusinessHours(
                mondayToFriday = DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
            )

        // When: Creating staff schedule from business hours
        val staffHours = StaffWorkingHoursSettings.createFromBusinessHours(staffId, salonId, businessHours)

        // Then: Should inherit business schedule
        assertEquals(businessHours.weeklySchedule, staffHours.weeklySchedule)
        assertTrue(staffHours.inheritFromBusiness)
    }

    // Helper methods
    private fun createBusinessHours(
        monday: DaySchedule = DaySchedule.dayOff(),
        tuesday: DaySchedule = DaySchedule.dayOff(),
        wednesday: DaySchedule = DaySchedule.dayOff(),
        thursday: DaySchedule = DaySchedule.dayOff(),
        friday: DaySchedule = DaySchedule.dayOff(),
        saturday: DaySchedule = DaySchedule.dayOff(),
        sunday: DaySchedule = DaySchedule.dayOff(),
        mondayToFriday: DaySchedule? = null,
        tuesdayToFriday: DaySchedule? = null,
    ): WorkingHoursSettings {
        val schedule = mutableMapOf<DayOfWeek, DaySchedule>()

        if (mondayToFriday != null) {
            schedule[DayOfWeek.MONDAY] = mondayToFriday
            schedule[DayOfWeek.TUESDAY] = mondayToFriday
            schedule[DayOfWeek.WEDNESDAY] = mondayToFriday
            schedule[DayOfWeek.THURSDAY] = mondayToFriday
            schedule[DayOfWeek.FRIDAY] = mondayToFriday
        } else {
            schedule[DayOfWeek.MONDAY] = monday
            schedule[DayOfWeek.TUESDAY] = tuesday
            schedule[DayOfWeek.WEDNESDAY] = wednesday
            schedule[DayOfWeek.THURSDAY] = thursday
            schedule[DayOfWeek.FRIDAY] = friday
        }

        if (tuesdayToFriday != null) {
            schedule[DayOfWeek.TUESDAY] = tuesdayToFriday
            schedule[DayOfWeek.WEDNESDAY] = tuesdayToFriday
            schedule[DayOfWeek.THURSDAY] = tuesdayToFriday
            schedule[DayOfWeek.FRIDAY] = tuesdayToFriday
        }

        schedule[DayOfWeek.SATURDAY] = saturday
        schedule[DayOfWeek.SUNDAY] = sunday

        return WorkingHoursSettings(
            salonId = salonId,
            weeklySchedule = schedule,
            holidays = emptyList(),
            customClosures = emptyList(),
        )
    }

    private fun createStaffHours(inheritFromBusiness: Boolean = true): StaffWorkingHoursSettings {
        return StaffWorkingHoursSettings.createDefault(staffId, salonId)
            .updateInheritance(inheritFromBusiness)
    }
}
