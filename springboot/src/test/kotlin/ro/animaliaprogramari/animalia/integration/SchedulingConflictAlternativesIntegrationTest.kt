package ro.animaliaprogramari.animalia.integration

import org.junit.jupiter.api.*
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.transaction.support.TransactionTemplate
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringClientRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.ClientEntityMapper
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.exception.AppointmentSchedulingConflictException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.test.TestCommandBuilder
import ro.animaliaprogramari.animalia.testutil.TestDataBuilder
import java.time.*
import kotlin.test.*

/**
 * Integration tests for appointment scheduling conflict detection and alternative suggestions.
 * Tests the complete workflow from conflict detection to alternative generation.
 *
 * Each test uses unique dates and times to ensure proper isolation.
 */
@SpringBootTest
@ActiveProfiles("test-postgres")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
@DisplayName("Scheduling Conflict Alternatives Integration Tests")
@Disabled("Requires database setup not available in CI")
class SchedulingConflictAlternativesIntegrationTest() {
    private val logger = LoggerFactory.getLogger(SchedulingConflictAlternativesIntegrationTest::class.java)

    @Autowired
    private lateinit var appointmentManagementUseCase: AppointmentManagementUseCase

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var salonRepository: SalonRepository

    @Autowired
    private lateinit var clientRepository: ClientRepository

    @Autowired
    private lateinit var petRepository: PetRepository

    @Autowired
    private lateinit var staffRepository: StaffRepository

    @Autowired
    private lateinit var salonServiceRepository: SalonServiceRepository

    @Autowired
    private lateinit var workingHoursRepository: WorkingHoursRepository

    @Autowired
    private lateinit var staffWorkingHoursRepository: StaffWorkingHoursRepository

    @Autowired
    private lateinit var appointmentRepository: AppointmentRepository

    @Autowired
    private lateinit var blockTimeRepository: BlockTimeRepository

    @Autowired
    private lateinit var userRepository: UserRepository

    @Autowired
    private lateinit var clientEntityMapper: ClientEntityMapper

    @Autowired
    private lateinit var springClientRepository: SpringClientRepository

    // Test data IDs
    private var salonId: SalonId? = null
    private var clientId: ClientId? = null
    private var petId: PetId? = null
    private var userId1: UserId? = null
    private var userId2: UserId? = null
    private var staffId1: StaffId? = null
    private var staffId2: StaffId? = null
    private var serviceId: ServiceId? = null

    @BeforeEach
    fun setUp() {
        logger.info("=== Setting up test data ===")
        cleanupTestData() // Clean any residual data first
        cleanAllResidualData() // More aggressive cleanup
        setupTestData()
        verifyCleanState()
        logger.info("=== Test data setup complete ===")
    }

    @AfterEach
    fun tearDown() {
        logger.info("=== Cleaning up test data ===")
        cleanupTestData()
        logger.info("=== Test data cleanup complete ===")
    }

    private fun setupTestData() {
        // Create salon
        val salon =
            TestDataBuilder.aSalon(
                name = "Test Conflict Salon",
            )
        salonId = salon.id
        salonRepository.save(salon)

        // Create users first (required for staff foreign key)
        val user1 =
            TestDataBuilder.aUser(
                email = Email.of("<EMAIL>"),
                firebaseUid = "firebase-uid-1",
            )
        userId1 = user1.id
        userRepository.save(user1)

        val user2 =
            TestDataBuilder.aUser(
                email = Email.of("<EMAIL>"),
                firebaseUid = "firebase-uid-2",
            )
        userId2 = user2.id
        userRepository.save(user2)

        // Create client and associate with salon
        val client =
            TestDataBuilder.aClient(
                name = "Test Conflict Client",
                phone = PhoneNumber.of("+40123456789"),
            )
        clientId = client.id

        // Save client with salon association using the entity mapper
        val clientEntity = clientEntityMapper.toEntity(client)
        springClientRepository.save(clientEntity)


        // Create pet
        val pet =
            TestDataBuilder.aPet(
                clientId = clientId!!,
                name = "Buddy",
                species = "Dog",
                breed = "Golden Retriever",
            )
        petId = pet.id
        petRepository.save(pet)

        // Create staff members
        val staff1 =
            TestDataBuilder.aStaff(
                userId = userId1!!,
                salonId = salonId!!,
                nickname = "Staff One",
            )
        staffId1 = staff1.id
        staffRepository.save(staff1)

        val staff2 =
            TestDataBuilder.aStaff(
                userId = userId2!!,
                salonId = salonId!!,
                nickname = "Staff Two",
            )
        staffId2 = staff2.id
        staffRepository.save(staff2)

        // Create service
        val service =
            TestDataBuilder.aSalonService(
                salonId = salonId!!,
                name = "Test Grooming",
                duration = ro.animaliaprogramari.animalia.domain.model.Duration.ofMinutes(60),
                basePrice = Money.of(50.0),
            )
        serviceId = service.id
        salonServiceRepository.save(service)

        // Setup working hours (Monday-Friday 9:00-18:00, lunch break 13:00-14:00)
        val daySchedule =
            DaySchedule.workingDay(
                startTime = LocalTime.of(9, 0),
                endTime = LocalTime.of(18, 0),
                breakStart = LocalTime.of(13, 0),
                breakEnd = LocalTime.of(14, 0),
            )

        val weeklySchedule =
            mapOf(
                DayOfWeek.MONDAY to daySchedule,
                DayOfWeek.TUESDAY to daySchedule,
                DayOfWeek.WEDNESDAY to daySchedule,
                DayOfWeek.THURSDAY to daySchedule,
                DayOfWeek.FRIDAY to daySchedule,
                DayOfWeek.SATURDAY to DaySchedule.dayOff(),
                DayOfWeek.SUNDAY to DaySchedule.dayOff(),
            )

        val workingHours =
            WorkingHoursSettings(
                salonId = salonId!!,
                weeklySchedule = weeklySchedule,
                holidays = emptyList(),
                customClosures = emptyList(),
            )
        workingHoursRepository.save(workingHours)

        // Setup staff working hours (same as salon)
        listOf(staffId1!!, staffId2!!).forEach { staffId ->
            val staffWorkingHours =
                StaffWorkingHoursSettings(
                    staffId = staffId,
                    salonId = salonId!!,
                    weeklySchedule = weeklySchedule,
                    holidays = emptyList(),
                    customClosures = emptyList(),
                )
            staffWorkingHoursRepository.save(staffWorkingHours)
        }
    }

    private fun cleanupTestData() {
        try {
            transactionTemplate.execute {
                // Clean up in reverse order of dependencies
                salonId?.let { sId ->
                    logger.debug("Cleaning appointments for salon: ${sId.value}")
                    appointmentRepository.findBySalonIdWithFilters(sId).forEach { appointment ->
                        logger.debug("Deleting appointment: ${appointment.id.value}")
                        appointmentRepository.deleteById(appointment.id)
                    }

                    logger.debug("Cleaning block times for salon: ${sId.value}")
                    blockTimeRepository.findBySalonId(sId).forEach { blockTime ->
                        blockTimeRepository.delete(blockTime.id)
                    }

                    logger.debug("Cleaning staff working hours for salon: ${sId.value}")
                    staffWorkingHoursRepository.findBySalonId(sId).forEach { staffHours ->
                        staffWorkingHoursRepository.deleteByStaffIdAndSalonId(staffHours.staffId, staffHours.salonId)
                    }

                    logger.debug("Cleaning working hours for salon: ${sId.value}")
                    workingHoursRepository.findBySalonId(sId)?.let { workingHours ->
                        workingHoursRepository.deleteBySalonId(workingHours.salonId)
                    }

                    logger.debug("Cleaning services for salon: ${sId.value}")
                    salonServiceRepository.findBySalonId(sId).forEach { service ->
                        salonServiceRepository.deleteById(service.id)
                    }

                    logger.debug("Cleaning staff for salon: ${sId.value}")
                    staffRepository.findBySalonId(sId).forEach { staff ->
                        staffRepository.delete(staff)
                    }
                }

                clientId?.let { cId ->
                    logger.debug("Cleaning pets for client: ${cId.value}")
                    petRepository.findByClientId(cId).forEach { pet ->
                        petRepository.deleteById(pet.id)
                    }

                    logger.debug("Cleaning client: ${cId.value}")
                    clientRepository.findById(cId)?.let { client ->
                        clientRepository.deleteById(client.id)
                    }
                }

                userId1?.let { uId ->
                    logger.debug("Cleaning user1: ${uId.value}")
                    userRepository.findById(uId)?.let { user ->
                        userRepository.deleteById(user.id)
                    }
                }

                userId2?.let { uId ->
                    logger.debug("Cleaning user2: ${uId.value}")
                    userRepository.findById(uId)?.let { user ->
                        userRepository.deleteById(user.id)
                    }
                }

                salonId?.let { sId ->
                    logger.debug("Cleaning salon: ${sId.value}")
                    salonRepository.findById(sId)?.let { salon ->
                        salonRepository.deleteById(salon.id)
                    }
                }

                // Reset all IDs
                salonId = null
                clientId = null
                petId = null
                userId1 = null
                userId2 = null
                staffId1 = null
                staffId2 = null
                serviceId = null
            }
        } catch (e: Exception) {
            logger.warn("Error during cleanup: ${e.message}", e)
        }
    }

    /**
     * Clean all residual test data from the database
     * This is more aggressive cleanup for when tests leave data behind
     */
    private fun cleanAllResidualData() {
        try {
            transactionTemplate.execute {
                logger.info("Performing aggressive cleanup of all residual test data...")

                // Find all appointments and clean them up
                val allAppointments = appointmentRepository.findAll()
                logger.info("Found ${allAppointments.size} total appointments to clean")
                allAppointments.forEach { appointment ->
                    try {
                        appointmentRepository.deleteById(appointment.id)
                        logger.debug("Deleted residual appointment: ${appointment.id.value}")
                    } catch (e: Exception) {
                        logger.warn("Failed to delete appointment ${appointment.id.value}: ${e.message}")
                    }
                }

                // Clean other entities as needed
                logger.info("Aggressive cleanup completed")
            }
        } catch (e: Exception) {
            logger.warn("Error during aggressive cleanup: ${e.message}", e)
        }
    }

    private fun verifyCleanState() {
        try {
            transactionTemplate.execute {
                salonId?.let { sId ->
                    val appointments = appointmentRepository.findBySalonIdWithFilters(sId)
                    if (appointments.isNotEmpty()) {
                        logger.warn("Found ${appointments.size} residual appointments after cleanup")
                        appointments.forEach { appt ->
                            logger.warn(
                                "Residual appointment: ${appt.id.value} at ${appt.appointmentDate} ${appt.startTime}-${appt.endTime}",
                            )
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Error during clean state verification: ${e.message}", e)
        }
    }

    private fun getNextWeekday(): LocalDate {
        var date = LocalDate.now().plusDays(1)
        while (date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY) {
            date = date.plusDays(1)
        }
        return date
    }

    private fun getWeekdayPlusDays(days: Int): LocalDate {
        var date = getNextWeekday().plusDays(days.toLong())
        while (date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY) {
            date = date.plusDays(1)
        }
        return date
    }

    /**
     * Get a unique date for each test to avoid conflicts
     */
    private fun getUniqueTestDate(testNumber: Int): LocalDate {
        var date = LocalDate.now().plusDays(testNumber.toLong() + 1)
        while (date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY) {
            date = date.plusDays(1)
        }
        return date
    }

    @Test
    @Order(1)
    @DisplayName("Should successfully create base appointment without conflicts")
    fun testCreateBaseAppointment() {
        // Given - Use unique date and time for this test
        val appointmentDate = getUniqueTestDate(1)
        logger.info("Test 1: Creating appointment on $appointmentDate at 09:00-10:00")

        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(9, 0), LocalTime.of(10, 0)) // Use 9:00-10:00 to avoid conflicts
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment for conflict testing")
                .build()

        // When
        val appointment = appointmentManagementUseCase.scheduleAppointment(command)

        // Then
        assertNotNull(appointment)
        assertEquals(salonId, appointment.salonId)
        assertEquals(clientId, appointment.clientId)
        assertEquals(petId, appointment.petId)
        assertEquals(staffId1, appointment.staffId)
        assertEquals(LocalTime.of(9, 0), appointment.startTime)
        assertEquals(LocalTime.of(10, 0), appointment.endTime)
        assertEquals(AppointmentStatus.SCHEDULED, appointment.status)

        logger.info("Test 1: Successfully created appointment ${appointment.id.value}")
    }

    @Test
    @Order(2)
    @DisplayName("Should detect appointment overlap conflict and suggest alternatives")
    fun testAppointmentOverlapConflictWithAlternatives() {
        // Given - Create base appointment first
        val appointmentDate = getUniqueTestDate(2)
        logger.info("Test 2: Creating base appointment on $appointmentDate at 10:00-11:00")

        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        val baseAppointment = appointmentManagementUseCase.scheduleAppointment(baseCommand)
        logger.info("Test 2: Created base appointment ${baseAppointment.id.value}")

        // When - Try to create overlapping appointment
        logger.info("Test 2: Attempting to create conflicting appointment at 10:30-11:30")
        val conflictingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Max", "Dog", "Labrador", "L")
                .withStaffId(staffId1!!) // Same staff
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 30), LocalTime.of(11, 30)) // Overlaps with base appointment
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Conflicting appointment")
                .build()

        // Then - Should throw conflict exception with alternatives
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
            }
        logger.info("Test 2: Caught expected conflict exception with ${exception.alternatives.size} alternatives")

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflict items")
        assertEquals(1, exception.conflicts.size, "Should have exactly one conflict")

        val conflict = exception.conflicts.first()
        assertEquals(ConflictItemType.APPOINTMENT, conflict.type)
        assertEquals(staffId1!!.value, conflict.staffId.value)
        assertEquals(baseAppointment.id.value, conflict.id)

        // Verify alternatives are provided
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        assertTrue(exception.alternatives.size <= 5, "Should not exceed 5 alternatives")

        // Check for different types of alternatives
        val hasStaffAlternative = exception.alternatives.any { it.type == AlternativeType.STAFF_ALTERNATIVE }
        val hasTimeAdjustment = exception.alternatives.any { it.type == AlternativeType.TIME_ADJUSTMENT }
        val hasDayAdjustment = exception.alternatives.any { it.type == AlternativeType.DAY_ADJUSTMENT }

        assertTrue(
            hasStaffAlternative || hasTimeAdjustment || hasDayAdjustment,
            "Should provide at least one type of alternative",
        )

        // Verify alternative suggestions are realistic
        exception.alternatives.forEach { alternative ->
            assertTrue(alternative.confidence > 0.0, "Alternative should have positive confidence")
            assertTrue(alternative.confidence <= 1.0, "Alternative confidence should not exceed 1.0")
            assertTrue(alternative.priority > 0, "Alternative should have positive priority")
            assertNotNull(alternative.reason, "Alternative should have a reason")
            assertNotNull(alternative.suggestion.staffName, "Alternative should have staff name")
        }
    }

    @Test
    @Order(3)
    @DisplayName("Should suggest staff alternative when same time slot is available with different staff")
    fun testStaffAlternativeSuggestion() {
        // Given - Create base appointment with staff1
        val appointmentDate = getUniqueTestDate(3)
        logger.info("Test 3: Creating base appointment on $appointmentDate at 11:00-12:00 with staff1")

        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(11, 0), LocalTime.of(12, 0)) // Different time to avoid conflicts
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        val baseAppointment = appointmentManagementUseCase.scheduleAppointment(baseCommand)
        logger.info("Test 3: Created base appointment ${baseAppointment.id.value}")

        // When - Try to book same time with same staff (should conflict and suggest alternatives)
        logger.info("Test 3: Attempting to create conflicting appointment at same time with same staff")
        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Luna", "Cat", "Persian", "S")
                .withStaffId(staffId1!!) // Same staff as base appointment
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(11, 0), LocalTime.of(12, 0)) // Exact same time
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Should suggest staff alternative")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }
        logger.info("Test 3: Caught expected conflict exception with ${exception.alternatives.size} alternatives")

        // Should have alternative suggestions (staff or time alternatives)
        assertTrue(exception.alternatives.isNotEmpty(), "Should suggest alternatives")

        // Check for staff alternatives (different staff, same time)
        val staffAlternatives = exception.alternatives.filter { it.type == AlternativeType.STAFF_ALTERNATIVE }
        val timeAlternatives = exception.alternatives.filter { it.type == AlternativeType.TIME_ADJUSTMENT }

        // Should have either staff alternatives or time alternatives
        assertTrue(
            staffAlternatives.isNotEmpty() || timeAlternatives.isNotEmpty(),
            "Should suggest either staff or time alternatives",
        )

        if (staffAlternatives.isNotEmpty()) {
            val staffAlternative = staffAlternatives.first()
            assertEquals(
                staffId2!!.value,
                staffAlternative.suggestion.staffId,
                "Should suggest different staff (staff2)",
            )
            assertEquals(appointmentDate, staffAlternative.suggestion.date, "Should keep same date")
            assertEquals(LocalTime.of(11, 0), staffAlternative.suggestion.startTime, "Should keep same start time")
            assertEquals(LocalTime.of(12, 0), staffAlternative.suggestion.endTime, "Should keep same end time")
        }

        if (timeAlternatives.isNotEmpty()) {
            val timeAlternative = timeAlternatives.first()
            assertEquals(appointmentDate, timeAlternative.suggestion.date, "Should keep same date")

            // Verify the alternative is a valid time slot (not the same as the conflicting one)
            val isDifferentTime =
                timeAlternative.suggestion.startTime != LocalTime.of(11, 0) ||
                    timeAlternative.suggestion.endTime != LocalTime.of(12, 0)
            assertTrue(isDifferentTime, "Should suggest different time")

            // Verify the alternative is within working hours
            assertTrue(
                timeAlternative.suggestion.startTime.isAfter(LocalTime.of(8, 59)),
                "Alternative should be within working hours",
            )
            assertTrue(
                timeAlternative.suggestion.endTime.isBefore(LocalTime.of(18, 1)),
                "Alternative should be within working hours",
            )

            // Verify the duration is maintained (1 hour)
            assertEquals(
                java.time.Duration.ofHours(1),
                java.time.Duration.between(timeAlternative.suggestion.startTime, timeAlternative.suggestion.endTime),
                "Alternative should maintain same duration",
            )
        }
    }

    // ========================================
    // STAFF AVAILABILITY CONFLICT TESTS
    // ========================================

    @Test
    @Order(4)
    @DisplayName("Should detect conflict when appointment is scheduled outside staff working hours - before start")
    fun testStaffUnavailableBeforeWorkingHours() {
        // Given
        val appointmentDate = getUniqueTestDate(4)
        logger.info(
            "Test 4: Attempting to create appointment on $appointmentDate at 08:00-09:00 (before working hours)",
        )

        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Early Bird", "Dog", "Beagle", "M")
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(8, 0), LocalTime.of(9, 0)) // Before 9:00 start time
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Before working hours")
                .build()

        // When & Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }
        logger.info("Test 4: Caught expected conflict exception with ${exception.alternatives.size} alternatives")

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")

        // Should suggest later time slots within working hours
        val timeAdjustments = exception.alternatives.filter { it.type == AlternativeType.TIME_ADJUSTMENT }
        assertTrue(timeAdjustments.isNotEmpty(), "Should suggest time adjustments")

        timeAdjustments.forEach { alternative ->
            assertTrue(
                alternative.suggestion.startTime.isAfter(LocalTime.of(8, 59)),
                "Alternative should be within working hours",
            )
            assertTrue(
                alternative.suggestion.endTime.isBefore(LocalTime.of(18, 1)),
                "Alternative should be within working hours",
            )
        }
    }

    @Test
    @Order(5)
    @DisplayName("Should detect conflict when appointment is scheduled outside staff working hours - after end")
    fun testStaffUnavailableAfterWorkingHours() {
        // Given
        val appointmentDate = getNextWeekday()
        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Night Owl", "Cat", "Siamese", "S")
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(18, 30), LocalTime.of(19, 30)) // After 18:00 end time
                .withServiceIds(listOf(serviceId!!))
                .withNotes("After working hours")
                .build()

        // When & Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")

        // Should suggest earlier time slots or next day
        val alternatives = exception.alternatives
        assertTrue(
            alternatives.any { it.type == AlternativeType.TIME_ADJUSTMENT || it.type == AlternativeType.DAY_ADJUSTMENT },
            "Should suggest time or day adjustments",
        )
    }

    @Test
    @Order(6)
    @DisplayName("Should detect conflict when appointment is scheduled during staff break time")
    fun testStaffUnavailableDuringBreakTime() {
        // Given
        val appointmentDate = getNextWeekday()
        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Lunch Break", "Dog", "Poodle", "L")
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(13, 15), LocalTime.of(14, 15)) // During 13:00-14:00 break
                .withServiceIds(listOf(serviceId!!))
                .withNotes("During break time")
                .build()

        // When & Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")

        // Should suggest time slots outside break time
        val timeAdjustments = exception.alternatives.filter { it.type == AlternativeType.TIME_ADJUSTMENT }
        assertTrue(timeAdjustments.isNotEmpty(), "Should suggest time adjustments")

        // Verify that at least some alternatives avoid break time
        val validAlternatives =
            timeAdjustments.filter { alternative ->
                val startTime = alternative.suggestion.startTime
                val endTime = alternative.suggestion.endTime
                // Check if alternative doesn't overlap with break time (13:00-14:00)
                endTime.isBefore(LocalTime.of(13, 0)) || startTime.isAfter(LocalTime.of(14, 0))
            }
        assertTrue(
            validAlternatives.isNotEmpty(),
            "Should have at least some alternatives that don't overlap with break time",
        )
    }

    @Test
    @Order(7)
    @DisplayName("Should detect conflict when appointment is scheduled on weekend (staff day off)")
    @Disabled
    fun testStaffUnavailableOnWeekend() {
        // Given - Find next Saturday
        var weekendDate = LocalDate.now().plusDays(1)
        while (weekendDate.dayOfWeek != DayOfWeek.SATURDAY) {
            weekendDate = weekendDate.plusDays(1)
        }

        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Weekend", "Dog", "Bulldog", "L")
                .withStaffId(staffId1!!)
                .withDate(weekendDate)
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Weekend appointment")
                .build()

        // When & Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")

        // Should suggest weekday alternatives
        val dayAdjustments = exception.alternatives.filter { it.type == AlternativeType.DAY_ADJUSTMENT }
        assertTrue(dayAdjustments.isNotEmpty(), "Should suggest day adjustments")

        dayAdjustments.forEach { alternative ->
            val dayOfWeek = alternative.suggestion.date.dayOfWeek
            assertTrue(
                dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY,
                "Alternative should be on weekday, got: $dayOfWeek",
            )
        }
    }

    // ========================================
    // APPOINTMENT OVERLAP CONFLICT TESTS
    // ========================================

    @Test
    @Order(8)
    @DisplayName("Should detect direct time slot conflict (exact same time)")
    fun testDirectTimeSlotConflict() {
        // Given - Create base appointment
        val appointmentDate = getWeekdayPlusDays(5)
        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(14, 30), LocalTime.of(15, 30))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Try to create appointment at exact same time
        val conflictingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Conflict", "Cat", "Maine Coon", "L")
                .withStaffId(staffId1!!) // Same staff
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(14, 30), LocalTime.of(15, 30)) // Exact same time
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Conflicting appointment")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
            }

        // Verify conflict details
        assertEquals(1, exception.conflicts.size, "Should have exactly one conflict")
        val conflict = exception.conflicts.first()
        assertEquals(ConflictItemType.APPOINTMENT, conflict.type)
        assertEquals(staffId1!!.value, conflict.staffId.value)

        // Verify alternatives
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        verifyAlternativesAreBookable(exception.alternatives, appointmentDate)
    }

    @Test
    @Order(9)
    @DisplayName("Should detect partial overlap conflict (start time overlaps)")
    fun testPartialOverlapConflictStartTime() {
        // Given - Create base appointment
        val appointmentDate = getWeekdayPlusDays(6)
        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId2!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 30))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Try to create appointment that overlaps at start
        val conflictingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Overlap Start", "Dog", "Husky", "L")
                .withStaffId(staffId2!!) // Same staff
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 45), LocalTime.of(12, 15)) // Overlaps at start
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Overlapping appointment")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
            }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertEquals(ConflictItemType.APPOINTMENT, exception.conflicts.first().type)

        // Verify alternatives suggest non-overlapping times
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        verifyAlternativesAreBookable(exception.alternatives, appointmentDate)
    }

    @Test
    @Order(10)
    @DisplayName("Should detect partial overlap conflict (end time overlaps)")
    fun testPartialOverlapConflictEndTime() {
        // Given - Create base appointment
        val appointmentDate = getWeekdayPlusDays(7)
        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(15, 0), LocalTime.of(16, 30))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Try to create appointment that overlaps at end
        val conflictingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Overlap End", "Cat", "Ragdoll", "L")
                .withStaffId(staffId1!!) // Same staff
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(14, 0), LocalTime.of(15, 45)) // Overlaps at end
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Overlapping appointment")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
            }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertEquals(ConflictItemType.APPOINTMENT, exception.conflicts.first().type)

        // Verify alternatives
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        verifyAlternativesAreBookable(exception.alternatives, appointmentDate)
    }

    @Test
    @Order(11)
    @DisplayName("Should allow adjacent appointments (no overlap)")
    fun testAdjacentAppointmentsAllowed() {
        // Given - Create base appointment
        val appointmentDate = getWeekdayPlusDays(8)
        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Create adjacent appointment (starts when first ends)
        val adjacentCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Adjacent", "Dog", "Corgi", "M")
                .withStaffId(staffId1!!) // Same staff
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(11, 0), LocalTime.of(12, 0)) // Starts when first ends
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Adjacent appointment")
                .build()

        // Then - Should succeed (no overlap)
        val appointment = appointmentManagementUseCase.scheduleAppointment(adjacentCommand)
        assertNotNull(appointment)
        assertEquals(LocalTime.of(11, 0), appointment.startTime)
        assertEquals(LocalTime.of(12, 0), appointment.endTime)
    }

    // ========================================
    // BLOCK TIME CONFLICT TESTS
    // ========================================

    @Test
    @Order(12)
    @DisplayName("Should detect conflict when appointment overlaps with block time")
    fun testBlockTimeConflict() {
        // Given - Create a block time
        val appointmentDate = getUniqueTestDate(12)
        logger.info("Test 12: Creating block time on $appointmentDate at 14:00-15:00")

        // Create a block time that overlaps with the appointment (14:15-15:15 Europe/Bucharest)
        // The appointment 14:15-15:15 Europe/Bucharest = 11:15-12:15 UTC
        // So we need to create a block time that overlaps with 11:15-12:15 UTC
        // Let's create a block time from 14:00-15:00 Europe/Bucharest = 11:00-12:00 UTC
        // This will overlap with the appointment time (11:15-12:15 UTC overlaps with 11:00-12:00 UTC)
        val blockStartTime = appointmentDate.atTime(LocalTime.of(14, 0)).atZone(ZoneId.systemDefault())
        val blockEndTime = appointmentDate.atTime(LocalTime.of(15, 0)).atZone(ZoneId.systemDefault())

        val blockTime =
            TestDataBuilder.aBlockTime(
                salonId = salonId!!,
                startTime = blockStartTime,
                endTime = blockEndTime,
                reason = BlockReason.PAUZA,
                staffIds = setOf(staffId1!!),
            )
        val savedBlockTime = blockTimeRepository.save(blockTime)
        logger.info("Test 12: Created block time ${savedBlockTime.id.value}")

        // Verify the block time was saved correctly
        assertTrue(savedBlockTime.affectsStaff(staffId1!!))
        assertTrue(savedBlockTime.isActive())

        // When - Try to create appointment during block time
        logger.info("Test 12: Attempting to create appointment at 14:15-15:15 (overlaps with block time)")
        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Block Conflict", "Cat", "British Shorthair", "M")
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(14, 15), LocalTime.of(15, 15)) // Overlaps with block time (14:00-15:00)
                .withServiceIds(listOf(serviceId!!))
                .withNotes("During block time")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }
        logger.info("Test 12: Caught expected conflict exception with ${exception.alternatives.size} alternatives")

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        val conflict = exception.conflicts.first()
        assertEquals(ConflictItemType.BLOCK_TIME, conflict.type)
        assertEquals(staffId1!!.value, conflict.staffId.value)

        // Verify alternatives
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        verifyAlternativesAreBookable(exception.alternatives, appointmentDate)

        // Verify that at least some alternatives avoid block time
        val validAlternatives =
            exception.alternatives.filter { alternative ->
                val altStart = alternative.suggestion.startTime
                val altEnd = alternative.suggestion.endTime
                val blockStart = LocalTime.of(14, 0) // Block time is now correctly stored as 14:00-15:00
                val blockEnd = LocalTime.of(15, 0)

                // Check if alternative doesn't overlap with block time
                altEnd.isBefore(blockStart) || altStart.isAfter(blockEnd)
            }
        assertTrue(
            validAlternatives.isNotEmpty(),
            "Should have at least some alternatives that don't overlap with block time",
        )
    }

    @Test
    @Order(13)
    @DisplayName("Should detect conflict with multiple block times")
    fun testMultipleBlockTimeConflicts() {
        // Given - Create multiple block times
        val appointmentDate = getWeekdayPlusDays(10)
        val block1StartTime = appointmentDate.atTime(LocalTime.of(10, 0)).atZone(ZoneId.systemDefault())
        val block1EndTime = appointmentDate.atTime(LocalTime.of(11, 0)).atZone(ZoneId.systemDefault())
        val block2StartTime = appointmentDate.atTime(LocalTime.of(15, 0)).atZone(ZoneId.systemDefault())
        val block2EndTime = appointmentDate.atTime(LocalTime.of(16, 0)).atZone(ZoneId.systemDefault())

        val blockTime1 =
            TestDataBuilder.aBlockTime(
                salonId = salonId!!,
                startTime = block1StartTime,
                endTime = block1EndTime,
                reason = BlockReason.INTALNIRE,
                staffIds = setOf(staffId2!!),
            )
        val blockTime2 =
            TestDataBuilder.aBlockTime(
                salonId = salonId!!,
                startTime = block2StartTime,
                endTime = block2EndTime,
                reason = BlockReason.TRAINING,
                staffIds = setOf(staffId2!!),
            )
        blockTimeRepository.save(blockTime1)
        blockTimeRepository.save(blockTime2)

        // When - Try to create appointment that spans both blocks
        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Multi Block", "Dog", "German Shepherd", "L")
                .withStaffId(staffId2!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 30), LocalTime.of(11, 30)) // Overlaps with first block
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Overlaps with block")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        verifyAlternativesAreBookable(exception.alternatives, appointmentDate)
    }

    // ========================================
    // CUSTOM CLOSURE AND HOLIDAY TESTS
    // ========================================

    @Test
    @Order(14)
    @DisplayName("Should detect conflict when appointment is scheduled on staff custom closure day")
    fun testStaffCustomClosureConflict() {
        // Given - Add custom closure for staff
        val closureDate = getWeekdayPlusDays(11)
        val customClosure =
            StaffCustomClosure.create(
                salonId = salonId!!,
                reason = "Concediu medical",
                date = closureDate,
                description = "Staff medical leave",
            )

        val updatedStaffHours =
            staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId1!!, salonId!!)!!
                .updateCustomClosures(listOf(customClosure))
        staffWorkingHoursRepository.save(updatedStaffHours)

        // When - Try to create appointment on closure day
        val command =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Closure Day", "Dog", "Boxer", "L")
                .withStaffId(staffId1!!)
                .withDate(closureDate)
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("On closure day")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(command)
            }

        // Verify conflict details
        assertTrue(exception.conflicts.isNotEmpty(), "Should have conflicts")
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")

        // Alternatives should be on different days
        val dayAdjustments = exception.alternatives.filter { it.type == AlternativeType.DAY_ADJUSTMENT }
        assertTrue(dayAdjustments.isNotEmpty(), "Should suggest day adjustments")
        dayAdjustments.forEach { alternative ->
            assertNotEquals(
                closureDate,
                alternative.suggestion.date,
                "Alternative should not be on closure day",
            )
        }
    }

    // ========================================
    // ALTERNATIVE VALIDATION TESTS
    // ========================================

    @Test
    @Order(15)
    @DisplayName("Should provide alternatives that are actually bookable")
    fun testAlternativeValidation() {
        // Given - Create appointment that will conflict
        val appointmentDate = getWeekdayPlusDays(12)
        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(11, 0), LocalTime.of(12, 0))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Try to create conflicting appointment
        val conflictingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Validation Test", "Cat", "Scottish Fold", "M")
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(11, 30), LocalTime.of(12, 30)) // Overlaps
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Conflicting appointment")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
            }

        // Verify all alternatives are actually bookable
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")
        verifyAlternativesAreBookable(exception.alternatives, appointmentDate)

        // Test that we can actually book the first alternative
        val firstAlternative = exception.alternatives.first()
        val alternativeCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Alternative Test", "Dog", "Dachshund", "S")
                .withStaffId(StaffId.of(firstAlternative.suggestion.staffId))
                .withDate(firstAlternative.suggestion.date)
                .withTimeSlot(firstAlternative.suggestion.startTime, firstAlternative.suggestion.endTime)
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Testing alternative booking")
                .build()

        // Should succeed without conflicts
        val alternativeAppointment = appointmentManagementUseCase.scheduleAppointment(alternativeCommand)
        assertNotNull(alternativeAppointment)
        assertEquals(firstAlternative.suggestion.startTime, alternativeAppointment.startTime)
        assertEquals(firstAlternative.suggestion.endTime, alternativeAppointment.endTime)
    }

    @Test
    @Order(16)
    @DisplayName("Should provide alternatives with proper confidence and priority ordering")
    fun testAlternativeOrdering() {
        // Given - Create appointment that will conflict
        val appointmentDate = getWeekdayPlusDays(13)
        val baseCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId2!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(16, 0), LocalTime.of(17, 0))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Base appointment")
                .build()

        appointmentManagementUseCase.scheduleAppointment(baseCommand)

        // When - Try to create conflicting appointment
        val conflictingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Ordering Test", "Cat", "Abyssinian", "M")
                .withStaffId(staffId2!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(16, 30), LocalTime.of(17, 30)) // Overlaps
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Conflicting appointment")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
            }

        // Verify alternatives are properly ordered
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")

        // Check confidence values are valid
        exception.alternatives.forEach { alternative ->
            assertTrue(alternative.confidence > 0.0, "Confidence should be positive")
            assertTrue(alternative.confidence <= 1.0, "Confidence should not exceed 1.0")
            assertTrue(alternative.priority > 0, "Priority should be positive")
            assertNotNull(alternative.reason, "Should have reason")
            assertNotNull(alternative.suggestion.staffName, "Should have staff name")
        }

        // Check that alternatives are sorted by priority and confidence
        val sortedAlternatives =
            exception.alternatives.sortedWith(
                compareBy<AlternativeSuggestion> { it.priority }.thenByDescending { it.confidence },
            )
        assertEquals(
            sortedAlternatives,
            exception.alternatives,
            "Alternatives should be sorted by priority (asc) then confidence (desc)",
        )
    }

    // ========================================
    // COMPLEX SCENARIO TESTS
    // ========================================

    @Test
    @Order(17)
    @DisplayName("Should handle multiple conflict types simultaneously")
    fun testMultipleConflictTypes() {
        // Given - Create a complex scenario with appointment + block time
        val appointmentDate = getWeekdayPlusDays(14)

        // Create existing appointment
        val existingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withPetId(petId)
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 0), LocalTime.of(11, 0))
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Existing appointment")
                .build()
        appointmentManagementUseCase.scheduleAppointment(existingCommand)

        // Create block time
        val blockStartTime = appointmentDate.atTime(LocalTime.of(15, 0)).atZone(ZoneId.systemDefault())
        val blockEndTime = appointmentDate.atTime(LocalTime.of(16, 0)).atZone(ZoneId.systemDefault())
        val blockTime =
            TestDataBuilder.aBlockTime(
                salonId = salonId!!,
                startTime = blockStartTime,
                endTime = blockEndTime,
                reason = BlockReason.PERSONAL,
                staffIds = setOf(staffId1!!),
            )
        blockTimeRepository.save(blockTime)

        // When - Try to create appointment that would conflict with existing appointment
        val conflictingCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Complex Conflict", "Dog", "Pomeranian", "S")
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(10, 30), LocalTime.of(11, 30)) // Overlaps with existing
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Complex conflict scenario")
                .build()

        // Then
        val exception =
            assertThrows<AppointmentSchedulingConflictException> {
                appointmentManagementUseCase.scheduleAppointment(conflictingCommand)
            }

        // Verify conflict detection
        assertTrue(exception.conflicts.isNotEmpty(), "Should detect conflicts")
        assertTrue(exception.alternatives.isNotEmpty(), "Should provide alternatives")

        // Verify alternatives avoid both appointment and block time conflicts
        verifyAlternativesAreBookable(exception.alternatives, appointmentDate)

        // Verify that at least some alternatives avoid the existing appointment
        val validAppointmentAlternatives =
            exception.alternatives.filter { alternative ->
                val altStart = alternative.suggestion.startTime
                val altEnd = alternative.suggestion.endTime

                // Check if alternative doesn't overlap with existing appointment (10:00-11:00)
                altEnd.isBefore(LocalTime.of(10, 0)) || altStart.isAfter(LocalTime.of(11, 0))
            }
        assertTrue(
            validAppointmentAlternatives.isNotEmpty(),
            "Should have alternatives that don't overlap with existing appointment",
        )

        // Verify that same-day alternatives avoid block time
        val sameDayAlternatives = exception.alternatives.filter { it.suggestion.date == appointmentDate }
        if (sameDayAlternatives.isNotEmpty()) {
            val validBlockAlternatives =
                sameDayAlternatives.filter { alternative ->
                    val altStart = alternative.suggestion.startTime
                    val altEnd = alternative.suggestion.endTime

                    // Check if alternative doesn't overlap with block time (15:00-16:00)
                    altEnd.isBefore(LocalTime.of(15, 0)) || altStart.isAfter(LocalTime.of(16, 0))
                }
            assertTrue(
                validBlockAlternatives.isNotEmpty(),
                "Same-day alternatives should avoid block time",
            )
        }
    }

    @Test
    @Order(18)
    @DisplayName("Should handle boundary conditions at working hour edges")
    fun testBoundaryConditions() {
        // Given
        val appointmentDate = getWeekdayPlusDays(15)

        // When - Try to create appointment that ends exactly at closing time
        val boundaryCommand =
            TestCommandBuilder.aScheduleAppointmentCommand()
                .withSalonId(salonId!!)
                .withClientId(clientId!!)
                .withNewPet("Boundary Test", "Cat", "Sphynx", "M")
                .withStaffId(staffId1!!)
                .withDate(appointmentDate)
                .withTimeSlot(LocalTime.of(17, 0), LocalTime.of(18, 0)) // Ends at 18:00 (closing)
                .withServiceIds(listOf(serviceId!!))
                .withNotes("Boundary appointment")
                .build()

        // Then - Should succeed (appointment ends exactly at closing time)
        val appointment = appointmentManagementUseCase.scheduleAppointment(boundaryCommand)
        assertNotNull(appointment)
        assertEquals(LocalTime.of(17, 0), appointment.startTime)
        assertEquals(LocalTime.of(18, 0), appointment.endTime)
    }

    // ========================================
    // HELPER METHODS
    // ========================================

    /**
     * Verify that all provided alternatives are actually bookable
     */
    private fun verifyAlternativesAreBookable(
        alternatives: List<AlternativeSuggestion>,
        originalDate: LocalDate,
    ) {
        assertTrue(alternatives.isNotEmpty(), "Should have alternatives to verify")

        alternatives.forEach { alternative ->
            // Verify basic alternative structure
            assertNotNull(alternative.suggestion.staffId, "Alternative should have staff ID")
            assertNotNull(alternative.suggestion.staffName, "Alternative should have staff name")
            assertNotNull(alternative.suggestion.date, "Alternative should have date")
            assertNotNull(alternative.suggestion.startTime, "Alternative should have start time")
            assertNotNull(alternative.suggestion.endTime, "Alternative should have end time")

            // Verify time slot is valid
            assertTrue(
                alternative.suggestion.startTime.isBefore(alternative.suggestion.endTime),
                "Alternative start time should be before end time",
            )

            // Verify staff exists
            val staffId = StaffId.of(alternative.suggestion.staffId)
            val staff = staffRepository.findById(staffId)
            assertNotNull(staff, "Alternative staff should exist: ${alternative.suggestion.staffId}")

            // Verify alternative is within reasonable time bounds
            val altDate = alternative.suggestion.date
            assertTrue(
                altDate.isAfter(originalDate.minusDays(7)) && altDate.isBefore(originalDate.plusDays(30)),
                "Alternative date should be within reasonable range: $altDate",
            )

            // Verify working hours compliance for same-day alternatives
            if (altDate == originalDate || altDate.dayOfWeek in
                listOf(
                    DayOfWeek.MONDAY, DayOfWeek.TUESDAY,
                    DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY,
                )
            ) {
                assertTrue(
                    alternative.suggestion.startTime.isAfter(LocalTime.of(8, 59)),
                    "Alternative should be after working hours start",
                )
                assertTrue(
                    alternative.suggestion.endTime.isBefore(LocalTime.of(18, 1)),
                    "Alternative should be before working hours end",
                )
            }
        }
    }
}
