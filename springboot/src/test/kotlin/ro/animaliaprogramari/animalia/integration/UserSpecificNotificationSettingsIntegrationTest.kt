//package ro.animaliaprogramari.animalia.integration
//
//import org.junit.jupiter.api.Assertions.*
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.DisplayName
//import org.junit.jupiter.api.Nested
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.test.context.ActiveProfiles
//import org.springframework.transaction.annotation.Transactional
//import ro.animaliaprogramari.animalia.application.command.UpdateNotificationSettingsCommand
//import ro.animaliaprogramari.animalia.application.port.inbound.NotificationSettingsManagementUseCase
//import ro.animaliaprogramari.animalia.application.port.outbound.NotificationSettingsRepository
//import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
//import ro.animaliaprogramari.animalia.application.query.GetNotificationSettingsQuery
//import ro.animaliaprogramari.animalia.domain.model.*
//import java.time.LocalTime
//
///**
// * Integration tests for user-specific notification settings
// */
//@SpringBootTest
//@ActiveProfiles("test")
//@Transactional
//@DisplayName("User-Specific Notification Settings Integration Tests")
//class UserSpecificNotificationSettingsIntegrationTest {
//
//    @Autowired
//    private lateinit var notificationSettingsUseCase: NotificationSettingsManagementUseCase
//
//    @Autowired
//    private lateinit var notificationSettingsRepository: NotificationSettingsRepository
//
//    @Autowired
//    private lateinit var salonRepository: SalonRepository
//
//    private lateinit var testSalon: Salon
//    private lateinit var testSalon2: Salon
//    private lateinit var testUserId: UserId
//    private lateinit var testUserId2: UserId
//
//    @BeforeEach
//    fun setUp() {
//        // Create test salons
//        testSalon = Salon(
//            id = SalonId.generate(),
//            name = "Test Salon 1",
//            address = "Test Address 1",
//            phoneNumber = "+40123456789",
//            email = Email.of("<EMAIL>"),
//            isActive = true
//        )
//
//        testSalon2 = Salon(
//            id = SalonId.generate(),
//            name = "Test Salon 2",
//            address = "Test Address 2",
//            phoneNumber = "+40123456790",
//            email = Email.of("<EMAIL>"),
//            isActive = true
//        )
//
//        salonRepository.save(testSalon)
//        salonRepository.save(testSalon2)
//
//        testUserId = UserId.generate()
//        testUserId2 = UserId.generate()
//    }
//
//    @Nested
//    @DisplayName("User Isolation Tests")
//    inner class UserIsolationTests {
//
//        @Test
//        fun `should maintain separate settings for different users in same salon`() {
//            // Given
//            val query1 = GetNotificationSettingsQuery(testUserId, testSalon.id)
//            val query2 = GetNotificationSettingsQuery(testUserId2, testSalon.id)
//
//            // When - Get initial settings (should create defaults)
//            val settings1 = notificationSettingsUseCase.getNotificationSettings(query1)
//            val settings2 = notificationSettingsUseCase.getNotificationSettings(query2)
//
//            // Then - Both users should have separate default settings
//            assertEquals(testUserId, settings1.userId)
//            assertEquals(testUserId2, settings2.userId)
//            assertEquals(testSalon.id, settings1.salonId)
//            assertEquals(testSalon.id, settings2.salonId)
//            assertTrue(settings1.pushNotificationsEnabled)
//            assertTrue(settings2.pushNotificationsEnabled)
//
//            // When - Update settings for user 1
//            val updateCommand1 = UpdateNotificationSettingsCommand(
//                userId = testUserId,
//                salonId = testSalon.id,
//                pushNotificationsEnabled = false,
//                soundPreference = SoundPreference.SILENT,
//                vibrationEnabled = false,
//                doNotDisturb = DoNotDisturbSettings(enabled = true),
//                notificationRules = NotificationRules(newAppointments = false)
//            )
//            val updatedSettings1 = notificationSettingsUseCase.updateNotificationSettings(updateCommand1)
//
//            // Then - Only user 1's settings should be updated
//            assertFalse(updatedSettings1.pushNotificationsEnabled)
//            assertEquals(SoundPreference.SILENT, updatedSettings1.soundPreference)
//            assertTrue(updatedSettings1.doNotDisturb.enabled)
//            assertFalse(updatedSettings1.notificationRules.newAppointments)
//
//            // When - Get user 2's settings again
//            val unchangedSettings2 = notificationSettingsUseCase.getNotificationSettings(query2)
//
//            // Then - User 2's settings should remain unchanged
//            assertTrue(unchangedSettings2.pushNotificationsEnabled)
//            assertEquals(SoundPreference.DEFAULT, unchangedSettings2.soundPreference)
//            assertFalse(unchangedSettings2.doNotDisturb.enabled)
//            assertTrue(unchangedSettings2.notificationRules.newAppointments)
//        }
//
//        @Test
//        fun `should maintain separate settings for same user across different salons`() {
//            // Given
//            val query1 = GetNotificationSettingsQuery(testUserId, testSalon.id)
//            val query2 = GetNotificationSettingsQuery(testUserId, testSalon2.id)
//
//            // When - Get initial settings for same user in different salons
//            val settings1 = notificationSettingsUseCase.getNotificationSettings(query1)
//            val settings2 = notificationSettingsUseCase.getNotificationSettings(query2)
//
//            // Then - Same user should have separate settings per salon
//            assertEquals(testUserId, settings1.userId)
//            assertEquals(testUserId, settings2.userId)
//            assertEquals(testSalon.id, settings1.salonId)
//            assertEquals(testSalon2.id, settings2.salonId)
//
//            // When - Update settings for salon 1
//            val updateCommand1 = UpdateNotificationSettingsCommand(
//                userId = testUserId,
//                salonId = testSalon.id,
//                pushNotificationsEnabled = false,
//                soundPreference = SoundPreference.CUSTOM1,
//                vibrationEnabled = false,
//                doNotDisturb = DoNotDisturbSettings(
//                    enabled = true,
//                    startTime = LocalTime.of(23, 0),
//                    endTime = LocalTime.of(7, 0)
//                ),
//                notificationRules = NotificationRules(
//                    newAppointments = false,
//                    teamMemberUpdates = false
//                )
//            )
//            val updatedSettings1 = notificationSettingsUseCase.updateNotificationSettings(updateCommand1)
//
//            // When - Update settings for salon 2 with different preferences
//            val updateCommand2 = UpdateNotificationSettingsCommand(
//                userId = testUserId,
//                salonId = testSalon2.id,
//                pushNotificationsEnabled = true,
//                soundPreference = SoundPreference.CUSTOM2,
//                vibrationEnabled = true,
//                doNotDisturb = DoNotDisturbSettings(
//                    enabled = false
//                ),
//                notificationRules = NotificationRules(
//                    newAppointments = true,
//                    teamMemberUpdates = true,
//                    paymentConfirmations = false
//                )
//            )
//            val updatedSettings2 = notificationSettingsUseCase.updateNotificationSettings(updateCommand2)
//
//            // Then - Settings should be different for each salon
//            assertEquals(testUserId, updatedSettings1.userId)
//            assertEquals(testUserId, updatedSettings2.userId)
//            assertEquals(testSalon.id, updatedSettings1.salonId)
//            assertEquals(testSalon2.id, updatedSettings2.salonId)
//
//            // Salon 1 settings
//            assertFalse(updatedSettings1.pushNotificationsEnabled)
//            assertEquals(SoundPreference.CUSTOM1, updatedSettings1.soundPreference)
//            assertFalse(updatedSettings1.vibrationEnabled)
//            assertTrue(updatedSettings1.doNotDisturb.enabled)
//            assertFalse(updatedSettings1.notificationRules.newAppointments)
//            assertFalse(updatedSettings1.notificationRules.teamMemberUpdates)
//
//            // Salon 2 settings
//            assertTrue(updatedSettings2.pushNotificationsEnabled)
//            assertEquals(SoundPreference.CUSTOM2, updatedSettings2.soundPreference)
//            assertTrue(updatedSettings2.vibrationEnabled)
//            assertFalse(updatedSettings2.doNotDisturb.enabled)
//            assertTrue(updatedSettings2.notificationRules.newAppointments)
//            assertTrue(updatedSettings2.notificationRules.teamMemberUpdates)
//            assertFalse(updatedSettings2.notificationRules.paymentConfirmations)
//        }
//    }
//
//    @Nested
//    @DisplayName("Repository Integration Tests")
//    inner class RepositoryIntegrationTests {
//
//        @Test
//        fun `should persist and retrieve user-specific settings correctly`() {
//            // Given
//            val settings = NotificationSettings.createDefault(testUserId, testSalon.id)
//                .updateSettings(
//                    pushNotificationsEnabled = false,
//                    soundPreference = SoundPreference.SILENT,
//                    vibrationEnabled = false,
//                    doNotDisturb = DoNotDisturbSettings(
//                        enabled = true,
//                        startTime = LocalTime.of(22, 30),
//                        endTime = LocalTime.of(8, 30),
//                        allowCritical = false
//                    ),
//                    notificationRules = NotificationRules(
//                        newAppointments = false,
//                        appointmentCancellations = true,
//                        paymentConfirmations = false,
//                        teamMemberUpdates = true,
//                        systemMaintenanceAlerts = false,
//                        defaultPriority = NotificationPriority.HIGH
//                    )
//                )
//
//            // When
//            val savedSettings = notificationSettingsRepository.save(settings)
//            val retrievedSettings = notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalon.id)
//
//            // Then
//            assertNotNull(retrievedSettings)
//            assertEquals(savedSettings.userId, retrievedSettings!!.userId)
//            assertEquals(savedSettings.salonId, retrievedSettings.salonId)
//            assertEquals(savedSettings.pushNotificationsEnabled, retrievedSettings.pushNotificationsEnabled)
//            assertEquals(savedSettings.soundPreference, retrievedSettings.soundPreference)
//            assertEquals(savedSettings.vibrationEnabled, retrievedSettings.vibrationEnabled)
//            assertEquals(savedSettings.doNotDisturb.enabled, retrievedSettings.doNotDisturb.enabled)
//            assertEquals(savedSettings.doNotDisturb.startTime, retrievedSettings.doNotDisturb.startTime)
//            assertEquals(savedSettings.doNotDisturb.endTime, retrievedSettings.doNotDisturb.endTime)
//            assertEquals(savedSettings.doNotDisturb.allowCritical, retrievedSettings.doNotDisturb.allowCritical)
//            assertEquals(savedSettings.notificationRules.newAppointments, retrievedSettings.notificationRules.newAppointments)
//            assertEquals(savedSettings.notificationRules.defaultPriority, retrievedSettings.notificationRules.defaultPriority)
//        }
//
//        @Test
//        fun `should handle multiple users and salons in repository`() {
//            // Given
//            val settings1 = NotificationSettings.createDefault(testUserId, testSalon.id)
//            val settings2 = NotificationSettings.createDefault(testUserId2, testSalon.id)
//            val settings3 = NotificationSettings.createDefault(testUserId, testSalon2.id)
//            val settings4 = NotificationSettings.createDefault(testUserId2, testSalon2.id)
//
//            // When
//            notificationSettingsRepository.save(settings1)
//            notificationSettingsRepository.save(settings2)
//            notificationSettingsRepository.save(settings3)
//            notificationSettingsRepository.save(settings4)
//
//            // Then - Each combination should be retrievable
//            val retrieved1 = notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalon.id)
//            val retrieved2 = notificationSettingsRepository.findByUserIdAndSalonId(testUserId2, testSalon.id)
//            val retrieved3 = notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalon2.id)
//            val retrieved4 = notificationSettingsRepository.findByUserIdAndSalonId(testUserId2, testSalon2.id)
//
//            assertNotNull(retrieved1)
//            assertNotNull(retrieved2)
//            assertNotNull(retrieved3)
//            assertNotNull(retrieved4)
//
//            assertEquals(testUserId, retrieved1!!.userId)
//            assertEquals(testUserId2, retrieved2!!.userId)
//            assertEquals(testUserId, retrieved3!!.userId)
//            assertEquals(testUserId2, retrieved4!!.userId)
//
//            assertEquals(testSalon.id, retrieved1.salonId)
//            assertEquals(testSalon.id, retrieved2.salonId)
//            assertEquals(testSalon2.id, retrieved3.salonId)
//            assertEquals(testSalon2.id, retrieved4.salonId)
//
//            // Then - Find by salon should return all users for that salon
//            val salon1Settings = notificationSettingsRepository.findBySalonId(testSalon.id)
//            val salon2Settings = notificationSettingsRepository.findBySalonId(testSalon2.id)
//
//            assertEquals(2, salon1Settings.size)
//            assertEquals(2, salon2Settings.size)
//
//            assertTrue(salon1Settings.any { it.userId == testUserId })
//            assertTrue(salon1Settings.any { it.userId == testUserId2 })
//            assertTrue(salon2Settings.any { it.userId == testUserId })
//            assertTrue(salon2Settings.any { it.userId == testUserId2 })
//        }
//
//        @Test
//        fun `should handle existence checks correctly`() {
//            // Given
//            val settings = NotificationSettings.createDefault(testUserId, testSalon.id)
//
//            // When - Before saving
//            val existsBeforeSave = notificationSettingsRepository.existsByUserIdAndSalonId(testUserId, testSalon.id)
//
//            // Then
//            assertFalse(existsBeforeSave)
//
//            // When - After saving
//            notificationSettingsRepository.save(settings)
//            val existsAfterSave = notificationSettingsRepository.existsByUserIdAndSalonId(testUserId, testSalon.id)
//
//            // Then
//            assertTrue(existsAfterSave)
//
//            // When - Check for different user/salon combination
//            val existsForDifferentUser = notificationSettingsRepository.existsByUserIdAndSalonId(testUserId2, testSalon.id)
//            val existsForDifferentSalon = notificationSettingsRepository.existsByUserIdAndSalonId(testUserId, testSalon2.id)
//
//            // Then
//            assertFalse(existsForDifferentUser)
//            assertFalse(existsForDifferentSalon)
//        }
//
//        @Test
//        fun `should handle deletion correctly`() {
//            // Given
//            val settings1 = NotificationSettings.createDefault(testUserId, testSalon.id)
//            val settings2 = NotificationSettings.createDefault(testUserId2, testSalon.id)
//            val settings3 = NotificationSettings.createDefault(testUserId, testSalon2.id)
//
//            notificationSettingsRepository.save(settings1)
//            notificationSettingsRepository.save(settings2)
//            notificationSettingsRepository.save(settings3)
//
//            // When - Delete specific user-salon combination
//            notificationSettingsRepository.deleteByUserIdAndSalonId(testUserId, testSalon.id)
//
//            // Then - Only that specific combination should be deleted
//            val deleted = notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalon.id)
//            val stillExists1 = notificationSettingsRepository.findByUserIdAndSalonId(testUserId2, testSalon.id)
//            val stillExists2 = notificationSettingsRepository.findByUserIdAndSalonId(testUserId, testSalon2.id)
//
//            assertNull(deleted)
//            assertNotNull(stillExists1)
//            assertNotNull(stillExists2)
//        }
//    }
//
//    @Nested
//    @DisplayName("End-to-End Scenarios")
//    inner class EndToEndScenariosTests {
//
//        @Test
//        fun `should handle complete user workflow with multiple salons`() {
//            // Scenario: User joins two salons and configures different notification preferences
//
//            // Step 1: User gets default settings for first salon
//            val query1 = GetNotificationSettingsQuery(testUserId, testSalon.id)
//            val defaultSettings1 = notificationSettingsUseCase.getNotificationSettings(query1)
//
//            assertTrue(defaultSettings1.pushNotificationsEnabled)
//            assertEquals(SoundPreference.DEFAULT, defaultSettings1.soundPreference)
//
//            // Step 2: User joins second salon and gets default settings
//            val query2 = GetNotificationSettingsQuery(testUserId, testSalon2.id)
//            val defaultSettings2 = notificationSettingsUseCase.getNotificationSettings(query2)
//
//            assertTrue(defaultSettings2.pushNotificationsEnabled)
//            assertEquals(SoundPreference.DEFAULT, defaultSettings2.soundPreference)
//
//            // Step 3: User configures work-focused settings for salon 1 (business hours)
//            val workCommand = UpdateNotificationSettingsCommand(
//                userId = testUserId,
//                salonId = testSalon.id,
//                pushNotificationsEnabled = true,
//                soundPreference = SoundPreference.DEFAULT,
//                vibrationEnabled = true,
//                doNotDisturb = DoNotDisturbSettings(
//                    enabled = true,
//                    startTime = LocalTime.of(18, 0), // After work
//                    endTime = LocalTime.of(9, 0),   // Before work
//                    allowCritical = true
//                ),
//                notificationRules = NotificationRules(
//                    newAppointments = true,
//                    appointmentCancellations = true,
//                    paymentConfirmations = true,
//                    teamMemberUpdates = true,
//                    systemMaintenanceAlerts = false
//                )
//            )
//            val workSettings = notificationSettingsUseCase.updateNotificationSettings(workCommand)
//
//            // Step 4: User configures quiet settings for salon 2 (personal/evening salon)
//            val quietCommand = UpdateNotificationSettingsCommand(
//                userId = testUserId,
//                salonId = testSalon2.id,
//                pushNotificationsEnabled = true,
//                soundPreference = SoundPreference.SILENT,
//                vibrationEnabled = false,
//                doNotDisturb = DoNotDisturbSettings(
//                    enabled = true,
//                    startTime = LocalTime.of(22, 0), // Night time
//                    endTime = LocalTime.of(8, 0),   // Morning
//                    allowCritical = false
//                ),
//                notificationRules = NotificationRules(
//                    newAppointments = true,
//                    appointmentCancellations = true,
//                    paymentConfirmations = false,
//                    teamMemberUpdates = false,
//                    systemMaintenanceAlerts = false
//                )
//            )
//            val quietSettings = notificationSettingsUseCase.updateNotificationSettings(quietCommand)
//
//            // Step 5: Verify settings are correctly applied and isolated
//            val finalWorkSettings = notificationSettingsUseCase.getNotificationSettings(query1)
//            val finalQuietSettings = notificationSettingsUseCase.getNotificationSettings(query2)
//
//            // Work salon settings
//            assertTrue(finalWorkSettings.pushNotificationsEnabled)
//            assertEquals(SoundPreference.DEFAULT, finalWorkSettings.soundPreference)
//            assertTrue(finalWorkSettings.vibrationEnabled)
//            assertTrue(finalWorkSettings.doNotDisturb.enabled)
//            assertEquals(LocalTime.of(18, 0), finalWorkSettings.doNotDisturb.startTime)
//            assertTrue(finalWorkSettings.doNotDisturb.allowCritical)
//            assertTrue(finalWorkSettings.notificationRules.teamMemberUpdates)
//            assertFalse(finalWorkSettings.notificationRules.systemMaintenanceAlerts)
//
//            // Quiet salon settings
//            assertTrue(finalQuietSettings.pushNotificationsEnabled)
//            assertEquals(SoundPreference.SILENT, finalQuietSettings.soundPreference)
//            assertFalse(finalQuietSettings.vibrationEnabled)
//            assertTrue(finalQuietSettings.doNotDisturb.enabled)
//            assertEquals(LocalTime.of(22, 0), finalQuietSettings.doNotDisturb.startTime)
//            assertFalse(finalQuietSettings.doNotDisturb.allowCritical)
//            assertFalse(finalQuietSettings.notificationRules.teamMemberUpdates)
//            assertFalse(finalQuietSettings.notificationRules.paymentConfirmations)
//
//            // Verify isolation - settings are completely independent
//            assertNotEquals(finalWorkSettings.soundPreference, finalQuietSettings.soundPreference)
//            assertNotEquals(finalWorkSettings.vibrationEnabled, finalQuietSettings.vibrationEnabled)
//            assertNotEquals(finalWorkSettings.doNotDisturb.startTime, finalQuietSettings.doNotDisturb.startTime)
//            assertNotEquals(finalWorkSettings.doNotDisturb.allowCritical, finalQuietSettings.doNotDisturb.allowCritical)
//        }
//    }
//}
