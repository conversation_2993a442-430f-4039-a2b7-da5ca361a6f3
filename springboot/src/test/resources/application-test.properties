# Test database configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH;NON_KEYWORDS=KEY,VALUE;INIT=CREATE DOMAIN IF NOT EXISTS JSONB AS TEXT\\;CREATE DOMAIN IF NOT EXISTS TEXT_ARRAY AS TEXT
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Enable H2 console for debugging
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Hibernate configuration - Use manual schema initialization
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.properties.hibernate.hbm2ddl.halt_on_error=false
spring.jpa.defer-datasource-initialization=true

# SQL initialization settings
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.continue-on-error=true

# Disable Flyway for tests
spring.flyway.enabled=false

# Logging
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.org.hibernate.tool.schema=WARN
logging.level.org.springframework.test=WARN
logging.level.org.springframework.boot.test=WARN

# Security configuration for tests
jwt.secret=testSecretKey
jwt.expiration=86400000

# Firebase configuration for tests
firebase.project-id=animalia-de0f1
firebase.database-url=https://animalia-grooming-default-rtdb.europe-west1.firebasedatabase.app
firebase.storage-bucket=animalia-grooming.appspot.com

# Business rules configuration for tests
animalia.business-rules.validation.max-name-length=255
animalia.business-rules.validation.max-notes-length=2000
animalia.business-rules.validation.max-description-length=1000
animalia.business-rules.validation.max-address-length=1000
animalia.business-rules.validation.max-city-length=255
animalia.business-rules.validation.max-breed-length=255
animalia.business-rules.validation.max-color-length=100
animalia.business-rules.validation.max-medical-conditions-length=2000

animalia.business-rules.appointment.minimum-advance-hours=2
animalia.business-rules.appointment.maximum-advance-days=90
animalia.business-rules.appointment.default-duration-minutes=60
animalia.business-rules.appointment.max-services-per-appointment=10
animalia.business-rules.appointment.min-duration-minutes=15
animalia.business-rules.appointment.max-duration-minutes=480

animalia.business-rules.salon.max-staff-members=20
animalia.business-rules.salon.max-services-per-salon=100
animalia.business-rules.salon.max-clients-per-salon=1000
