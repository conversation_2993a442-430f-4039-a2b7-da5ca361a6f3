# Development Environment Variables
# This file contains development-specific environment variables

# Application Profile
SPRING_PROFILES_ACTIVE=dev

# Server Configuration
PORT=8080

# Database Configuration - Development PostgreSQL
DATABASE_URL=*********************************************
DATABASE_USERNAME=animalia_dev_user
DATABASE_PASSWORD=dev_password_123

# Database Connection Pool Settings
DB_POOL_SIZE=5
DB_POOL_MIN_IDLE=1
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000
DB_LEAK_DETECTION=60000

# JPA/Hibernate Configuration
SPRING_JPA_HIBERNATE_DDL_AUTO=create-drop
SPRING_JPA_PROPERTIES_HIBERNATE_HBM2DDL_AUTO=create-drop
SPRING_JPA_SHOW_SQL=true
SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL=true

# Security Configuration
JWT_SECRET=devSecretKeyForJwtTokenGenerationAndValidationWithExtraLengthToMeetHS512RequirementsInDevelopment
JWT_EXPIRATION=86400000

# Firebase Configuration
FIREBASE_PROJECT_ID=animalia-de0f1
FIREBASE_DATABASE_URL=https://animalia-grooming-default-rtdb.europe-west1.firebasedatabase.app
FIREBASE_STORAGE_BUCKET=animalia-grooming.appspot.com

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:4200

# Cloudinary Configuration (Development)
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=

# Email Configuration (Development - use MailHog or similar)
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=
MAIL_PASSWORD=

# Notification Configuration
NOTIFICATIONS_SMS_ENABLED=false
NOTIFICATIONS_PUSH_ENABLED=true
NOTIFICATIONS_ASYNC_ENABLED=true

# Phone Validation
PHONE_VALIDATION_COUNTRY_CODE=+40
PHONE_VALIDATION_MIN_LENGTH=10
PHONE_VALIDATION_MAX_LENGTH=10

# Development Flags
SPRING_DEVTOOLS_RESTART_ENABLED=true
SPRING_DEVTOOLS_LIVERELOAD_ENABLED=true
SPRING_DATA_REST_ENABLED=true

# Logging Configuration
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB=DEBUG
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_SECURITY=DEBUG
LOGGING_LEVEL_ORG_HIBERNATE_SQL=DEBUG
LOGGING_LEVEL_RO_ANIMALIAPROGRAMARI=DEBUG

# Actuator Configuration
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=*
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=always
