# Delete Account Feature Implementation

## Overview

Implementarea funcționalității "Delete Account" pentru aplicația Animalia Programări, care permite utilizatorilor să își șteargă permanent contul și toate datele asociate.

## Funcționalitate

### Frontend Integration
Butonul "Delete Account" a fost implementat în frontend cu următoarele caracteristici:
- Poziționat sub butonul "Deconectare"
- Cere confirmarea utilizatorului prin scrierea textului "confirm"
- Include opțiunea de anulare
- Afișează avertisment în engleză că toate datele vor fi șterse

### Backend Implementation

#### 1. Command Pattern
**Fișier**: `src/main/kotlin/ro/animaliaprogramari/animalia/application/command/AuthenticationCommands.kt`

```kotlin
data class DeleteUserAccountCommand(
    val userId: UserId,
    val confirmationText: String
) {
    init {
        require(confirmationText == "confirm") { 
            "Confirmation text must be exactly 'confirm' to proceed with account deletion" 
        }
    }
}
```

#### 2. Use Case Interface
**Fișier**: `src/main/kotlin/ro/animaliaprogramari/animalia/application/port/inbound/UserManagementUseCase.kt`

```kotlin
fun deleteUserAccount(command: DeleteUserAccountCommand): Boolean
```

#### 3. Use Case Implementation
**Fișier**: `src/main/kotlin/ro/animaliaprogramari/animalia/application/usecase/UserManagementUseCaseImpl.kt`

Logica de ștergere include:
- Validarea existenței utilizatorului
- Ștergerea tuturor programărilor unde utilizatorul este staff (hard delete)
- Dezactivarea asociațiilor staff (soft delete)
- Dezactivarea înregistrărilor client (soft delete)
- Ștergerea finală a contului utilizator (hard delete)

#### 4. REST Endpoint
**Fișier**: `src/main/kotlin/ro/animaliaprogramari/animalia/adapter/inbound/rest/AuthenticationController.kt`

```kotlin
@DeleteMapping("/delete-account")
fun deleteAccount(@RequestBody request: DeleteAccountRequest): ResponseEntity<ApiResponse<Map<String, Any>>>
```

#### 5. DTO
**Fișier**: `src/main/kotlin/ro/animaliaprogramari/animalia/adapter/inbound/rest/dto/AuthenticationDtos.kt`

```kotlin
data class DeleteAccountRequest(
    @JsonProperty("confirmationText")
    val confirmationText: String
)
```

## Business Logic

### Validări
1. **Autentificare**: Utilizatorul trebuie să fie autentificat
2. **Confirmare**: Textul de confirmare trebuie să fie exact "confirm"

### Cleanup Process
1. **Appointments**: Ștergere completă (hard delete) a tuturor programărilor unde utilizatorul este staff
2. **Staff Associations**: Dezactivare (soft delete) pentru toate asociațiile staff
3. **Client Records**: Dezactivare (soft delete) pentru înregistrările client
4. **User Account**: Ștergere completă (hard delete) din baza de date

### Error Handling
- `EntityNotFoundException`: Utilizatorul nu există
- `400 Bad Request`: Text de confirmare invalid
- `401 Unauthorized`: Utilizator neautentificat
- `500 Internal Server Error`: Eroare la ștergere

## API Usage

### Request
```http
DELETE /auth/delete-account
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "confirmationText": "confirm"
}
```

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "message": "Account deleted successfully. All your data has been permanently removed.",
    "timestamp": "2024-01-15T10:30:00",
    "userId": "user-123"
  }
}
```

### Error Response (404 - User Not Found)
```json
{
  "success": false,
  "error": "User account not found"
}
```

## Configuration Updates

### Dependency Injection
**Fișier**: `src/main/kotlin/ro/animaliaprogramari/animalia/config/HexagonalArchitectureConfig.kt`

Actualizat `userManagementUseCase` bean pentru a include dependințele necesare:
- `staffRepository`
- `clientRepository` 
- `appointmentRepository`

## Testing

### Unit Tests
1. **DeleteUserAccountUseCaseTest**: Testează logica de business
2. **UserManagementUseCaseTest**: Actualizat pentru noile dependințe

### Test Scenarios
- ✅ Ștergere cu succes când nu există asociații staff
- ✅ Ștergere cu succes și a programărilor când utilizatorul are programări active
- ✅ Eroare când utilizatorul nu există
- ✅ Dezactivarea corectă a datelor asociate (staff, clienți)
- ✅ Ștergerea completă a programărilor
- ✅ Validarea textului de confirmare

## Security Considerations

1. **Authentication Required**: Endpoint-ul necesită autentificare JWT
2. **Self-Service Only**: Utilizatorii pot șterge doar propriul cont
3. **Confirmation Required**: Textul "confirm" previne ștergerea accidentală
4. **Audit Trail**: Operațiunea este logată pentru urmărire

## Future Enhancements

1. **Grace Period**: Implementarea unei perioade de grație înainte de ștergerea finală
2. **Data Export**: Opțiunea de export a datelor înainte de ștergere
3. **Admin Override**: Posibilitatea pentru administratori să șteargă conturi cu programări active
4. **Notification**: Trimiterea de notificări către clienții afectați

## Dependencies

- Spring Boot 3.x
- Spring Security
- JPA/Hibernate
- Kotlin
- MockK (pentru teste)

## Deployment Notes

- Funcționalitatea este disponibilă imediat după deployment
- Nu necesită migrări de bază de date
- Compatibilă cu configurația existentă de securitate
