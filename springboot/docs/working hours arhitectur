graph TB
    subgraph "Domain Layer"
        WH[WorkingHours<br/>Unified Value Object]
        DS[DaySchedule<br/>Value Object]
        WHS[WorkingHoursSettings<br/>Aggregate Root]
        SWH[StaffWorkingHours<br/>Entity]

        WHS --> WH
        WH --> DS
        SWH --> WH
    end

    subgraph "Application Layer"
        WHM[WorkingHoursManagement<br/>Use Case]
        CS[ConflictService<br/>Domain Service]

        WHM --> WHS
        CS --> WHS
        CS --> SWH
    end

    subgraph "Infrastructure Layer"
        WHR[WorkingHoursRepository<br/>Unified]
        DB[(Database<br/>Single Schema)]

        WHR --> DB
        WHM --> WHR
    end

    subgraph "API Layer"
        SWC[SalonWorkingHoursController]
        SWHC[StaffWorkingHoursController]

        SWC --> WHM
        SWHC --> WHM
    end

    Staff --> SWH
    Salon --> WHS