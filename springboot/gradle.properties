# Gradle performance optimizations
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=false
org.gradle.daemon=false
org.gradle.jvmargs=-Xmx3g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=100

# Kotlin compiler optimizations
kotlin.incremental=true
kotlin.incremental.useClasspathSnapshot=true
kotlin.build.report.output=file

# JVM optimizations for build
org.gradle.workers.max=4

# Disable configuration cache for now (causing issues)
org.gradle.unsafe.configuration-cache=false
