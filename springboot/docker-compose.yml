services:
  # Development PostgreSQL Database
  postgres-dev:
    image: postgres:16.2
    container_name: animalia-postgres-dev
    environment:
      POSTGRES_DB: animalia_dev
      POSTGRES_USER: animalia_dev_user
      POSTGRES_PASSWORD: dev_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-dev-db.sql:/docker-entrypoint-initdb.d/init-dev-db.sql
    networks:
      - animalia-dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U animalia_dev_user -d animalia_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for development (optional, for caching/sessions)
  redis-dev:
    image: redis:7.2.4
    container_name: animalia-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - animalia-dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Development Application (optional - can be run from IDE)
#  app-dev:
#    build:
#      context: .
#      dockerfile: Dockerfile
#    container_name: animalia-app-dev
#    ports:
#      - "8080:8080"
#    environment:
#      - SPRING_PROFILES_ACTIVE=dev
#      - SPRING_DATASOURCE_URL=************************************************
#      - SPRING_DATASOURCE_USERNAME=animalia_dev_user
#      - SPRING_DATASOURCE_PASSWORD=dev_password_123
#    depends_on:
#      postgres-dev:
#        condition: service_healthy
#      redis-dev:
#        condition: service_healthy
#    networks:
#      - animalia-dev
#    restart: unless-stopped
#    profiles:
#      - full-stack  # Only start when explicitly requested

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  animalia-dev:
    driver: bridge
