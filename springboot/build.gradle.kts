plugins {
    kotlin("jvm") version "1.9.25"
    kotlin("plugin.spring") version "1.9.25"
    id("org.springframework.boot") version "3.5.0"
    id("io.spring.dependency-management") version "1.1.7"
    id("jacoco")
    id("org.jlleitschuh.gradle.ktlint") version "12.1.0"
}

group = "ro.animalia-programari"
version = "0.0.1-SNAPSHOT"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    mavenCentral()
}

extra["springModulithVersion"] = "1.4.0"

dependencies {
    implementation("org.springframework.boot:spring-boot-starter-data-rest")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-web-services")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.springframework.modulith:spring-modulith-starter-core")
    implementation("org.postgresql:postgresql")
    implementation("com.google.firebase:firebase-admin:9.2.0")
    implementation("com.cloudinary:cloudinary-http44:1.39.0")
    implementation("io.jsonwebtoken:jjwt-api:0.11.5")
    implementation("io.swagger.core.v3:swagger-annotations:2.2.29")
    runtimeOnly("io.jsonwebtoken:jjwt-impl:0.11.5")
    runtimeOnly("io.jsonwebtoken:jjwt-jackson:0.11.5")

    // Notification dependencies
    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation("com.github.ben-manes.caffeine:caffeine")
    implementation("org.springframework:spring-webflux") // For WebClient HTTP calls to SMSO API
    implementation("com.google.firebase:firebase-admin:9.2.0") // Firebase Admin SDK for FCM

    // Monitoring dependencies
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("io.micrometer:micrometer-registry-prometheus")
    implementation("io.micrometer:micrometer-core")
    compileOnly("org.projectlombok:lombok")
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    developmentOnly("org.springframework.boot:spring-boot-docker-compose")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    annotationProcessor("org.projectlombok:lombok")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("io.projectreactor:reactor-test")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test")
    testImplementation("org.springframework.modulith:spring-modulith-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("io.mockk:mockk:1.13.9")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.2.1")
    testImplementation("com.ninja-squad:springmockk:4.0.2")
    testImplementation("com.h2database:h2")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}

dependencyManagement {
    imports {
        mavenBom("org.springframework.modulith:spring-modulith-bom:${property("springModulithVersion")}")
    }
}

kotlin {
    compilerOptions {
        freeCompilerArgs.addAll("-Xjsr305=strict")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
    finalizedBy(tasks.jacocoTestReport) // run coverage report after tests

    // Performance optimizations for CI
    maxParallelForks = if (System.getenv("CI") == "true") 2 else Runtime.getRuntime().availableProcessors()
    forkEvery = 25  // Reduced further for faster startup

    // JVM optimizations for tests - reduced memory for CI
    jvmArgs(
        "-XX:+UseG1GC",
        "-XX:MaxGCPauseMillis=100",
        "-XX:+UseStringDeduplication",
        "-Xms256m",
        "-Xmx1g",
        "-XX:TieredStopAtLevel=1"  // Faster JIT compilation
    )

    // Fail fast on first test failure in CI
    if (System.getenv("CI") == "true") {
        failFast = true

        // Skip slow integration tests in CI
        val excludePatterns = System.getProperty("test.exclude.patterns")
        if (!excludePatterns.isNullOrEmpty()) {
            excludePatterns.split(",").forEach { pattern ->
                exclude(pattern.trim())
            }
        }
    }

    testLogging {
        events("failed") // Only log failures to reduce output
        showStandardStreams = false
        showExceptions = true
        showCauses = true
        showStackTraces = false
    }
}

jacoco {
    toolVersion = "0.8.11"
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)
    reports {
        xml.required.set(true)
        html.required.set(true)
    }
}

// Ktlint configuration
configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
    version.set("1.0.1")
    debug.set(false)
    verbose.set(true)
    android.set(false)
    outputToConsole.set(true)
    outputColorName.set("RED")
    ignoreFailures.set(true) // Don't fail build on lint issues
    enableExperimentalRules.set(false)
    disabledRules.set(
        setOf(
            "no-wildcard-imports",
            "discouraged-comment-location",
            "max-line-length",
            "multiline-expression-wrapping",
            "trailing-comma-on-call-site",
            "trailing-comma-on-declaration-site",
            "chain-method-continuation",
            "argument-list-wrapping",
            "function-signature",
            "no-empty-file",
        ),
    )
    filter {
        exclude("**/generated/**")
        include("**/kotlin/**")
    }
}
