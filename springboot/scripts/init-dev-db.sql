-- Development Database Initialization Script
-- This script sets up the development database with proper configuration

-- Set timezone
SET timezone = 'Europe/Bucharest';

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create development-specific schemas if needed
-- (Hibernate will create the main tables)

-- Insert development-specific configuration data
-- This can include test users, sample data, etc.

-- Development logging function
CREATE OR REPLACE FUNCTION log_dev_message(message TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'DEV: %', message;
END;
$$ LANGUAGE plpgsql;

-- Log initialization
SELECT log_dev_message('Development database initialized successfully');

-- Create a development admin user (this will be handled by the application)
-- But we can prepare the database for it

-- Development-specific indexes for better performance during development
-- (These will be created by Hibernate, but we can add custom ones here)

-- Development-specific functions or procedures can be added here

-- Log completion
SELECT log_dev_message('Development database setup completed');
