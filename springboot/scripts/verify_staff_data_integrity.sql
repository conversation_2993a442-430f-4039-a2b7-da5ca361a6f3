-- Comprehensive verification script for staff data integrity
-- Run this script to identify and analyze data integrity issues

-- ========================================
-- 1. CHECK STAFF WORKING HOURS DATA INTEGRITY
-- ========================================

-- Find working hours records where staff_id doesn't match any staff.id
SELECT 
    'ORPHANED_WORKING_HOURS' as issue_type,
    swhs.id as working_hours_id,
    swhs.staff_id as problematic_staff_id,
    swhs.salon_id,
    'No staff record found with this ID' as description
FROM staff_working_hours_settings swhs
LEFT JOIN staff s ON swhs.staff_id = s.id AND swhs.salon_id = s.salon_id
WHERE s.id IS NULL;

-- Find working hours records where staff_id matches user_id instead of staff.id
SELECT 
    'INCORRECT_STAFF_ID_REFERENCE' as issue_type,
    swhs.id as working_hours_id,
    swhs.staff_id as incorrect_staff_id,
    s.id as correct_staff_id,
    s.user_id,
    u.name as user_name,
    swhs.salon_id,
    'staff_id references user_id instead of staff.id' as description
FROM staff_working_hours_settings swhs
JOIN staff s ON swhs.staff_id = s.user_id AND swhs.salon_id = s.salon_id
JOIN users u ON s.user_id = u.id;

-- ========================================
-- 2. CHECK STAFF ENTITY CONSISTENCY
-- ========================================

-- Find staff records with invalid user references
SELECT 
    'INVALID_USER_REFERENCE' as issue_type,
    s.id as staff_id,
    s.user_id as invalid_user_id,
    s.salon_id,
    'Staff references non-existent user' as description
FROM staff s
LEFT JOIN users u ON s.user_id = u.id
WHERE u.id IS NULL;

-- Find duplicate staff records (same user in same salon)
SELECT 
    'DUPLICATE_STAFF_RECORDS' as issue_type,
    s1.user_id,
    s1.salon_id,
    COUNT(*) as duplicate_count,
    STRING_AGG(s1.id, ', ') as staff_ids,
    'Multiple staff records for same user in same salon' as description
FROM staff s1
GROUP BY s1.user_id, s1.salon_id
HAVING COUNT(*) > 1;

-- ========================================
-- 3. CHECK APPOINTMENT DATA INTEGRITY
-- ========================================

-- Find appointments with invalid staff references
SELECT 
    'APPOINTMENT_INVALID_STAFF' as issue_type,
    a.id as appointment_id,
    a.staff_id as invalid_staff_id,
    a.salon_id,
    'Appointment references non-existent staff' as description
FROM appointments a
LEFT JOIN staff s ON a.staff_id = s.id
WHERE s.id IS NULL;

-- Find appointments where staff_id might be user_id
SELECT 
    'APPOINTMENT_STAFF_ID_CONFUSION' as issue_type,
    a.id as appointment_id,
    a.staff_id as possible_user_id,
    s.id as correct_staff_id,
    s.user_id,
    a.salon_id,
    'Appointment staff_id might be user_id instead of staff.id' as description
FROM appointments a
JOIN staff s ON a.staff_id = s.user_id AND a.salon_id = s.salon_id
LEFT JOIN staff s2 ON a.staff_id = s2.id
WHERE s2.id IS NULL;

-- ========================================
-- 4. CHECK BLOCK TIME DATA INTEGRITY
-- ========================================

-- Find block times with invalid staff references
SELECT 
    'BLOCK_TIME_INVALID_STAFF' as issue_type,
    bt.id as block_time_id,
    bt.staff_ids as invalid_staff_ids,
    bt.salon_id,
    'Block time references non-existent staff' as description
FROM block_times bt
WHERE EXISTS (
    SELECT 1 
    FROM unnest(string_to_array(bt.staff_ids, ',')) as staff_id
    WHERE staff_id NOT IN (SELECT id FROM staff WHERE salon_id = bt.salon_id)
);

-- ========================================
-- 5. SUMMARY REPORT
-- ========================================

-- Count of each type of issue
WITH issue_counts AS (
    -- Working hours issues
    SELECT 'ORPHANED_WORKING_HOURS' as issue_type, COUNT(*) as count
    FROM staff_working_hours_settings swhs
    LEFT JOIN staff s ON swhs.staff_id = s.id AND swhs.salon_id = s.salon_id
    WHERE s.id IS NULL
    
    UNION ALL
    
    SELECT 'INCORRECT_STAFF_ID_REFERENCE' as issue_type, COUNT(*) as count
    FROM staff_working_hours_settings swhs
    JOIN staff s ON swhs.staff_id = s.user_id AND swhs.salon_id = s.salon_id
    
    UNION ALL
    
    -- Staff issues
    SELECT 'INVALID_USER_REFERENCE' as issue_type, COUNT(*) as count
    FROM staff s
    LEFT JOIN users u ON s.user_id = u.id
    WHERE u.id IS NULL
    
    UNION ALL
    
    SELECT 'DUPLICATE_STAFF_RECORDS' as issue_type, COUNT(*) as count
    FROM (
        SELECT s1.user_id, s1.salon_id
        FROM staff s1
        GROUP BY s1.user_id, s1.salon_id
        HAVING COUNT(*) > 1
    ) duplicates
    
    UNION ALL
    
    -- Appointment issues
    SELECT 'APPOINTMENT_INVALID_STAFF' as issue_type, COUNT(*) as count
    FROM appointments a
    LEFT JOIN staff s ON a.staff_id = s.id
    WHERE s.id IS NULL
    
    UNION ALL
    
    SELECT 'APPOINTMENT_STAFF_ID_CONFUSION' as issue_type, COUNT(*) as count
    FROM appointments a
    JOIN staff s ON a.staff_id = s.user_id AND a.salon_id = s.salon_id
    LEFT JOIN staff s2 ON a.staff_id = s2.id
    WHERE s2.id IS NULL
)
SELECT 
    issue_type,
    count,
    CASE 
        WHEN count = 0 THEN '✅ OK'
        WHEN count > 0 THEN '❌ NEEDS ATTENTION'
    END as status
FROM issue_counts
ORDER BY count DESC;

-- ========================================
-- 6. SPECIFIC CHECK FOR THE REPORTED ISSUE
-- ========================================

-- Check for the specific ID mentioned in the issue: 55d78abe-7441-4a4e-a2bc-95fb09bf1c9c
SELECT 
    'SPECIFIC_ID_CHECK' as check_type,
    '55d78abe-7441-4a4e-a2bc-95fb09bf1c9c' as problematic_id,
    CASE 
        WHEN EXISTS (SELECT 1 FROM staff WHERE id = '55d78abe-7441-4a4e-a2bc-95fb09bf1c9c') 
        THEN 'Found in staff.id'
        WHEN EXISTS (SELECT 1 FROM staff WHERE user_id = '55d78abe-7441-4a4e-a2bc-95fb09bf1c9c')
        THEN 'Found in staff.user_id (THIS IS THE PROBLEM)'
        WHEN EXISTS (SELECT 1 FROM users WHERE id = '55d78abe-7441-4a4e-a2bc-95fb09bf1c9c')
        THEN 'Found in users.id'
        ELSE 'Not found in any table'
    END as location,
    CASE 
        WHEN EXISTS (SELECT 1 FROM staff WHERE user_id = '55d78abe-7441-4a4e-a2bc-95fb09bf1c9c')
        THEN (SELECT id FROM staff WHERE user_id = '55d78abe-7441-4a4e-a2bc-95fb09bf1c9c' LIMIT 1)
        ELSE NULL
    END as correct_staff_id;

-- Show the actual staff record if the ID is found as user_id
SELECT 
    s.id as correct_staff_id,
    s.user_id as problematic_user_id,
    s.salon_id,
    s.role,
    s.is_active,
    u.name as user_name,
    u.phone as user_phone
FROM staff s
JOIN users u ON s.user_id = u.id
WHERE s.user_id = '55d78abe-7441-4a4e-a2bc-95fb09bf1c9c';
