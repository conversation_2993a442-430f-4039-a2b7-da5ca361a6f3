-- Test Database Initialization Script
-- This script sets up the test database with proper configuration

-- Set timezone
SET timezone = 'Europe/Bucharest';

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create test-specific schemas if needed
-- (Hibernate will create the main tables)

-- Test logging function
CREATE OR REPLACE FUNCTION log_test_message(message TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'TEST: %', message;
END;
$$ LANGUAGE plpgsql;

-- Log initialization
SELECT log_test_message('Test database initialized successfully');

-- Test-specific configuration
-- This can include test data, test users, etc.

-- Test-specific indexes for performance testing
-- (These will be created by Hibernate, but we can add custom ones here)

-- Test-specific functions or procedures for testing
CREATE OR REPLACE FUNCTION reset_test_data()
RETURNS VOID AS $$
BEGIN
    -- This function can be used to reset test data between test runs
    -- Implementation will depend on your specific needs
    RAISE NOTICE 'Test data reset function created (implementation pending)';
END;
$$ LANGUAGE plpgsql;

-- Log completion
SELECT log_test_message('Test database setup completed');
