#!/bin/bash

# Optimized CI Test Script for Spring Boot Animalia Backend
# This script provides fast, reliable testing for CI/CD environments

set -e  # Exit on any error

# Colors for clean output
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Configuration
readonly SCRIPT_NAME="Spring Boot CI Test Suite"
readonly MAX_WORKERS=4
readonly TEST_TIMEOUT_MINUTES=10
readonly COVERAGE_THRESHOLD=80

# Test categories
readonly UNIT_TEST_PATTERNS="**/*Test.kt,**/*Tests.kt"
readonly INTEGRATION_TEST_PATTERNS="**/*IntegrationTest.kt,**/*Integration*.kt,**/*IT.kt"
readonly PERFORMANCE_TEST_PATTERNS="**/*PerformanceTest.kt,**/*Performance*.kt"

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_section() {
    echo ""
    echo -e "${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Test execution failed with exit code $exit_code"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Main execution
main() {
    local start_time=$(date +%s)
    local test_type="${1:-all}"
    
    log_section "🧪 $SCRIPT_NAME"
    log_info "Starting optimized CI test execution..."
    log_info "Test type: $test_type"
    log_info "Configuration: Workers=$MAX_WORKERS, Timeout=${TEST_TIMEOUT_MINUTES}m"
    
    # Step 1: Environment validation
    validate_environment
    
    # Step 2: Setup Gradle
    setup_gradle
    
    # Step 3: Code quality checks
    run_code_quality_checks
    
    # Step 4: Execute tests based on type
    case "$test_type" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "fast")
            run_fast_tests
            ;;
        "all"|*)
            run_all_tests
            ;;
    esac
    
    # Step 5: Generate reports
    generate_reports
    
    # Step 6: Summary
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_section "📊 Test Execution Summary"
    log_success "All tests completed successfully!"
    log_info "Total execution time: ${duration}s ($(($duration / 60))m $(($duration % 60))s)"
    log_info "Test artifacts available in build/reports/ directory"
}

validate_environment() {
    log_section "🔍 Environment Validation"
    
    # Check Java installation
    if ! command -v java &> /dev/null; then
        log_error "Java not found. Please install JDK 21."
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | head -n 1)
    log_info "Java version: $java_version"
    
    # Check if we're in the correct directory
    if [ ! -f "build.gradle.kts" ]; then
        log_error "build.gradle.kts not found. Please run from Spring Boot project root."
        exit 1
    fi
    
    # Check Gradle wrapper
    if [ ! -f "gradlew" ]; then
        log_error "Gradle wrapper not found."
        exit 1
    fi
    
    # Make gradlew executable
    chmod +x ./gradlew
    
    # Count test files
    local test_count=$(find src/test -name "*Test.kt" -o -name "*Tests.kt" | wc -l)
    log_info "Found $test_count test files"
    
    log_success "Environment validation completed"
}

setup_gradle() {
    log_section "🔧 Gradle Setup"
    
    # Set CI environment variables
    export CI=true
    export GRADLE_OPTS="-Dorg.gradle.daemon=false -Dorg.gradle.parallel=true -Dorg.gradle.workers.max=$MAX_WORKERS -Xmx3g"
    
    log_info "Validating Gradle setup..."
    if ./gradlew --version --no-daemon --quiet; then
        log_success "Gradle setup validated"
    else
        log_error "Gradle setup failed"
        exit 1
    fi
    
    # Clean previous build artifacts
    log_info "Cleaning previous build artifacts..."
    ./gradlew clean --no-daemon --quiet
    
    log_success "Gradle setup completed"
}

run_code_quality_checks() {
    log_section "🔍 Code Quality Checks"
    
    log_info "Running Kotlin lint check..."
    if ./gradlew ktlintCheck --no-daemon --parallel --quiet --continue; then
        log_success "Kotlin lint check passed"
    else
        log_warning "Kotlin lint check found issues (continuing with tests)"
        # Don't fail on lint issues in CI, just warn
    fi
}

run_fast_tests() {
    log_section "⚡ Fast Tests (Unit Tests Only)"
    
    log_info "Running fast unit tests..."
    
    ./gradlew test --no-daemon --parallel --max-workers=$MAX_WORKERS --build-cache \
        -Dtest.exclude.patterns="$INTEGRATION_TEST_PATTERNS,$PERFORMANCE_TEST_PATTERNS" \
        --fail-fast \
        --continue
    
    log_success "Fast tests completed"
}

run_unit_tests() {
    log_section "🧪 Unit Tests"
    
    log_info "Running unit tests..."
    
    ./gradlew test --no-daemon --parallel --max-workers=$MAX_WORKERS --build-cache \
        -Dtest.exclude.patterns="$INTEGRATION_TEST_PATTERNS,$PERFORMANCE_TEST_PATTERNS" \
        --continue
    
    log_success "Unit tests completed"
}

run_integration_tests() {
    log_section "🔗 Integration Tests"
    
    log_info "Running integration tests..."
    
    # Run integration tests with reduced parallelism for stability
    ./gradlew test --no-daemon --parallel --max-workers=2 --build-cache \
        --tests="*IntegrationTest" --tests="*Integration*" --tests="*IT" \
        --continue
    
    log_success "Integration tests completed"
}

run_performance_tests() {
    log_section "⚡ Performance Tests"
    
    log_info "Running performance tests..."
    
    # Run performance tests with single worker for accurate measurements
    ./gradlew test --no-daemon --max-workers=1 --build-cache \
        --tests="*PerformanceTest*" --tests="*Performance*" \
        --continue
    
    log_success "Performance tests completed"
}

run_all_tests() {
    log_section "🎯 All Tests"
    
    log_info "Running comprehensive test suite..."
    
    # Run unit tests first (fast feedback)
    run_unit_tests
    
    # Run integration tests
    run_integration_tests
    
    # Run performance tests only on main branch or manual trigger
    if [ "${GITHUB_REF:-}" = "refs/heads/main" ] || [ "${RUN_PERFORMANCE_TESTS:-}" = "true" ]; then
        run_performance_tests
    else
        log_info "Skipping performance tests (not on main branch)"
    fi
    
    log_success "All tests completed"
}

generate_reports() {
    log_section "📊 Report Generation"
    
    # Generate JaCoCo coverage report
    log_info "Generating coverage report..."
    ./gradlew jacocoTestReport --no-daemon --quiet
    
    # Check if coverage report was generated
    if [ -f "build/reports/jacoco/test/html/index.html" ]; then
        log_success "Coverage report generated: build/reports/jacoco/test/html/index.html"
        
        # Extract coverage percentage if possible
        if [ -f "build/reports/jacoco/test/jacocoTestReport.xml" ]; then
            local coverage_line=$(grep -o 'line-rate="[0-9.]*"' build/reports/jacoco/test/jacocoTestReport.xml | head -1)
            if [ ! -z "$coverage_line" ]; then
                local coverage_decimal=$(echo "$coverage_line" | grep -o '[0-9.]*')
                local coverage_percentage=$(echo "$coverage_decimal * 100" | bc -l | cut -d. -f1)
                
                log_info "Test coverage: ${coverage_percentage}%"
                
                # Check coverage threshold
                if [ "$coverage_percentage" -ge "$COVERAGE_THRESHOLD" ]; then
                    log_success "Coverage threshold met (${coverage_percentage}% >= ${COVERAGE_THRESHOLD}%)"
                else
                    log_warning "Coverage below threshold (${coverage_percentage}% < ${COVERAGE_THRESHOLD}%)"
                fi
            fi
        fi
    else
        log_warning "Coverage report not generated"
    fi
    
    # Generate test summary
    generate_test_summary
}

generate_test_summary() {
    local summary_file="build/reports/test-summary.txt"
    mkdir -p build/reports
    
    # Count test results
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    local skipped_tests=0
    
    if [ -d "build/test-results" ]; then
        # Parse test results from XML files
        for xml_file in build/test-results/test/TEST-*.xml; do
            if [ -f "$xml_file" ]; then
                local file_tests=$(grep -o 'tests="[0-9]*"' "$xml_file" | grep -o '[0-9]*' || echo "0")
                local file_failures=$(grep -o 'failures="[0-9]*"' "$xml_file" | grep -o '[0-9]*' || echo "0")
                local file_errors=$(grep -o 'errors="[0-9]*"' "$xml_file" | grep -o '[0-9]*' || echo "0")
                local file_skipped=$(grep -o 'skipped="[0-9]*"' "$xml_file" | grep -o '[0-9]*' || echo "0")
                
                total_tests=$((total_tests + file_tests))
                failed_tests=$((failed_tests + file_failures + file_errors))
                skipped_tests=$((skipped_tests + file_skipped))
            fi
        done
        passed_tests=$((total_tests - failed_tests - skipped_tests))
    fi
    
    cat > "$summary_file" << EOF
Spring Boot Test Execution Summary
==================================
Execution Date: $(date)
Script Version: CI Optimized v1.0

Test Configuration:
- Max Workers: $MAX_WORKERS
- Test Timeout: ${TEST_TIMEOUT_MINUTES}m
- Coverage Threshold: ${COVERAGE_THRESHOLD}%

Test Results:
- Total Tests: $total_tests
- Passed: $passed_tests
- Failed: $failed_tests
- Skipped: $skipped_tests

Test Categories:
- Unit Tests: Included
- Integration Tests: Included
- Performance Tests: Conditional

Build Information:
- Gradle Version: $(./gradlew --version --quiet | grep "Gradle" | head -1)
- Java Version: $(java -version 2>&1 | head -1)
- Kotlin Version: $(./gradlew --version --quiet | grep "Kotlin" | head -1)

Artifacts Generated:
- Test Results: build/test-results/
- Coverage Report: build/reports/jacoco/test/html/index.html
- Test Summary: build/reports/test-summary.txt

Status: SUCCESS
EOF
    
    log_success "Test summary generated: $summary_file"
}

# Show usage if help requested
if [ "${1:-}" = "--help" ] || [ "${1:-}" = "-h" ]; then
    echo "Usage: $0 [test-type]"
    echo ""
    echo "Test types:"
    echo "  all          - Run all tests (default)"
    echo "  unit         - Run unit tests only"
    echo "  integration  - Run integration tests only"
    echo "  performance  - Run performance tests only"
    echo "  fast         - Run fast tests only (unit tests with fail-fast)"
    echo ""
    echo "Environment variables:"
    echo "  RUN_PERFORMANCE_TESTS=true  - Force performance tests to run"
    echo ""
    exit 0
fi

# Execute main function
main "$@"
