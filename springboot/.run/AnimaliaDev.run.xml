<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="AnimaliaDev" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ALTERNATIVE_JRE_PATH" value="/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <envs>
      <env name="DEV_SLOW_NOTIFICATIONS" value="true" />
    </envs>
    <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
    <module name="animalia-programari-backend.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="ro.animaliaprogramari.animalia.AnimaliaProgramariBackendApplication" />
    <option name="VM_PARAMETERS" value="-Djava.net.preferIPv4Stack=true -Dspring.profiles.active=dev" />
    <method v="2">
      <option name="Make" enabled="true" />
      <option name="ToolBeforeRunTask" enabled="true" actionId="Tool_External Tools_start-dev" />
    </method>
  </configuration>
</component>