version: '3.8'

services:
  # Test PostgreSQL Database
  postgres-test:
    image: postgres:16.2
    container_name: animalia-postgres-test
    environment:
      POSTGRES_DB: animalia_test
      POSTGRES_USER: animalia_test_user
      POSTGRES_PASSWORD: test_password_456
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/init-test-db.sql:/docker-entrypoint-initdb.d/init-test-db.sql
    networks:
      - animalia-test
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U animalia_test_user -d animalia_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Test
  redis-test:
    image: redis:7.2.4
    container_name: animalia-redis-test
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_test_data:/data
    networks:
      - animalia-test
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Application
  app-test:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: animalia-app-test
    ports:
      - "8081:8080"  # Different port to avoid conflicts
    environment:
      - SPRING_PROFILES_ACTIVE=test
      - SPRING_DATASOURCE_URL=**************************************************
      - SPRING_DATASOURCE_USERNAME=animalia_test_user
      - SPRING_DATASOURCE_PASSWORD=test_password_456
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - animalia-test
    restart: unless-stopped
    profiles:
      - full-stack  # Only start when explicitly requested

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local

networks:
  animalia-test:
    driver: bridge
