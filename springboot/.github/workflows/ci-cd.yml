name: CI/CD Pipeline

on:
  pull_request:
    branches: [main, master]
  push:
    branches: [main, master]
  workflow_dispatch:

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest
    timeout-minutes: 12
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: animalia
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_INITDB_ARGS: "--auth-host=trust"
        ports:
          - 5432:5432
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 5s
          --health-timeout 3s
          --health-retries 3
          --tmpfs /var/lib/postgresql/data:rw,noexec,nosuid,size=512m
    env:
      SPRING_PROFILES_ACTIVE: ci
      POSTGRES_URL: *****************************************
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      SPRING_FLYWAY_ENABLED: false
      SPRING_JPA_HIBERNATE_DDL_AUTO: create-drop
      # Gradle optimizations
      GRADLE_OPTS: "-Dorg.gradle.daemon=false -Dorg.gradle.parallel=true -Dorg.gradle.workers.max=2 -Xmx3g"
      # CI flag for conditional optimizations
      CI: true
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'
          cache: gradle

      - name: Setup Gradle
        run: |
          chmod +x ./gradlew
          # Quick validation that gradlew exists and is executable
          ./gradlew --version --no-daemon

      - name: Kotlin lint (fast check only)
        run: |
          echo "🔧 Running fast Kotlin lint check..."
          # Only run on changed files in CI for speed
          ./gradlew ktlintCheck --no-daemon --parallel || echo "⚠️ Some lint issues found but continuing build"
          echo "✅ Kotlin lint step completed"
        continue-on-error: true

      - name: Run fast tests only
        run: |
          echo "🧪 Running fast unit tests only (skipping slow integration tests)..."
          ./gradlew test --no-daemon --parallel --max-workers=2 --build-cache \
            -Dtest.exclude.patterns="**/*IntegrationTest*,**/*Integration*,**/*IT*"
        timeout-minutes: 4

      - name: Run integration tests (main branch only)
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
        run: |
          echo "🧪 Running integration tests..."
          ./gradlew test --no-daemon --parallel --max-workers=1 --build-cache \
            --tests="*IntegrationTest" --tests="*Integration*" --tests="*IT"
        timeout-minutes: 8
        continue-on-error: true

      - name: Code coverage verification (main branch only)
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
        run: ./gradlew jacocoTestReport jacocoTestCoverageVerification --no-daemon

      - name: Build application
        run: ./gradlew build -x test --no-daemon --parallel --build-cache

      - name: Dependency vulnerability scan
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: dependency-check/Dependency-Check_Action@1.1.0
        continue-on-error: true
        with:
          project: 'animalia-backend'
          path: '.'
          format: 'HTML'
          out: 'build/reports'

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: build/test-results/

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports
          path: build/reports/jacoco/

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        timeout-minutes: 10
        with:
          context: .
          push: false
          tags: animalia:ci
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64

  deploy:
    name: Deploy to Render
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    steps:
      - name: Trigger Render deploy
        env:
          RENDER_SERVICE_ID: ${{ secrets.RENDER_SERVICE_ID }}
          RENDER_API_KEY: ${{ secrets.RENDER_API_KEY }}
        run: |
          curl -X POST -H "Authorization: Bearer $RENDER_API_KEY" "https://api.render.com/v1/services/$RENDER_SERVICE_ID/deploys"
