# Animalia Production Environment Variables
# Copy these values to Render Environment Variables section

# Application Profile
SPRING_PROFILES_ACTIVE=prod

# Server Configuration
PORT=8080

# Database Configuration (using your existing working database)
DATABASE_URL=****************************************************************************************
DATABASE_USERNAME=animalia_lbzo_user
DATABASE_PASSWORD=U3Clr9xn3TbnhcNHIeuRzoXvIn4CWbGd

# Database Connection Pool Settings
DB_POOL_SIZE=15
DB_POOL_MIN_IDLE=5
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000
DB_LEAK_DETECTION=60000

# Security Configuration (using your existing working JWT secret)
JWT_SECRET=animaliaGroomingSecretKey2024ForJwtTokenGenerationAndValidationWithExtraLengthToMeetHS512Requirements
JWT_EXPIRATION=86400000

# Firebase Configuration (using your existing working Firebase setup)
FIREBASE_PROJECT_ID=animalia-de0f1
FIREBASE_DATABASE_URL=https://animalia-grooming-default-rtdb.europe-west1.firebasedatabase.app
FIREBASE_STORAGE_BUCKET=animalia-grooming.appspot.com

# Logging Configuration
SPRING_LOG_LEVEL=INFO
APP_LOG_LEVEL=INFO
HIBERNATE_SQL_LOG_LEVEL=WARN
HIBERNATE_BINDER_LOG_LEVEL=WARN
SECURITY_LOG_LEVEL=INFO
FIREBASE_LOG_LEVEL=INFO

# Health Check Configuration
HEALTH_SHOW_DETAILS=never

# CORS Configuration (update with your actual frontend domains)
ALLOWED_ORIGINS=https://animalia-app.com,https://www.animalia-app.com

# JVM Configuration
JAVA_OPTS=-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:+UseStringDeduplication

# Application Version (optional)
APP_VERSION=1.0.0

# Flyway DISABLED - Using Hibernate for schema management
SPRING_FLYWAY_ENABLED=false

# JPA/Hibernate Configuration - Let Hibernate manage schema
SPRING_JPA_HIBERNATE_DDL_AUTO=update
SPRING_JPA_PROPERTIES_HIBERNATE_HBM2DDL_AUTO=update
SPRING_JPA_PROPERTIES_HIBERNATE_TEMP_USE_JDBC_METADATA_DEFAULTS=false
SPRING_JPA_PROPERTIES_HIBERNATE_HBM2DDL_AUTO=update
SPRING_JPA_PROPERTIES_HIBERNATE_TEMP_USE_JDBC_METADATA_DEFAULTS=false

# Cloudinary configuration
CLOUDINARY_CLOUD_NAME=datjgpnel
CLOUDINARY_API_KEY=616156997515572
CLOUDINARY_API_SECRET=g6BReXQ1_jxqJ-whjWMGN_CATT0
